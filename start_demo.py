#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
海康威视NVR录像回放演示程序启动脚本

使用方法:
python start_demo.py
"""

import os
import sys
import time
import webbrowser
import subprocess
import threading
from pathlib import Path

def print_banner():
    """打印启动横幅"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║        🎬 海康威视NVR录像回放演示程序 🎬                      ║
    ║                                                              ║
    ║        基于ONVIF协议的NVR录像查询与回放功能                   ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def check_dependencies():
    """检查依赖项"""
    print("🔍 检查依赖项...")
    
    required_packages = [
        'fastapi',
        'onvif',
        'loguru',
        'pydantic',
        'uvicorn',
        'cachetools',
        'requests'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"  ✅ {package}")
        except ImportError as e:
            print(f"  ❌ {package} - 未安装")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n❌ 缺少依赖包: {', '.join(missing_packages)}")
        print("请运行以下命令安装:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    print("✅ 所有依赖项检查通过!")
    return True

def start_backend_server():
    """启动后端服务"""
    print("\n🚀 启动后端服务...")
    
    backend_path = Path("backend_opt")
    if not backend_path.exists():
        print("❌ 找不到backend_opt目录")
        return None
    
    # 切换到backend_opt目录
    os.chdir(backend_path)
    
    try:
        # 启动FastAPI服务
        process = subprocess.Popen([
            sys.executable, "application_opt.py"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        
        print("✅ 后端服务启动中...")
        print("📡 服务地址: http://localhost:6400")
        
        return process
        
    except Exception as e:
        print(f"❌ 启动后端服务失败: {e}")
        return None

def wait_for_server(url="http://localhost:6400/ping", timeout=30):
    """等待服务器启动"""
    print(f"\n⏳ 等待服务器启动 (最多等待{timeout}秒)...")
    
    import requests
    
    start_time = time.time()
    while time.time() - start_time < timeout:
        try:
            response = requests.get(url, timeout=2)
            if response.status_code == 200:
                print("✅ 服务器启动成功!")
                return True
        except:
            pass
        
        print(".", end="", flush=True)
        time.sleep(1)
    
    print(f"\n❌ 服务器启动超时 ({timeout}秒)")
    return False

def open_web_interface():
    """打开Web界面"""
    print("\n🌐 打开Web界面...")
    
    # 回到项目根目录
    os.chdir("..")
    
    web_demo_path = Path("web_demo/index.html")
    if not web_demo_path.exists():
        print("❌ 找不到web_demo/index.html文件")
        return False
    
    # 获取绝对路径
    html_file = web_demo_path.absolute()
    
    try:
        # 在默认浏览器中打开
        webbrowser.open(f"file://{html_file}")
        print(f"✅ Web界面已在浏览器中打开")
        print(f"📄 文件路径: {html_file}")
        return True
    except Exception as e:
        print(f"❌ 打开Web界面失败: {e}")
        print(f"请手动在浏览器中打开: file://{html_file}")
        return False

def start_local_http_server():
    """启动本地HTTP服务器"""
    print("\n🌐 启动本地HTTP服务器...")
    
    web_demo_path = Path("web_demo")
    if not web_demo_path.exists():
        print("❌ 找不到web_demo目录")
        return None
    
    os.chdir(web_demo_path)
    
    try:
        # 启动Python内置HTTP服务器
        process = subprocess.Popen([
            sys.executable, "-m", "http.server", "8080"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        
        print("✅ HTTP服务器启动成功!")
        print("🌐 Web界面地址: http://localhost:8080")
        
        return process
        
    except Exception as e:
        print(f"❌ 启动HTTP服务器失败: {e}")
        return None

def show_usage_info():
    """显示使用说明"""
    info = """
    📖 使用说明:
    
    1. 设备配置:
       - IP地址: ***************
       - 用户名: admin
       - 密码: Qqwe1234
       - 通道: 1-4
    
    2. 功能操作:
       - 点击"连接设备"建立连接
       - 选择日期并点击"查询录像"
       - 在录像列表中选择文件播放
       - 使用播放控制按钮控制回放
    
    3. 故障排除:
       - 确保设备网络连接正常
       - 检查ONVIF服务是否启用
       - 确认用户权限足够
    
    4. 停止服务:
       - 按 Ctrl+C 停止后端服务
       - 关闭浏览器标签页
    """
    print(info)

def monitor_processes(backend_process, http_process=None):
    """监控进程状态"""
    print("\n🔄 服务监控中... (按 Ctrl+C 停止)")
    
    try:
        while True:
            # 检查后端进程
            if backend_process and backend_process.poll() is not None:
                print("\n❌ 后端服务意外停止")
                break
            
            # 检查HTTP服务进程
            if http_process and http_process.poll() is not None:
                print("\n❌ HTTP服务意外停止")
                break
            
            time.sleep(1)
            
    except KeyboardInterrupt:
        print("\n\n🛑 正在停止服务...")
        
        if backend_process:
            backend_process.terminate()
            print("✅ 后端服务已停止")
        
        if http_process:
            http_process.terminate()
            print("✅ HTTP服务已停止")
        
        print("👋 演示程序已退出")

def main():
    """主函数"""
    print_banner()
    
    # 检查依赖项
    if not check_dependencies():
        return
    
    # 启动后端服务
    backend_process = start_backend_server()
    if not backend_process:
        return
    
    # 等待服务器启动
    if not wait_for_server():
        if backend_process:
            backend_process.terminate()
        return
    
    # 选择Web界面访问方式
    print("\n🌐 选择Web界面访问方式:")
    print("1. 直接打开HTML文件 (推荐)")
    print("2. 启动本地HTTP服务器")
    
    choice = input("请选择 (1/2, 默认1): ").strip() or "1"
    
    http_process = None
    
    if choice == "2":
        # 启动HTTP服务器
        http_process = start_local_http_server()
        if http_process:
            time.sleep(2)  # 等待HTTP服务器启动
            webbrowser.open("http://localhost:8080")
    else:
        # 直接打开HTML文件
        open_web_interface()
    
    # 显示使用说明
    show_usage_info()
    
    # 监控进程
    monitor_processes(backend_process, http_process)

if __name__ == "__main__":
    main()
