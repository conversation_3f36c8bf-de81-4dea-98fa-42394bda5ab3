# ONVIF录像查询实现指南

## 概述

ONVIF（Open Network Video Interface Forum）是一个开放的网络视频接口标准，用于IP摄像头和NVR设备的互操作性。本文档详细说明如何在ONVIF协议中实现录像查询功能。

## ONVIF录像查询的核心概念

### 1. 录像服务类型

ONVIF标准中涉及录像查询的主要服务：

- **Recording Search Service** - 录像搜索服务（推荐）
- **Replay Service** - 回放服务
- **Media Service** - 媒体服务
- **Analytics Service** - 分析服务（某些厂商扩展）

### 2. 录像查询流程

标准的ONVIF录像查询流程包括以下步骤：

```
1. 建立ONVIF连接
2. 获取Recording Search服务
3. 获取录像源信息 (GetRecordingSources)
4. 创建搜索范围和过滤条件
5. 执行录像搜索 (FindRecordings)
6. 获取搜索结果 (GetRecordingSearchResults)
7. 解析录像信息
8. 结束搜索 (EndSearch)
```

## 实际实现方法

### 方法1：使用Recording Search服务（推荐）

这是ONVIF标准推荐的录像查询方法：

```python
def search_recordings_with_search_service(self, channel, start_time, end_time):
    # 1. 创建Recording Search服务
    search_service = self.mycam.create_search_service()
    
    # 2. 获取录像源信息
    recording_sources = search_service.GetRecordingSources()
    
    # 3. 查找指定通道的录像源
    target_source = None
    for source in recording_sources:
        source_id = getattr(source, 'SourceId', '')
        if str(channel) in source_id:
            target_source = source
            break
    
    # 4. 创建搜索范围
    search_scope = {
        'IncludedSources': [target_source.SourceId],
        'IncludedRecordings': [],
        'RecordingInformationFilter': {
            'EarliestRecording': start_time.isoformat() + 'Z',
            'LatestRecording': end_time.isoformat() + 'Z'
        }
    }
    
    # 5. 执行录像搜索
    search_result = search_service.FindRecordings(
        Scope=search_scope,
        MaxMatches=100,
        KeepAliveTime='PT30S'
    )
    
    # 6. 获取搜索结果
    search_token = search_result.SearchToken
    get_result = search_service.GetRecordingSearchResults(
        SearchToken=search_token,
        MinResults=0,
        MaxResults=100,
        WaitTime='PT10S'
    )
    
    # 7. 处理搜索结果
    recordings = []
    if hasattr(get_result, 'RecordingInformation'):
        for recording_info in get_result.RecordingInformation:
            recording = self._parse_recording_info(recording_info, channel)
            recordings.append(recording)
    
    # 8. 结束搜索
    search_service.EndSearch(SearchToken=search_token)
    
    return recordings
```

### 方法2：使用Replay服务

当Recording Search服务不可用时的备用方法：

```python
def search_recordings_with_replay_service(self, channel, start_time, end_time):
    # 1. 创建Replay服务
    replay_service = self.mycam.create_replay_service()
    
    # 2. 获取Replay配置
    replay_config = replay_service.GetReplayConfiguration()
    
    # 3. 获取媒体配置文件
    profiles = self.media_service.GetProfiles()
    
    # 4. 根据通道匹配配置文件
    target_profile = None
    for profile in profiles:
        if str(channel) in getattr(profile, 'Name', ''):
            target_profile = profile
            break
    
    # 5. 构造回放URI
    # 注意：具体实现依赖于设备厂商
    
    return recordings
```

### 方法3：厂商特有实现（海康威视）

海康威视设备的特殊处理：

```python
def search_recordings_hikvision_specific(self, channel, start_time, end_time):
    # 1. 检测设备制造商
    device_service = self.mycam.create_devicemgmt_service()
    device_info = device_service.GetDeviceInformation()
    manufacturer = getattr(device_info, 'Manufacturer', '')
    
    if 'Hikvision' in manufacturer:
        # 2. 生成海康威视特有的RTSP URL
        start_str = start_time.strftime("%Y%m%dT%H%M%SZ")
        end_str = end_time.strftime("%Y%m%dT%H%M%SZ")
        track_id = channel * 100 + 1  # 主码流
        
        playback_url = (f"rtsp://{username}:{password}@{ip}:554/"
                       f"Streaming/tracks/{track_id}?"
                       f"starttime={start_str}&endtime={end_str}")
        
        # 3. 创建录像记录
        recording = {
            "id": f"hik_rec_{channel}_{start_time.strftime('%Y%m%d_%H%M%S')}",
            "channel": channel,
            "start_time": start_time.strftime("%Y-%m-%d %H:%M:%S"),
            "end_time": end_time.strftime("%Y-%m-%d %H:%M:%S"),
            "playback_url": playback_url
        }
        
        return [recording]
    
    return []
```

## 关键数据结构

### 录像信息结构

```python
recording = {
    "id": "唯一标识符",
    "channel": "通道号",
    "start_time": "开始时间 (YYYY-MM-DD HH:MM:SS)",
    "end_time": "结束时间 (YYYY-MM-DD HH:MM:SS)",
    "duration": "时长（秒）",
    "file_size": "文件大小",
    "recording_token": "ONVIF录像令牌",
    "source_id": "录像源ID",
    "file_path": "文件路径或URL",
    "playback_url": "回放URL"
}
```

### 搜索范围结构

```python
search_scope = {
    'IncludedSources': ["录像源ID列表"],
    'IncludedRecordings': ["录像ID列表"],
    'RecordingInformationFilter': {
        'EarliestRecording': "最早时间 (ISO格式)",
        'LatestRecording': "最晚时间 (ISO格式)"
    }
}
```

## 常见问题和解决方案

### 1. Recording Search服务不可用

**问题**: 某些设备不支持Recording Search服务
**解决方案**: 
- 使用Replay服务作为备用
- 使用厂商特有的接口
- 回退到模拟数据

### 2. 录像源匹配问题

**问题**: 无法正确匹配通道号和录像源
**解决方案**:
```python
# 多种匹配方式
if (str(channel) in source_id or 
    f"Channel{channel}" in source_id or 
    f"ch{channel}" in source_id.lower() or
    f"track{channel}" in source_id.lower()):
    target_source = source
```

### 3. 时间格式问题

**问题**: 不同设备对时间格式要求不同
**解决方案**:
```python
# ISO 8601格式
iso_time = datetime.isoformat() + 'Z'

# 海康威视格式
hik_time = datetime.strftime("%Y%m%dT%H%M%SZ")

# UTC时间处理
utc_time = datetime.utctimetuple()
```

### 4. 搜索令牌管理

**问题**: 搜索令牌需要正确管理和释放
**解决方案**:
```python
try:
    # 执行搜索
    search_result = search_service.FindRecordings(...)
    search_token = search_result.SearchToken
    
    # 获取结果
    get_result = search_service.GetRecordingSearchResults(...)
    
finally:
    # 确保释放搜索令牌
    try:
        search_service.EndSearch(SearchToken=search_token)
    except:
        pass
```

## 最佳实践

### 1. 错误处理

```python
def query_recordings_with_fallback(self, channel, start_time, end_time):
    try:
        # 方法1: Recording Search
        recordings = self._search_with_search_service(...)
        if recordings:
            return recordings
    except Exception as e:
        logger.warning(f"Recording Search失败: {e}")
    
    try:
        # 方法2: Replay Service
        recordings = self._search_with_replay_service(...)
        if recordings:
            return recordings
    except Exception as e:
        logger.warning(f"Replay Service失败: {e}")
    
    try:
        # 方法3: 厂商特有
        recordings = self._search_vendor_specific(...)
        if recordings:
            return recordings
    except Exception as e:
        logger.warning(f"厂商特有方法失败: {e}")
    
    # 最后回退到模拟数据
    return self._simulate_recording_query(...)
```

### 2. 性能优化

- 使用连接池管理ONVIF连接
- 缓存录像源信息
- 分页获取大量录像结果
- 异步处理长时间搜索

### 3. 兼容性处理

- 检测设备制造商和型号
- 根据设备特性选择合适的查询方法
- 提供配置选项支持不同设备

## 总结

ONVIF录像查询的实现需要考虑多种情况：

1. **标准ONVIF实现**: 使用Recording Search服务
2. **备用方案**: 使用Replay服务或其他ONVIF服务
3. **厂商特有**: 针对特定厂商的扩展实现
4. **容错机制**: 提供模拟数据作为最后的回退方案

通过多层次的实现策略，可以确保在各种设备和环境下都能正常工作。
