#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
海康威视NVR录像回放功能测试脚本

使用方法:
python test_nvr_demo.py
"""

import requests
import json
import time
from datetime import datetime, timedelta

from service.nvr_playback_service import NVRPlaybackService

# 测试配置
TEST_CONFIG = {
    "ip": "***********",
    "user": "admin", 
    "pwd": "Qqwe1234",
    "channel": 1
}

API_BASE_URL = "http://localhost:6400"

def test_device_connection():
    """测试设备连接"""
    print("=" * 50)
    print("测试设备连接...")
    
    url = f"{API_BASE_URL}/nvr/device/info"
    params = {
        "ip": TEST_CONFIG["ip"],
        "user": TEST_CONFIG["user"],
        "pwd": TEST_CONFIG["pwd"]
    }
    
    try:
        response = requests.get(url, params=params, timeout=10)
        result = response.json()
        
        if result["code"] == 0:
            print("✅ 设备连接成功!")
            print(f"设备信息: {json.dumps(result['data'], indent=2, ensure_ascii=False)}")
            return True
        else:
            print(f"❌ 设备连接失败: {result['msg']}")
            return False
            
    except Exception as e:
        print(f"❌ 连接异常: {e}")
        return False

def test_query_recordings():
    """测试录像查询"""
    print("=" * 50)
    print("测试录像查询...")
    
    # 查询今天的录像
    today = datetime.now().strftime("%Y-%m-%d")
    
    url = f"{API_BASE_URL}/nvr/recordings/query-by-date"
    params = {
        "ip": TEST_CONFIG["ip"],
        "user": TEST_CONFIG["user"],
        "pwd": TEST_CONFIG["pwd"],
        "channel": TEST_CONFIG["channel"],
        "date": today
    }
    
    try:
        response = requests.get(url, params=params, timeout=10)
        result = response.json()
        
        if result["code"] == 0:
            recordings = result["data"]
            print(f"✅ 录像查询成功! 找到 {len(recordings)} 个录像文件")
            
            for i, recording in enumerate(recordings[:3]):  # 只显示前3个
                print(f"录像 {i+1}:")
                print(f"  ID: {recording['id']}")
                print(f"  时间: {recording['start_time']} - {recording['end_time']}")
                print(f"  时长: {recording['duration']}秒")
                print(f"  大小: {recording['file_size']}")
                print()
            
            return recordings
        else:
            print(f"❌ 录像查询失败: {result['msg']}")
            return []
            
    except Exception as e:
        print(f"❌ 查询异常: {e}")
        return []

def test_playback_stream(recording):
    """测试获取回放流"""
    print("=" * 50)
    print("测试获取回放流...")
    
    url = f"{API_BASE_URL}/nvr/playback/stream"
    data = {
        "ip": TEST_CONFIG["ip"],
        "user": TEST_CONFIG["user"],
        "pwd": TEST_CONFIG["pwd"],
        "channel": recording["channel"],
        "start_time": recording["start_time"],
        "end_time": recording["end_time"]
    }
    
    try:
        response = requests.post(url, json=data, timeout=10)
        result = response.json()
        
        if result["code"] == 0:
            stream_url = result["data"]["stream_url"]
            print(f"✅ 获取回放流成功!")
            print(f"RTSP流地址: {stream_url}")
            return stream_url
        else:
            print(f"❌ 获取回放流失败: {result['msg']}")
            return None
            
    except Exception as e:
        print(f"❌ 获取流异常: {e}")
        return None

def test_playback_control():
    """测试播放控制"""
    print("=" * 50)
    print("测试播放控制...")
    
    url = f"{API_BASE_URL}/nvr/playback/control"
    
    # 测试播放控制命令
    actions = ["play", "pause", "stop"]
    
    for action in actions:
        data = {
            "ip": TEST_CONFIG["ip"],
            "user": TEST_CONFIG["user"],
            "pwd": TEST_CONFIG["pwd"],
            "channel": TEST_CONFIG["channel"],
            "action": action,
            "position": 0
        }
        
        try:
            response = requests.post(url, json=data, timeout=10)
            result = response.json()
            
            if result["code"] == 0:
                print(f"✅ {action} 控制成功: {result['data']['message']}")
            else:
                print(f"❌ {action} 控制失败: {result['msg']}")
                
        except Exception as e:
            print(f"❌ {action} 控制异常: {e}")
        
        time.sleep(1)  # 等待1秒

def test_api_endpoints():
    """测试所有API端点"""
    print("=" * 50)
    print("测试API端点可用性...")
    
    endpoints = [
        "/ping",
        "/nvr/device/info",
        "/nvr/recordings/query-by-date", 
        "/nvr/playback/stream",
        "/nvr/playback/control"
    ]
    
    for endpoint in endpoints:
        try:
            if endpoint == "/ping":
                response = requests.get(f"{API_BASE_URL}{endpoint}", timeout=5)
            else:
                # 其他端点需要参数，这里只测试是否可访问
                continue
                
            if response.status_code == 200:
                print(f"✅ {endpoint} - 可访问")
            else:
                print(f"❌ {endpoint} - HTTP {response.status_code}")
                
        except Exception as e:
            print(f"❌ {endpoint} - 异常: {e}")

def main():
    """主测试函数"""
    print("🚀 海康威视NVR录像回放功能测试")
    print(f"测试设备: {TEST_CONFIG['ip']}")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 1. 测试API端点
    test_api_endpoints()
    
    # 2. 测试设备连接
    if not test_device_connection():
        print("❌ 设备连接失败，跳过后续测试")
        return
    
    # 3. 测试录像查询
    recordings = test_query_recordings()
    if not recordings:
        print("❌ 没有找到录像文件，跳过播放测试")
        return
    
    # 4. 测试获取回放流
    first_recording = recordings[0]
    stream_url = test_playback_stream(first_recording)
    
    # 5. 测试播放控制
    test_playback_control()
    
    print("=" * 50)
    print("🎉 测试完成!")
    print()
    print("📋 测试总结:")
    print("1. 设备连接 - 测试NVR设备的ONVIF连接")
    print("2. 录像查询 - 测试按日期查询录像文件")
    print("3. 回放流获取 - 测试获取RTSP播放流地址")
    print("4. 播放控制 - 测试播放、暂停、停止控制")
    print()
    print("💡 使用建议:")
    print("1. 确保NVR设备网络连接正常")
    print("2. 确认ONVIF服务已启用")
    print("3. 检查用户权限是否足够")
    print("4. 使用专业RTSP播放器测试流地址")
    
    if stream_url:
        print(f"\n🎬 RTSP流地址: {stream_url}")
        print("可以使用VLC等播放器测试此地址")

def test_nvr_onvif_playback():
    service = NVRPlaybackService()
    service.connect()

    service.query_recordings()

if __name__ == "__main__":
    main()
