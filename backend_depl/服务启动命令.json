# 深度学习服务1
  inspection-backend-depl1:
    container_name: "inspection-backend-depl1"
    hostname: "inspection-backend-depl1"
    image: "${REGISTRY_URL}/${IMAGE_GROUP}/vision_depl:${ARCHITECTURE}${IMAGE_VERSION}"
    user: root
    privileged: true
    shm_size: "8g"
    ulimits:
      memlock: -1
    deploy:
      resources:
        reservations:
          cpus: "0.5"
          memory: 1024M
        limits:
          cpus: "10"
          memory: 4096M
    environment:
      - "TZ=Asia/Shanghai"
    ports:
      - "6301:6300/tcp"
    restart: "unless-stopped"
    devices:
      - "/dev/davinci0:/dev/davinci0"
      - "/dev/davinci_manager"
      - "/dev/devmm_svm"
      - "/dev/hisi_hdc"
    volumes:
      - "/usr/local/dcmi:/usr/local/dcmi"
      - "/usr/local/bin/npu-smi:/usr/local/bin/npu-smi"
      - "/usr/local/Ascend/driver/lib64/common:/usr/local/Ascend/driver/lib64/common"
      - "/usr/local/Ascend/driver/lib64/driver:/usr/local/Ascend/driver/lib64/driver"
      - "/etc/ascend_install.info:/etc/ascend_install.info"
      - "/usr/local/Ascend/driver/version.info:/usr/local/Ascend/driver/version.info"

      - "/etc/localtime:/etc/localtime:ro"
      - "${OUT_PROJECT_HOME}/workspace/backend-depl1/logs:/home/<USER>/logs/depl"
      - "${OUT_PROJECT_HOME}/workspace/algorithm-depl:/home/<USER>/depl_maths/algorithm/user"
      - "${OUT_PROJECT_HOME}/workspace/inference-model:/home/<USER>"
      # 映射临时文件目录，将临时文件的删除交给后端服务实现
      - "${OUT_PROJECT_HOME}/workspace/java/file-server/TEMP/depl:/tmp_file"

    logging:
      driver: "json-file"
      options:
        max-size: "1G"