from typing import Callable

from fastapi import FastAPI
from fastapi_cache import FastAPICache
from fastapi_cache.backends.inmemory import InMemoryBackend

from backend_common.config.logging import get_logger
from service.mathsMgmtService import preload_all_deeplearning_block, MathABMgmt


def app_startup_event(app: FastAPI) -> Callable:
    '''
    # 应用启动的事件接收---类似应用级别上的钩子函数
    :return:
    '''

    def startup() -> None:
        get_logger('depl')
        # os.system("pip install -r requirements.txt -i https://pypi.doubanio.com/simple")
        # TODO 为提高deeplearning model 的推理速度，所有type="DeepLearning"的算法都应当预先加载并创建好 predictor，调用时直接 predictor.run 即可
        preload_all_deeplearning_block()
        maths_mgmt = MathABMgmt({})
        # maths_mgmt.update_cls_gallery()

        FastAPICache.init(InMemoryBackend(), prefix="fastapi-cache")
        print("应用被启动了")

    return startup


def app_shutdown_event(app: FastAPI) -> Callable:
    '''
    #  应用关闭的事件接收---类似应用级别上的钩子函数
    :return:
    '''

    def shutdown() -> None:
        print("应用被关闭了")

    return shutdown
