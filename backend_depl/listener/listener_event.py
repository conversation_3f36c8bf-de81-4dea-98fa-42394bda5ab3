from typing import Callable
from loguru import logger
from fastapi import FastAPI
from fastapi_cache import FastAPICache
from fastapi_cache.backends.inmemory import InMemoryBackend

from backend_common.config.logging import get_logger
from service.maths_mgmt_service import preload_all_deeplearning_block
from depl_maths.realtime_stream.process import connect_redis, start_scheduler, scheduler, tasks
import requests, time

def app_startup_event(app: FastAPI) -> Callable:
    '''
    # 应用启动的事件接收---类似应用级别上的钩子函数
    :return:
    '''

    def startup() -> None:
        get_logger('depl')

        # REDIS_HOST = "************"
        connect_redis(app.state.REDIS_HOST)

        try:
            start_scheduler()
            logger.info("✅ 调度器已启动")
        except Exception as e:
            logger.error(f"❌ 调度器启动失败: {e}")

        preload_all_deeplearning_block()

        FastAPICache.init(InMemoryBackend(), prefix="fastapi-cache")
        logger.info("[app_startup] 应用被启动了")

        java_host = app.state.REDIS_HOST
        java_port = "9090"
        java_service_url = f"http://{java_host}:{java_port}/python-service/start-all-task"

        logger.info(f"🔔 等待 Java 服务就绪，准备通知: {java_service_url}")
        logger.info("🔁 将使用 GET 方法无限重试，直到成功调用 Java 接口...")

        # 重试配置
        retry_delay = 3  # 每次重试间隔 3 秒

        while True:
            try:
                response = requests.get(java_service_url, timeout=10)

                # 先检查 HTTP 状态码
                if response.status_code != 200:
                    logger.warning(
                        f"⚠️ HTTP 请求失败 | 状态码: {response.status_code} | 响应: {response.text[:500]}"
                    )
                else:
                    # 尝试解析 JSON
                    try:
                        resp_json = response.json()
                    except Exception as e:
                        logger.warning(f"⚠️ 响应不是有效的 JSON 格式 | 错误: {e} | 原始响应: {response.text}")
                        resp_json = {"code": -1, "msg": "Invalid JSON response"}

                    # 明确检查业务成功码
                    if resp_json.get("code") == 0:
                        logger.info(f"✅ 成功调用 Java 服务 (GET)！响应: {resp_json}")
                        break  # ✅ 成功，跳出循环
                    else:
                        error_msg = resp_json.get("msg", "Unknown error")
                        logger.warning(f"⚠️ Java 服务返回失败 | code: {resp_json.get('code')} | msg: {error_msg}")
                # 如果未 break，说明失败，进入重试
                logger.info(f"🔁 {retry_delay} 秒后重试...")
                time.sleep(retry_delay)

            except requests.exceptions.ConnectionError:
                logger.error(f"❌ 无法连接到 Java 服务: {java_service_url}（服务可能未启动或网络问题）")
                logger.info(f"🔁 {retry_delay} 秒后重试...")
                time.sleep(retry_delay)

            except requests.exceptions.Timeout:
                logger.warning(f"❌ 请求超时（10秒），Java 服务无响应")
                logger.info(f"🔁 {retry_delay} 秒后重试...")
                time.sleep(retry_delay)

            except requests.exceptions.RequestException as e:
                logger.error(f"❌ 发生未知网络错误: {type(e).__name__}: {e}")
                logger.info(f"🔁 {retry_delay} 秒后重试...")
                time.sleep(retry_delay)

    return startup


def app_shutdown_event(app: FastAPI) -> Callable:
    '''
    #  应用关闭的事件接收---类似应用级别上的钩子函数
    :return:
    '''

    def shutdown() -> None:

        logger.info("🛑 开始关闭服务...")
        try:
            for task in tasks.values():
                task["stop_event"].set()
            logger.info("✅ 所有任务已收到停止信号")
        except Exception as e:
            logger.error(f"❌ 停止任务时出错: {e}")

        try:
            if scheduler and scheduler.running:
                scheduler.shutdown()
                logger.info("✅ 调度器已关闭")
        except Exception as e:
            logger.error(f"❌ 调度器关闭失败: {e}")

        logger.info("👋 服务已关闭")

        logger.info("[app_shutdown] 应用被关闭了")
    
    return shutdown
