from typing import Callable

from loguru import logger
from fastapi import FastAPI
from fastapi_cache import FastAPICache
from fastapi_cache.backends.inmemory import InMemoryBackend

from backend_common.config.logging import get_logger
from service.maths_mgmt_service import preload_all_deeplearning_block


def app_startup_event(app: FastAPI) -> Callable:
    '''
    # 应用启动的事件接收---类似应用级别上的钩子函数
    :return:
    '''

    def startup() -> None:
        get_logger('depl')

        preload_all_deeplearning_block()

        FastAPICache.init(InMemoryBackend(), prefix="fastapi-cache")
        logger.info("[app_startup] 应用被启动了")

    return startup


def app_shutdown_event(app: FastAPI) -> Callable:
    '''
    #  应用关闭的事件接收---类似应用级别上的钩子函数
    :return:
    '''

    def shutdown() -> None:
        logger.info("[app_shutdown] 应用被关闭了")

    return shutdown
