# subscriber.py
import redis
from loguru import logger

# 连接 Redis（根据实际情况修改 host/port）
client = redis.Redis(host='************', port=6379, db=0, decode_responses=True)

def listen_to_channel(channel_name):
    print(f"正在监听频道: {channel_name} ...")
    
    # 创建发布/订阅对象
    pubsub = client.pubsub()
    pubsub.subscribe(channel_name)
    
    try:
        for message in pubsub.listen():
            if message['type'] == 'message':
                channel = message['channel']
                data = message['data']
                logger.info(f"[收到消息] 频道: {channel}, 内容: {data}")
                # print(f"[收到消息] 频道: {channel}, 内容: {data}")
            elif message['type'] == 'subscribe':
                logger.info(f"已订阅频道: {message['channel']}")
    except KeyboardInterrupt:
        logger.info("停止监听...")
    finally:
        pubsub.unsubscribe()
        client.close()

# 开始监听
if __name__ == "__main__":
    listen_to_channel('task_5b8d289b730748a28eb9937ac8ff22a9')