# file: ascend_rtsp_weekly_schedule_redis.py
import cv2
import numpy as np
import subprocess
import threading
import time
from loguru import logger
import asyncio
from queue import Queue, Full, Empty
from typing import Optional, Dict, List, Tuple
from fastapi import FastAPI, HTTPException, Body
from pydantic import BaseModel, field_validator
from uvicorn import Config, Server
# from numba import njit
import redis
import base64
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.jobstores.memory import MemoryJobStore
from apscheduler.executors.pool import ThreadPoolExecutor
import atexit
from contextlib import asynccontextmanager
from backend_common.exceptions.inspection_exception import InspectionException, AlgorithmCheckException, AlgorithmProcessException
from backend_common.maths.algorithm.base.algorithm_base import AlgorithmInput, AlgorithmBase
from backend_common.maths.algorithm.base.parameter_check import ParameterCheck
from service.maths_mgmt_service import DEEPLEARN_BLOCK_MAP, load_algorithm_super
from backend_common.constants.math_constants import ALGORITHM_DEPL_USER_DEFINE_PATH, ALGORITHM_DEPL_SYSTEM_INNER_PATH, ALGORITHM_DEPL_USER_DEFINE_BASE_OCR
import json
from datetime import datetime
from PIL import Image, ImageDraw, ImageFont
from backend_common.models.response.response_body import response


# ==================== Redis 客户端 ====================
REDIS_HOST = "************"
REDIS_PORT = 6379
REDIS_CHANNEL = "danger_alert"
redis_client = None

# ==================== 全局模型路径 ====================
MODEL_PATH = '/home/<USER>/yolo11s-seg_input640_310B1.om'

# ==================== APScheduler 调度器 ====================
scheduler = BackgroundScheduler(
    jobstores={'default': MemoryJobStore()},
    executors={'default': ThreadPoolExecutor(10)},
    timezone='UTC',
    daemon=True
)

# 确保退出时关闭调度器
atexit.register(lambda: scheduler.shutdown() if scheduler.running else None)

# ==================== 全局任务存储 ====================
tasks: Dict[str, Dict] = {}
yolo_model = None

def connect_redis(REDIS_HOST):
    global redis_client
    try:
        redis_client = redis.StrictRedis(host=REDIS_HOST, port=6379, decode_responses=True)
        redis_client.ping()
        logger.info("✅ Redis 连接成功")
    except Exception as e:
        logger.error(f"❌ Redis 连接失败: {e}")

def start_scheduler():
    def run_scheduler():
        try:
            scheduler.start()
        except Exception as e:
            logger.error(f"❌ APScheduler 启动失败: {e}")
    global scheduler
    thread = threading.Thread(target=run_scheduler, daemon=True)
    thread.start()
    logger.info("✅ APScheduler 后台线程已启动")


def draw_box_string(img, RGB, string):
    """
    img: 用 cv2.imread 读取的图片 (BGR格式)
    RGB: 字体颜色 (可以是 (255, 255, 255) 这样的元组)
    string: 要显示的文字
    return: 绘制后的图片 (BGR格式)
    """
    # 获取图像高度
    h = img.shape[0]

    # BGR 转 RGB
    img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
    img_pil = Image.fromarray(img)
    draw = ImageDraw.Draw(img_pil)

    # 加载字体（确保路径正确）
    try:
        font = ImageFont.truetype("/home/<USER>/backend_common/utils/font/simsun.ttf", 30, encoding="utf-8")
    except IOError:
        # 字体加载失败时使用默认字体（仅支持英文）
        font = ImageFont.load_default()
        print("Warning: simsun.ttf not found, using default font (may not support Chinese).")

    # 文字位置：左下角附近 (10, h - 30)
    text_position = (10, h - 40)

    # 绘制黑色文字（如果传入了 RGB 参数，也可以用它作为颜色）
    # 注意：PIL 使用 RGB，OpenCV 是 BGR，这里 RGB 是 (R, G, B) 元组
    text_color = tuple(RGB)  # 例如：(255, 0, 0) 蓝色（在 OpenCV 中）

    draw.text(text_position, string, fill=text_color, font=font)

    # 转回 OpenCV 格式 (BGR)
    img_cv2 = cv2.cvtColor(np.array(img_pil), cv2.COLOR_RGB2BGR)
    return img_cv2

# ==================== Pydantic 模型定义 ====================    
class ScheduleItem(BaseModel):
    daysOfWeek: str      # 字符串格式，如 "1234567" 或 "135"，表示星期几
    startTime: str     # 格式: HH:MM:SS
    endTime: str       # 格式: HH:MM:SS

    @field_validator('daysOfWeek')
    def validate_days(cls, v):
        if not v:
            raise ValueError('days 不能为空')
        if not v.isdigit():
            raise ValueError('days 必须只包含数字')
        for char in v:
            d = int(char)
            if d not in range(1, 8):  # 只允许 1~7
                raise ValueError(f'无效的星期: {d}，应为 1-7（星期一到星期日）')
        # 可选：去重并排序，变成规范格式
        sorted_unique = ''.join(sorted(set(v)))
        return sorted_unique

    @field_validator('startTime', 'endTime')
    def validate_time_format(cls, v):
        try:
            h, m, s = map(int, v.split(':'))
            if not (0 <= h < 24 and 0 <= m < 60 and 0 <= s < 60):
                raise ValueError()
        except Exception:
            raise ValueError('时间格式必须为 HH:MM:SS')
        return v

def list_all_jobs(scheduler) -> List[Dict]:
    """
    返回调度器中所有任务的 JSON 序列化格式列表
    """
    jobs = scheduler.get_jobs()
    
    if not jobs:
        logger.info("📭 没有正在运行的定时任务。")
        return []

    job_list = []
    for job in jobs:
        job_info = {
            "args": job.args,
            "id": job.id,
            "name": job.name.split('.')[-1] if job.name else "unknown",  # 提取函数名
            "func": str(job.func_ref),  # 如 'module:function_name'
            "trigger": str(job.trigger),  # 自动转为字符串（如 cron 表达式）
            "next_run_time": job.next_run_time.isoformat() if job.next_run_time else None,
            "kwargs": job.kwargs,
            "executor": job.executor,
            "jobstore": job._jobstore_alias or "default",
            "misfire_grace_time": job.misfire_grace_time,
            "max_instances": job.max_instances,
            "coalesce": job.coalesce,
        }
        job_list.append(job_info)

    # 可选：记录日志
    logger.info(f"📋 当前调度器中有 {len(job_list)} 个任务：")
    for job_info in job_list:
        if job_info['args']:
            logger.info(f"   参数(args): {job_info['args']}")
        logger.info(f"🔹 任务ID: {job_info['id']}")
        logger.info(f"   名称: {job_info['name']}")
        logger.info(f"   触发器: {job_info['trigger']}")
        logger.info(f"   下次执行时间: {job_info['next_run_time']}")
        if job_info['kwargs']:
            logger.info(f"   关键字参数(kwargs): {job_info['kwargs']}")
        logger.info("-" * 40)

    return job_list  # 返回标准 dict 列表，可被 json.dumps 序列化

class TaskConfig(BaseModel):
    taskId: str
    rtspSource: str
    rtspSink: str
    inferenceInterval: int = 4
    max_height: int = 1080
    algorithmParams: Optional[Dict] = {}
    presetDesc: str = ""
    offset_width: int = 0#467
    enabled: bool = True
    schedule: Optional[List[ScheduleItem]] = None  # 多个时间段规则
    alert_enabled: bool = True
    alert_cooldown: int = 1  # 秒

# ==================== 判断当前时间是否在调度范围内（支持跨天）====================
def is_current_time_in_schedule(schedule_list: List[Dict]) -> bool:
    if not schedule_list:
        return True

    now = time.localtime()
    current_weekday = now.tm_wday + 1  # Python: Mon=0 → 我们要 Mon=1
    current_seconds = now.tm_hour * 3600 + now.tm_min * 60 + now.tm_sec

    for item in schedule_list:
        if current_weekday not in [int(i) for i in list(item["daysOfWeek"])]:#item["daysOfWeek"]:
            continue

        start_hms, end_hms = item["startTime"], item["endTime"]
        try:
            h_s, m_s, s_s = map(int, start_hms.split(':'))
            h_e, m_e, s_e = map(int, end_hms.split(':'))
            start_seconds = h_s * 3600 + m_s * 60 + s_s
            end_seconds = h_e * 3600 + m_e * 60 + s_e
        except Exception as e:
            logger.warning(f"❌ 时间解析失败 {start_hms}/{end_hms}: {e}")
            continue

        if start_seconds <= end_seconds:
            if start_seconds <= current_seconds <= end_seconds:
                return True
        else:
            if current_seconds >= start_seconds or current_seconds <= end_seconds:
                return True
    return False

# ==================== 获取流信息 ====================
def get_stream_info(rtsp_source: str) -> Tuple[int, int, int, str]:
    # 先用 OpenCV 获取基本信息
    cap = cv2.VideoCapture(rtsp_source)
    if not cap.isOpened():
        raise IOError(f"无法打开 RTSP 流: {rtsp_source}")

    w = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    h = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    fps = max(1, min(int(cap.get(cv2.CAP_PROP_FPS)), 30))

    #  versucht mit OpenCV
    fourcc_val = int(cap.get(cv2.CAP_PROP_FOURCC))
    fourcc = "".join([chr((fourcc_val >> 8 * i) & 0xFF) for i in range(4)])
    cap.release()

    if fourcc in ['H264', 'h264', 'X264', 'x264']:
        return w, h, fps, 'h264'
    elif fourcc in ['H265', 'h265', 'X265', 'x265', 'HEVC', 'hevc']:
        return w, h, fps, 'h265'


    return w, h, fps, "h264"  # 默认 h264

# def _cleanup_process(process):
#     """安全终止并回收 ffmpeg 子进程"""
#     if process.poll() is None:  # 进程仍在运行
#         try:
#             process.terminate()  # 发送 SIGTERM
#             process.wait(timeout=3)
#         except subprocess.TimeoutExpired:
#             logger.warning("ffmpeg 未在3秒内退出，强制杀死")
#             process.kill()
#             process.wait()
#     else:
#         # 进程已退出，但仍需 wait() 防止僵尸
#         process.wait()


def _cleanup_process(process):
    if process is None:
        return
    try:
        if process.poll() is None:
            process.terminate()
            process.wait(timeout=3)
    except subprocess.TimeoutExpired:
        try:
            process.kill()
            process.wait()
        except:
            pass
    except:
        pass
    finally:
        try:
            process.stdout.close()
        except:
            pass
        try:
            process.stdin.close()
        except:
            pass

# ==================== 算法实例化 ====================
def algorithm_load(config: Dict):
    global DEEPLEARN_BLOCK_MAP
    inputs = AlgorithmInput()
    if not config.get("algorithmParams"):
        raise AlgorithmCheckException("算法参数缺失或不完整")
    
    method_code = config['algorithmParams'].get('code', None)
    user_or_system = config['algorithmParams'].get('userOrSystem', None)
    roi = config['algorithmParams'].get('roi', None)
    inputParam = config['algorithmParams'].get('inputParam', None)

    if not method_code or not user_or_system or not roi or not inputParam:
        raise AlgorithmCheckException("算法参数缺失或不完整")

    inputs['roi'] = roi
    inputs['inputParam'] = inputParam
    inputs['images'] = ['./depl_maths/realtime_stream/test.png']  # 图像路径留空，改为直接传图像数据

    # 输入参数校验
    ParameterCheck().do_check_param(inputs)
    algorithm_instance = DEEPLEARN_BLOCK_MAP.get(f"{method_code}")
    ocr = DEEPLEARN_BLOCK_MAP.get("ppocr").text_sys
    if algorithm_instance is None:
        if method_code in ALGORITHM_DEPL_USER_DEFINE_BASE_OCR.keys():
            algorithm_instance = load_algorithm_super(method_code, user_or_system, inputs, reload_model=ocr)
        else:
            algorithm_instance = load_algorithm_super(method_code, user_or_system, inputs)
        DEEPLEARN_BLOCK_MAP[f"{method_code}"] = algorithm_instance
    else:
        algorithm_instance.init(inputs)

    assert algorithm_instance is not None, "无法加载算法实例!!!"
    # result = algorithm_instance.perform()

    return algorithm_instance

# ==================== 拉流线程 ====================
def start_pull_stream(taskId: str):
    task = tasks[taskId]
    config = task["config"]
    stop_event = task["stop_event"]
    frame_queue = task["frame_queue"]

    # ✅ 使用缓存的流信息
    stream_info = task["stream_info"]
    in_w, in_h, in_fps, codec = stream_info["width"], stream_info["height"], stream_info["fps"], stream_info["codec"]
    if in_h <= config["max_height"]:
        out_h = in_h
        out_w = in_w
    else:
        out_h = config["max_height"]
        scale = out_h / in_h if in_h > out_h else 1.0
        out_w = int(in_w * scale)
    # out_fps = min(in_fps, 25)

    logger.info(f"[{taskId}] 📹 拉流: {config['rtspSource']} → {out_w}x{out_h}@{in_fps}")

    command = [
        "ffmpeg", "-hwaccel", "ascend", "-c:v", codec,
        "-resize", f"{out_w}x{out_h}", 
        "-i", config["rtspSource"],
        "-pix_fmt", "nv12", "-f", "rawvideo",
        # "-s", f"{out_w}x{out_h}",    
        "-r", str(in_fps), "-"
    ]
    process = subprocess.Popen(command, stdout=subprocess.PIPE, stderr=subprocess.DEVNULL, bufsize=10**6)
    frame_size = out_w * out_h * 3 // 2
    yuv_buffer = np.empty((out_h * 3 // 2, out_w), dtype=np.uint8)
    off = config["offset_width"]
    try:
        while not stop_event.is_set():
            raw = process.stdout.read(frame_size)
            if not raw or len(raw) != frame_size:
                logger.warning(f"{taskId}-{config['rtspSource']} 帧大小不符，实际读取 {len(raw)} 字节，预期 {frame_size} 字节。跳过此帧。")
                continue
                # raise EOFError("流中断")
            yuv_buffer[:] = np.frombuffer(raw, dtype=np.uint8).reshape(yuv_buffer.shape)
            bgr = cv2.cvtColor(yuv_buffer, cv2.COLOR_YUV2RGB_NV21)

            if off > 0 and out_w > off:
                fixed = np.empty_like(bgr)
                fixed[:, :off] = bgr[:, out_w - off:]
                fixed[:, off:] = bgr[:, :out_w - off]
                bgr = fixed

            try:
                frame_queue.put(bgr, block=False)
            except Full:
                pass
    finally:
        # ✅ 1. 通知 ffmpeg 优雅退出
        if process.poll() is None:  # 如果还在运行
            process.terminate()  # 发送 SIGTERM
            try:
                process.wait(timeout=3)  # 等待退出
            except subprocess.TimeoutExpired:
                logger.warning(f"[{taskId}-{config['rtspSource']}] ffmpeg 未在3秒内退出，强制杀死")
                process.kill()  # SIGKILL
                process.wait()  # 必须 wait

        # ✅ 2. 关闭 stdout 管道
        process.stdout.close()

        logger.info(f"[{taskId}] 🛑 拉流退出")

# ==================== 推理 + 绘图 + 报警线程 ====================
def process_frames(taskId: str, algorithm: AlgorithmBase):
    task = tasks[taskId]
    config = task["config"]
    frame_queue = task["frame_queue"]
    result_queue = task["result_queue"]
    stop_event = task["stop_event"]
    last_annotated = task["last_annotated_frame"]
    last_alert_time = 0
    
    # ✅ 使用缓存的流信息
    stream_info = task["stream_info"]
    in_w, in_h, in_fps = stream_info["width"], stream_info["height"], stream_info["fps"]

    if in_h <= config["max_height"]:
        out_h = in_h
        out_w = in_w
        scale = 1.0
    else:
        out_h = config["max_height"]
        scale = out_h / in_h if in_h > out_h else 1.0
        out_w = int(in_w * scale)

    logger.info(f"[{taskId}-{config['rtspSource']}] 🧠 推理线程启动")

    frame_count = 0

    try:
        while not stop_event.is_set():
            try:
                # 🔁 非阻塞或短超时获取帧，确保 stop_event 能及时中断
                frame = frame_queue.get(timeout=0.5)  # 改为 0.5 秒，更快响应 stop
                frame_count += 1
                # 是否需要执行推理
                if config["inferenceInterval"]==0:
                    do_infer = True
                else:
                    do_infer = (last_annotated is None) or (frame_count % config["inferenceInterval"] == 0)
                if do_infer:
                    start_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
                    draw_frame = frame.copy()
                    algorithm.origin_img = draw_frame
                    results = algorithm.perform()
                    draw_frame = algorithm.drow_figure(draw_frame)
                    if config["presetDesc"] != "":
                        draw_frame = draw_box_string(draw_frame, (255,255,255), config["presetDesc"])
                    last_annotated = draw_frame
                    # timestamp = datetime.now().strftime("%Y%m%d%H%M%S%f")[:-3]
                    # object_name = f"http://192.168.1.4:18010/TEMP/{timestamp}.JPEG"
                    if config["alert_enabled"] and results:
                        # now = time.time()
                        # if now - last_alert_time >= config["alert_cooldown"]:

                        _, buffer = cv2.imencode(".jpg", draw_frame, [cv2.IMWRITE_JPEG_QUALITY, 80])
                        img_base64 = base64.b64encode(buffer).decode('utf-8')  # 转为字符串
                        end_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
                        data = {
                            'isSuccess': results[0],
                            'output': results[1],
                            'osdInfo': results[2],
                            "startTime": start_time,
                            "endTime": end_time,
                            'resultImg': img_base64
                        }
                        try:
                            message = json.dumps(data, ensure_ascii=False) 
                            redis_client.publish(taskId, str(message))
                            data.pop('resultImg')  # 发布后删除大图字段
                            logger.info(f"[{taskId}-{config['rtspSource']}] 🔔 已发布消息至 Redis {data}")
                        except Exception as e:
                            logger.error(f"[{taskId}-{config['rtspSource']}] ❌ Redis 发布失败: {e}")

            # last_annotated = cv2.addWeighted(last_annotated, 1.0, danger_overlay, 1.0, 0)
                yuv = cv2.cvtColor(last_annotated, cv2.COLOR_BGR2YUV_I420)
                try:
                    result_queue.put(yuv, block=False)
                except Full:
                    pass
            except Empty:
                # 队列为空，继续循环，响应 stop_event
                continue
            except Exception as e:
                logger.error(f"[{taskId}-{config['rtspSource']}] ❌ 推理处理异常: {e}", exc_info=True)
    except Exception as e:
        logger.error(f"[{taskId}-{config['rtspSource']}] ❌ 推理线程未预期异常: {e}", exc_info=True)
    finally:
        # ✅ 确保退出日志和清理
        logger.info(f"[{taskId}-{config['rtspSource']}] 🛑 推理退出")

        # 可选：清理 algorithm 状态
        if hasattr(algorithm, 'cleanup'):
            try:
                algorithm.cleanup()
            except:
                pass

# ==================== 推流线程 ====================
def start_push_stream(taskId: str):
    task = tasks[taskId]
    config = task["config"]
    result_queue = task["result_queue"]
    stop_event = task["stop_event"]

    # ✅ 使用缓存的流信息
    stream_info = task["stream_info"]
    in_w, in_h, in_fps, codec = stream_info["width"], stream_info["height"], stream_info["fps"], stream_info["codec"]

    # out_h = config["max_height"]
    # scale = out_h / in_h if in_h > out_h else 1.0
    # out_w = int(in_w * scale)
    out_fps = min(in_fps, 20)

    if in_h <= config["max_height"]:
        out_h = in_h
        out_w = in_w
        scale = 1.0
    else:
        out_h = config["max_height"]
        scale = out_h / in_h if in_h > out_h else 1.0
        out_w = int(in_w * scale)

    command = [
        "ffmpeg","-y","-f","rawvideo","-pix_fmt","yuv420p",
        "-s", f"{out_w}x{out_h}","-r", str(out_fps),"-i","-",
        "-c:v", codec,"-frame_rate",str(out_fps),
        "-max_bit_rate","2000","-f","rtsp","-rtsp_transport","tcp",config["rtspSink"]
    ]

    process = None
    logger.info(f"[{taskId}] 📤 推流至: {config['rtspSink']}")

    try:
        if process is None or process.poll() is not None:
            # 如果已有进程但已退出，先回收
            if process is not None:
                logger.warning(f"[{taskId}-{config['rtspSource']}] ffmpeg 推流进程异常退出，正在重启")
                _cleanup_process(process)
        # if process is None:
            process = subprocess.Popen(command, stdin=subprocess.PIPE, stderr=subprocess.DEVNULL)
        while not stop_event.is_set():
            try:
                yuv = result_queue.get(timeout=1)
                process.stdin.write(yuv.tobytes())
                process.stdin.flush()  # 🔥 关键：及时刷新缓冲区
            except BrokenPipeError:
                logger.warning(f"[{taskId}-{config['rtspSource']}] ffmpeg stdin 断开，准备重启")
                _cleanup_process(process)
                process = None
            except Empty:
                continue
            except Exception as e:
                logger.error(f"[{taskId}-{config['rtspSource']}] ⚠️ 推流异常: {e}")
                _cleanup_process(process)
                process = None
                time.sleep(0.1)  # 避免频繁重试
    finally:
        # ✅ 退出时必须清理 process
        if process:
            _cleanup_process(process)
        logger.info(f"[{taskId}-{config['rtspSource']}] 🛑 推流退出")   

# ==================== 任务运行主循环 ====================
def run_task(taskId: str):
    if taskId not in tasks:
        logger.warning(f"[{taskId}] ❌ 任务不存在")
        return

    task = tasks[taskId]
    config = task["config"]

    if not config["enabled"]:
        logger.info(f"[{taskId}] ❌ 任务已禁用")
        return

    # ✅ 新增：在启动任何线程前，先获取一次流信息
    try:
        in_w, in_h, in_fps, encodeInfo = get_stream_info(config["rtspSource"])
        # ✅ 缓存到任务上下文中，避免重复获取
        codec = "h264_ascend" if encodeInfo=='h264' else "h265_ascend"
        task["stream_info"] = {
            "width": in_w,
            "height": in_h,
            "fps": in_fps,
            "codec": codec
        }
        logger.info(f"[{taskId}] 📡 获取流信息: {in_w}x{in_h}@{in_fps}")
    except Exception as e:
        logger.error(f"[{taskId}] ❌ 获取流信息失败: {e}，任务启动中止")
        tasks[taskId]["status"] = "stopping"
        tasks[taskId]["stop_event"].set()
        del tasks[taskId]
        remove_jobs_by_prefix(taskId)
        raise InspectionException(f"获取流信息失败: {e}")

    algorithm = None
    try:
        algorithm= algorithm_load(config)
        logger.info(f"[{taskId}] 🧩 算法实例化成功: {config['algorithmParams'].get('code')}")
    except Exception as e:
        logger.error(f"[{taskId}] ❌ 算法实例化失败: {e}，任务启动中止")
        tasks[taskId]["status"] = "stopping"
        tasks[taskId]["stop_event"].set()
        del tasks[taskId]
        remove_jobs_by_prefix(taskId)
        raise InspectionException(f"算法实例化失败: {e}")

    task["status"] = "running"
    task["stop_event"].clear()

    # ✅ 启动线程（此时流信息已准备就绪）
    threads = [
        threading.Thread(target=start_pull_stream, args=(taskId,), daemon=True),
        threading.Thread(target=process_frames, args=(taskId, algorithm), daemon=True),
        threading.Thread(target=start_push_stream, args=(taskId,), daemon=True),
    ]
    task["threads"] = threads
    for t in threads:
        t.start()
    logger.info(f"[{taskId}, {task}] ✅ 任务已启动")

# ==================== 调度任务管理 ====================
def remove_jobs_by_prefix(taskId):
    if scheduler:
        # 获取所有 job
        jobs = scheduler.get_jobs()
        prefix = f"task_{taskId}_"

        for job in jobs:
            if job.id.startswith(prefix):  # 匹配 task_{taskId}_start_x 或 stop_x
                scheduler.remove_job(job.id)
                logger.info(f"[{taskId}] 🗑️ 已移除调度任务: {job}")

def reschedule_task(taskId: str):
    """为指定任务创建或更新调度任务，并检查是否需要立即启动"""
    remove_jobs_by_prefix(taskId)

    if taskId not in tasks:
        return

    config = tasks[taskId]["config"]
    schedule = config["schedule"]
    if not schedule or not config["enabled"]:
        return

    for idx, item in enumerate(schedule):
        job_id_start = f"task_{taskId}_start_{idx}"
        job_id_stop = f"task_{taskId}_stop_{idx}"

        days = item["daysOfWeek"]
        start_time = item["startTime"]
        end_time = item["endTime"]

        h_s, m_s, s_s = map(int, start_time.split(':'))
        h_e, m_e, s_e = map(int, end_time.split(':'))

        scheduler.add_job(
            func=start_single_task_wrapper,
            args=[taskId],
            trigger="cron",
            # day_of_week=','.join(str(d-1) for d in days),
            day_of_week=','.join(str(int(d) - 1) for d in days),
            hour=h_s, minute=m_s, second=s_s,
            id=job_id_start,
            replace_existing=True,
            # timezone="Asia/Shanghai"
            timezone='UTC'
        )

        scheduler.add_job(
            func=stop_single_task_wrapper,
            args=[taskId],
            trigger="cron",
            # day_of_week=','.join(str(d-1) for d in days),
            day_of_week=','.join(str(int(d) - 1) for d in days),
            hour=h_e, minute=m_e, second=s_e,
            id=job_id_stop,
            replace_existing=True,
            # timezone="Asia/Shanghai"
            timezone='UTC'
        )

    logger.info(f"[{taskId}] 🕒 已设置调度任务: {schedule}")

    # ✅ 检查是否立即启动
    if is_current_time_in_schedule(schedule):
        current_status = tasks[taskId]["status"]
        if current_status != "running":
            logger.info(f"[{taskId}] ⏱️ 当前时间在调度范围内，立即启动任务")
            thread = threading.Thread(target=start_single_task_wrapper, args=(taskId,), daemon=True)
            thread.start()
        else:
            logger.info(f"[{taskId}] ✅ 任务已在运行中，跳过立即启动")
    else:
        logger.info(f"[{taskId}] ⏸️ 当前时间不在调度范围内，等待定时触发")

def start_single_task_wrapper(taskId: str):
    """包装函数，用于调度器调用"""
    logger.info(f"[{taskId}] 🕒 调度器触发启动检查")
    if taskId in tasks and tasks[taskId]["status"] != "running":
        logger.info(f"[{taskId}] 🕒 调度器触发启动")
        thread = threading.Thread(target=run_task, args=(taskId,), daemon=True)
        tasks[taskId]["thread"] = thread
        thread.start()

def stop_single_task_wrapper(taskId: str):
    """包装函数，用于调度器调用"""
    if taskId in tasks and tasks[taskId]["status"] == "running":
        logger.info(f"[{taskId}] 🕒 调度器触发停止")
        stop_single_task_sync(taskId)

def stop_single_task_sync(taskId: str):
    """同步停止任务（非 FastAPI 异步）"""
    tasks[taskId]["status"] = "stopping"
    tasks[taskId]["stop_event"].set()

# ==================== API 接口 ====================
@asynccontextmanager
async def lifespan(app: FastAPI):
    logger.info("🚀 服务正在启动...")

    connect_redis()

    try:
        start_scheduler()
        logger.info("✅ 调度器已启动")
    except Exception as e:
        logger.error(f"❌ 调度器启动失败: {e}")

    try:
        logger.info(f"⏱️ 重新调度所有任务...{len(tasks)} 个任务{tasks.keys()}")
        for tid in tasks:
            reschedule_task(tid)
        logger.info("✅ 所有任务已重新调度")
    except Exception as e:
        logger.error(f"❌ 任务重调度失败: {e}")

    yield

    logger.info("🛑 开始关闭服务...")
    try:
        for task in tasks.values():
            task["stop_event"].set()
        logger.info("✅ 所有任务已收到停止信号")
    except Exception as e:
        logger.error(f"❌ 停止任务时出错: {e}")

    try:
        if scheduler and scheduler.running:
            scheduler.shutdown()
            logger.info("✅ 调度器已关闭")
    except Exception as e:
        logger.error(f"❌ 调度器关闭失败: {e}")

    logger.info("👋 服务已关闭")

def add_sample_tasks():
    tasks["task_001"] = {
        "config": TaskConfig(
            rtspSource="rtsp://admin:Qqwe1234@192.168.1.65:554/Streaming/Channels/101",
            rtspSink="rtsp://************:8554/out_stream",
            inferenceInterval=4,
            schedule=[
                {"daysOfWeek": [1, 2, 3, 4, 5, 6, 7], "startTime": "00:00:00", "endTime": "11:00:00"},
            ],
            alert_enabled=True,
            alert_cooldown=1
        ).model_dump(),
        "status": "stopped",
        "thread": None,
        "stop_event": threading.Event(),
        "last_annotated_frame": None,
        "frame_queue": Queue(maxsize=5),
        "result_queue": Queue(maxsize=5)
    }

# ==================== FastAPI 初始化 ====================
app = FastAPI(title="Ascend 多路RTSP智能分析服务", version="3.1", lifespan=lifespan)


@app.get("/tasks_list", response_model=Dict)
async def list_tasks():
    return {"tasks": tasks}

@app.post("/tasks/start")
async def start_single_task_api(config: TaskConfig = Body(None)):
    taskId = config.taskId
    logger.info(f"[{taskId}] 🕒 API 请求启动任务")
    if taskId in tasks:
        if tasks[taskId]["status"] == "running":
            logger.info(f"[{taskId}] ✅ 任务已在运行中，停止旧任务后重新启动")
            await stop_single_task({"taskId": taskId})
        tasks[taskId]["config"] = config.model_dump()
        reschedule_task(taskId)
        return {"taskId": taskId, "status": "updated_and_started"}

    if taskId not in tasks and config is not None:
        tasks[taskId] = {
            "config": config.model_dump(),
            "status": "stopped",
            "thread": None,
            "stop_event": threading.Event(),
            "last_annotated_frame": None,
            "frame_queue": Queue(maxsize=5),
            "result_queue": Queue(maxsize=5)
        }
        logger.info(f"✅ 动态创建新任务: {taskId}")
        # thread = threading.Thread(target=run_task, args=(taskId,), daemon=True)
        # tasks[taskId]["thread"] = thread
        # thread.start()
        reschedule_task(taskId)
        return {"taskId": taskId, "status": "created_and_started"}

    raise HTTPException(status_code=400, detail="任务不存在且未提供配置")

@app.post("/tasks/stop")
async def stop_single_task(config: dict):
    taskId = config['taskId']

    if scheduler:
        # 获取所有 job
        jobs = scheduler.get_jobs()
        prefix = f"task_{taskId}_"

        for job in jobs:
            if job.id.startswith(prefix):  # 匹配 task_{taskId}_start_x 或 stop_x
                scheduler.remove_job(job.id)
                logger.info(f"[{taskId}] 🗑️ 已移除调度任务: {job}")

    if taskId not in tasks:
        raise HTTPException(status_code=404, detail="任务不存在")
    if tasks[taskId]["status"] != "running":
        del tasks[taskId]
        return {"status": "not_running"}
    tasks[taskId]["status"] = "stopping"
    tasks[taskId]["stop_event"].set()
    del tasks[taskId]
    return {"taskId": taskId, "status": "stopping"}

@app.post("/tasks/start_all")
async def start_all_tasks(tasks: List[TaskConfig] = Body([])):
    started = []
    for task in tasks:
        # if tasks[tid]["status"] != "running":
        #     reschedule_task(tid)
        await start_single_task_api(task)
        started.append(task)
    return {"status": "all_started", "tasks": started}

@app.post("/tasks/stop_all")
async def stop_all_tasks():
    for tid in tasks:
        if tasks[tid]["status"] == "running":
            tasks[tid]["status"] = "stopping"
            tasks[tid]["stop_event"].set()
    if scheduler and scheduler.running:
        scheduler.remove_all_jobs(jobstore='default')  # 删除 default 存储中的所有任务
        logger.info("🗑️ 已移除所有调度任务")
    tasks.clear()
    return {"status": "all_stopped"}

@app.put("/tasks/")
async def update_task_config(config: TaskConfig):
    taskId = config.taskId
    if taskId not in tasks:
        raise HTTPException(status_code=404, detail="任务不存在")
    old_status = tasks[taskId]["status"]
    if old_status == "running":
        await stop_single_task(taskId)
    tasks[taskId]["config"] = config.model_dump()
    reschedule_task(taskId)
    # if old_status == "running":
    #     await start_single_task_api(taskId)
    return {"status": "updated", "config": config}

@app.delete("/tasks")
async def delete_task(config: dict):
    taskId = config['taskId']
    if taskId not in tasks:
        raise HTTPException(status_code=404, detail="任务不存在")
    if tasks[taskId]["status"] == "running":
        await stop_single_task(taskId)
    try:
        scheduler.remove_jobs(job_id_prefix=f"task_{taskId}_")
    except:
        pass
    del tasks[taskId]
    return {"status": "deleted"}

@app.get("/status")
async def system_status():
    running = sum(1 for t in tasks.values() if t["status"] == "running")
    return {
        "total_tasks": len(tasks),
        "running_tasks": running,
        "redis_connected": bool(redis_client and redis_client.ping()),
        "model_loaded": yolo_model is not None,
        "scheduler_running": scheduler.running
    }

# ==================== 主函数 ====================
def main():
    logger.info("🚀 启动 Ascend 多路RTSP服务 (支持按星期几+时分秒调度 + APScheduler 自动启停)")
    config = Config(app=app, host="0.0.0.0", port=6300, loop="asyncio")
    server = Server(config=config)
    try:
        asyncio.run(server.serve())
    except KeyboardInterrupt:
        logger.info("👋 正在关闭...")
        for task in tasks.values():
            task["stop_event"].set()
        if scheduler.running:
            scheduler.shutdown()
        logger.info("✅ 退出完成")

if __name__ == "__main__":
    main()