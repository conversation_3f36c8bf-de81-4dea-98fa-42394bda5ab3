import cv2
import numpy as np
from io import BytesIO
from minio import Minio
from minio.error import S3Error
import logging
from datetime import datetime
import os

# 设置日志记录
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def is_jpeg_image(bgr_image):
    """检查图像是否已经是JPEG格式"""
    is_success, buffer = cv2.imencode(".jpg", bgr_image)
    return is_success, buffer

def upload_bgr_image_to_minio(client, bucket_name, object_name, bgr_image):
    """
    将BGR格式的图像上传到MinIO中。
    
    :param client: MinIO 客户端对象
    :param bucket_name: 存储桶名称
    :param object_name: 在 MinIO 中的文件名
    :param bgr_image: BGR 格式的图像 (numpy array)
    :return: 图片访问链接或 None（失败）
    """
    # 确保存储桶存在，如果不存在则创建
    try:
        if not client.bucket_exists(bucket_name):
            client.make_bucket(bucket_name)
            logger.info(f"Bucket '{bucket_name}' created.")
    except S3Error as err:
        logger.error(f"Failed to check or create bucket '{bucket_name}': {err}")
        return None

    # 检查图像是否已经是JPEG格式
    is_success, buffer = is_jpeg_image(bgr_image)
    if not is_success:
        logger.error("Failed to encode image into JPEG format.")
        return None
    
    # 如果已经是JPEG格式，跳过编码步骤
    byte_io = BytesIO(buffer)
    file_size = len(byte_io.getvalue())

    logger.info(f"Image size to upload: {file_size} bytes.")
    
    # 对于较大的图像，使用分块上传
    if file_size > 5 * 1024 * 1024:  # 5MB以上的文件进行分块上传
        try:
            result = client.put_object(
                bucket_name,
                object_name,
                data=byte_io,
                length=file_size,
                content_type='image/jpeg',
                part_size=5 * 1024 * 1024  # 每块最大5MB
            )
            logger.info(f"Image uploaded successfully as '{object_name}' using multipart upload.")
        except S3Error as err:
            logger.error(f"Error occurred while uploading the image: {err}")
            return None
    else:
        # 对于小于5MB的文件，直接上传
        try:
            client.put_object(
                bucket_name,
                object_name,
                data=byte_io,
                length=file_size,
                content_type='image/jpeg'
            )
            logger.info(f"Image uploaded successfully as '{object_name}'.")
        except S3Error as err:
            logger.error(f"Error occurred while uploading the image: {err}")
            return None

    # 返回完整的图片访问URL
    return f"http://***********:18010/{bucket_name}/{object_name}"

if __name__ == "__main__":
    # 初始化 MinIO 客户端
    client = Minio(
        "***********:18010",  # 替换为你的 MinIO 地址
        access_key="admin",    # 替换为你的 Access Key
        secret_key="Qqwe@1234",  # 替换为你的 Secret Key
        secure=False          # 如果使用 HTTPS，设为 True
    )

    # 创建一个 400x400 的随机 BGR 图像
    random_bgr_image = np.random.randint(0, 255, (400, 400, 3), dtype=np.uint8)

    # 使用时间戳命名文件
    timestamp = datetime.now().strftime("%Y%m%d%H%M%S%f")[:-3]
    object_name = f"{timestamp}.JPEG"

    # 上传图像
    url = upload_bgr_image_to_minio(client, "TEMP", object_name, random_bgr_image)
    
    if url:
        logger.info(f"Access URL: {url}")  # 输出完整的图片URL
    else:
        logger.error("Failed to upload the image.")
