## 泄漏检测识别算法V1.0.0

### 描述

 该算法用于对工厂环境中常见的漏水、漏油、烟雾泄漏等进行检测。

- 是否配置ROI：否

- 是否配置多图输入：否

### 输入参数

| 参数名     | 是否绘制 | 参数类型    | 默认值  | 是否必填 | 数据范围 | 精度   | 描述                           |
| ------- | ---- | ------- | ---- | ---- | ---- | ---- | ---------------------------- |
| ROI区域   | 否    | POLYGON | -    | 否    | -    | -    | 代表检测该多边形区域的目标                |
| 框面积占比阈值 | 否    | FLOAT   | 0.90 | 是    | 0-1  | 0.01 | 检测框面积与原图面积占比阈值,<br>高于该值的检测框会被过滤掉 |
| 框检测阈值   | 否    | FLOAT   | 0.20 | 是    | 0-1  | 0.01 | 选择阈值高于该值的框进行展示               |
| 类别阈值    | 否    | FLOAT   | 0.20 | 是    | 0-1  | 0.01 | 在框检测阈值的基础上对类别阈值二次过滤          |

### 输出参数

| 参数名    | 参数类型   | 描述                   |
| ------ | ------ | -------------------- |
| 泄漏检测结果 | STRING | 输出值为"检测到泄漏"或"未检测到泄漏" |
| 泄漏检测布尔值| BOOLEAN| "检测到泄漏": True, "未检测到泄漏": False|

### 结果展示

| 名称   | 描述                             |
| ---- | ------------------------------ |
| 目标框  | 矩形框，识别出的目标的位置坐标                |
| 类别名称 | 检测出目标的类别，例如水、烟雾等               |
| 置信度  | 浮点型值，范围0-1之间，精度0.01，代表识别目标的可信度 |