import os
import torch

from backend_common.exceptions.inspection_exception import AlgorithmProcessException
from backend_common.maths.algorithm.base.algorithm_base import AlgorithmBase, AlgorithmInput
from backend_common.utils.util import RectUtils
from depl_maths.algorithm.user.grounding_dino.grounding_dino_model import GroundingDINODetect
from depl_maths.algorithm.user.grounding_dino.input_parameter import PROMPT_CATEGORY, FILTER_CATEGORY
from loguru import logger


class GroundingDinoReader(AlgorithmBase):
    """
    泄漏检测算法
    """

    _name = "grounding_dino"

    def __init__(self, _inputs: AlgorithmInput):
        super(GroundingDinoReader, self).__init__(self.__class__._name)

        # 具体模型路径检查
        config_file = os.path.join(os.path.dirname(__file__), 'GroundingDINO_SwinB_cfg.py')
        checkpoint_path = "/home/<USER>/grounding_dino/groundingdino_swinb_cogcoor.pth"
        text_encoder_path = "/home/<USER>/grounding_dino/bert-base-uncased"
        if not os.path.exists(checkpoint_path):
            raise AlgorithmProcessException("泄漏检测模型文件不存在")
        if not os.path.exists(text_encoder_path):
            raise AlgorithmProcessException("泄漏检测文本编码器文件路径不存在")

        if torch.cuda.is_available():
            cpu_only = False
            logger.info("device use GPU infer")
        else:
            cpu_only = True
            logger.info("device use CPU infer")

        self.grounding_dino_model = GroundingDINODetect(config_file, checkpoint_path, cpu_only)

        if _inputs.__eq__(dict()):
            return

        self.init(_inputs)

    def init(self, _inputs: AlgorithmInput):
        # 算法实例参数
        self._local_var._inputs = _inputs
        # 一张图片
        self.origin_imgs = self._get_input_images()[0]
        # 输入参数获取
        input_param = self._get_input_param()

        self.prompt_category = PROMPT_CATEGORY
        self.filter_category = FILTER_CATEGORY
        self.box_threshold = input_param["box_threshold"]
        self.text_threshold = input_param["text_threshold"]
        self.box_area_threshold = input_param["box_area_threshold"]

        # self.iou_threshold = input_param["iou_threshold"]
        self.iou_threshold = 0.1

        input_roi = input_param["input_roi"]
        if input_roi is None:
            self.input_roi = None
        else:
            self.input_roi = [[coor['x'], coor['y']] for coor in input_roi]

        return self

    def _do_detect(self):

        results = self.grounding_dino_model.predict(self.origin_imgs,
                                                    self.prompt_category,
                                                    self.filter_category,
                                                    self.box_threshold,
                                                    self.text_threshold,
                                                    self.box_area_threshold,
                                                    self.iou_threshold,
                                                    self.input_roi)

        class_name, osdinfo = [], []
        for result in results:
            xmin, ymin, xmax, ymax, box_score, box_class = result
            class_name.append(box_class)
            coord = RectUtils.ltrb_to_4_points_dict([xmin, ymin], [xmax, ymax])
            osdinfo.append({"dataType": "SQUARE", "textObj": {"value": box_class}, "score": round(float(box_score), 2), "coords": coord})

        if len(class_name) > 0:
            output = {"grounding_dino": {"value": "检测到泄漏"}, "grounding_dinoBoolean": {"value": True}}
        else:
            output = {"grounding_dino": {"value": "未检测到泄漏"}, "grounding_dinoBoolean": {"value": False}}

        return True, output, osdinfo
