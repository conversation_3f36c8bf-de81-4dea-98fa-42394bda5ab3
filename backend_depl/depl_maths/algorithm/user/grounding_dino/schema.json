{"algorithmMetadata": {"name": "泄漏检测", "code": "grounding_dino", "modelCode": "grounding_dino", "version": "1.0.0", "description": "泄漏检测算法.V1", "type": "DeepLearning", "isBatchProcessing": false, "roiDrawType": "POLYGON", "roiRequired": false, "classification": "ENVIRONMENTAL_SAFETY"}, "input": [{"key": "input_roi", "label": "ROI区域", "dataType": "POLYGON", "constraints": {"required": false}, "drawToOsd": false}, {"key": "box_threshold", "label": "框检测阈值", "dataType": "FLOAT", "defaultValue": 0.2, "constraints": {"min": 0, "max": 1, "precision": 0.01, "required": true}, "drawToOsd": false}, {"key": "text_threshold", "label": "类别阈值", "dataType": "FLOAT", "defaultValue": 0.2, "constraints": {"min": 0, "max": 1, "precision": 0.01, "required": true}, "drawToOsd": false}, {"key": "box_area_threshold", "label": "框面积占比阈值", "dataType": "FLOAT", "defaultValue": 0.9, "constraints": {"min": 0, "max": 1, "precision": 0.01, "required": true}, "drawToOsd": false}], "output": [{"key": "grounding_dino", "label": "泄漏检测结果", "dataType": "STRING"}, {"key": "grounding_dinoBoolean", "label": "泄漏检测布尔值", "dataType": "BOOLEAN"}]}