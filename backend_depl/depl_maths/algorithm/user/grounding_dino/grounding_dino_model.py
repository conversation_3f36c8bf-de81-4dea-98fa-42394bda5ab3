import os

import numpy as np
import torch
import cv2
from PIL import Image

import groundingdino.datasets.transforms as T
from groundingdino.models import build_model
from groundingdino.util.slconfig import SLConfig
from groundingdino.util.utils import clean_state_dict, get_phrases_from_posmap

from depl_maths.algorithm.user.grounding_dino.grounding_post_process import boxes_pross, class_nms,\
    boxes_area_filter, class_filter, boxes_in_roi, get_top1_result
from depl_maths.algorithm.user.grounding_dino.input_parameter import NAME_CONVERSION

# torch.set_num_threads(cpu_num)


class GroundingDINODetect():
    def __init__(self, config_file, checkpoint_path, cpu_only):
        self.cpu_only = cpu_only
        self.model = self.load_model(config_file, checkpoint_path, cpu_only=self.cpu_only)

    def load_model(self, model_config_path, model_checkpoint_path, cpu_only=False):
        args = SLConfig.fromfile(model_config_path)
        args.device = "cuda" if not cpu_only else "cpu"
        model = build_model(args)
        checkpoint = torch.load(model_checkpoint_path, map_location="cpu")
        load_res = model.load_state_dict(clean_state_dict(checkpoint["model"]), strict=False)
        # print("grounding dino model load res:", load_res)
        _ = model.eval()
        return model

    def load_image(self, image):
        image_pil = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        image_pil = Image.fromarray(image_pil)

        transform = T.Compose(
            [
                T.RandomResize([800], max_size=1333),
                T.ToTensor(),
                T.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225]),
            ]
        )
        image, _ = transform(image_pil, None)  # 3, h, w
        return image_pil.size, image

    def get_grounding_output(self, model, image, caption, box_threshold,
                             text_threshold, with_logits=True, cpu_only=False, token_spans=None):

        # caption = '"person. chair. monitor.book."'
        caption = caption.replace('"', '')
        caption = caption.lower()
        caption = caption.strip()
        if not caption.endswith("."):
            caption = caption + "."
        device = "cuda" if not cpu_only else "cpu"
        model = model.to(device)
        image = image.to(device)
        with torch.no_grad():
            outputs = model(image[None], captions=[caption])
        logits = outputs["pred_logits"].sigmoid()[0]  # (nq, 256)
        boxes = outputs["pred_boxes"][0]  # (nq, 4)

        # filter output
        logits_filt = logits.cpu().clone()
        boxes_filt = boxes.cpu().clone()
        filt_mask = logits_filt.max(dim=1)[0] > box_threshold
        logits_filt = logits_filt[filt_mask]  # num_filt, 256
        boxes_filt = boxes_filt[filt_mask]  # num_filt, 4

        # get phrase
        tokenlizer = model.tokenizer
        tokenized = tokenlizer(caption)
        # build pred
        pred_phrases = []
        logit_list = []
        for logit, box in zip(logits_filt, boxes_filt):
            pred_phrase = get_phrases_from_posmap(logit > text_threshold, tokenized, tokenlizer)
            if with_logits:
                pred_phrases.append(pred_phrase)
                logit_list.append(f"{str(logit.max().item())[:4]}")
            else:
                pred_phrases.append(pred_phrase)

        logit_list = [float(i) for i in logit_list]
        return boxes_filt, pred_phrases, logit_list

    def predict(self, image, prompt_category, filter_category, box_threshold, text_threshold, box_area_threshold, iou_threshold, roi_coor):
        ori_image_HW, image = self.load_image(image)

        boxes_filt, pred_phrases, logits = self.get_grounding_output(self.model, image,
                                                                     prompt_category,
                                                                     box_threshold,
                                                                     text_threshold,
                                                                     cpu_only=self.cpu_only,
                                                                     token_spans=None)

        boxes_filt = boxes_pross(boxes_filt, ori_image_HW)

        grounding_output = []

        for i in range(len(boxes_filt)):
            box_info = boxes_filt[i]
            box_info.append(logits[i])
            box_info.append(pred_phrases[i])
            grounding_output.append(box_info)

        if grounding_output == []:
            return grounding_output

        # 过滤大面积框
        grounding_output = boxes_area_filter(grounding_output, ori_image_HW, box_area_threshold)

        # 过滤多提示词组合的框、过滤不显示的框
        grounding_output = class_filter(grounding_output, prompt_category, filter_category)

        # 执行多类别NMS
        # grounding_output = class_nms(grounding_output, iou_threshold, NAME_CONVERSION)

        # 置信度top1结果
        grounding_output = get_top1_result(grounding_output, NAME_CONVERSION)

        if roi_coor is not None:
            grounding_output = boxes_in_roi(grounding_output, roi_coor)

        return grounding_output
