#!/usr/bin/env python

"""算法包安装制作脚本 打包指定算法 - 支持代码加密"""

#  加密构建命令:
#  python setup.py build_py_encrypted bdist_wheel --dist-dir ../dist
#  python setup.py build_py_encrypted sdist --dist-dir ../dist --formats=gztar
#
#  标准构建命令:
#  python setup.py sdist --dist-dir ../dist --formats=zip --verbose
#  python setup.py sdist --dist-dir ../dabao  --formats=zip --verbose
#  pip install ..\dist\ppocr-1.0.0.tar.gz --target ..\target

from setuptools import setup
import sys
import os

# 添加system目录到路径以导入加密模块
current_dir = os.path.dirname(__file__)
system_dir = os.path.join(os.path.dirname(current_dir), 'system')
if system_dir not in sys.path:
    sys.path.insert(0, system_dir)

from build_encrypted import EncryptedBuildPy, EncryptCommand

# with open('README.rst') as readme_file:
#     readme = readme_file.read()
block_name = "ppocr"

# requirements which will be auto install when setup
install_requires = [
    'cryptography>=3.0.0',  # 加密解密功能
]
# requirements which declared, but won't be auto install when setup
extras_requires = []
# requirements which for test case
test_requires = []
# requirements which for setup.py itself
setup_requires = [
    'cryptography>=3.0.0',  # 构建时加密需要
]

test_requirements = ['pytest>=3', ]
setup(
    author="qinghaibo",
    author_email='<EMAIL>',
    python_requires='>=3.6',
    classifiers=[
        'Development Status :: 2 - Pre-Alpha',
        'Intended Audience :: Developers',
        'Natural Language :: English',
        'Programming Language :: Python :: 3',
        'Programming Language :: Python :: 3.6',
        'Programming Language :: Python :: 3.7',
        'Programming Language :: Python :: 3.8',
    ],
    description="算法块打包",
    install_requires=install_requires,
    extras_requires=extras_requires,
    test_requires=test_requires,
    setup_requires=setup_requires,
    # long_description=open("../../../README.md").read(),
    long_description="copyright © hollysys.com 2023",
    long_description_content_type='text/markdown',
    include_package_data=True,
    keywords=block_name,
    name=block_name,
    packages=[block_name],
    package_data={
        block_name: ['*.py', '*.pye', '*.json', '*.yml', '*.md'],
    },
    # 自定义构建命令
    cmdclass={
        'build_py_encrypted': EncryptedBuildPy,
        'encrypt': EncryptCommand,
    },
    tests_require=test_requirements,
    url='https://www.hollysys.com',
    version='1.0.0',
    zip_safe=False,
)
