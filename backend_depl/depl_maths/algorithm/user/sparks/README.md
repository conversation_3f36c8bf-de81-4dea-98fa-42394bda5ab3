## 冒火星识别算法

## 描述:

 该算法用于冒火星识别，包括生产过程中，因管理不善及操作不当而产生冒火星的现象。支持工厂、实验室等大多数场景下的冒火星的识别。
- 是否配置ROI：否
- 是否配置多图输入：否

### 输入参数

| 参数名     | 是否绘制 | 参数类型 | 默认值 | 是否必填 | 数据范围 | 精度 | 描述                                                         |
| ---------- | -------- | -------- | ------ | -------- | -------- | ---- | ------------------------------------------------------------ |
| ROI区域    | 是       | SQUARE   | -      | 否       | -        | -    | 矩形ROI区域                                                  |
| 框检测阈值 | 否       | FLOAT    | 0.5    | 是       | 0-1      | 0.01 | 检测框的置信度阈值，阈值越大，可信度越高，但检测出目标数量越少，可根据实际情况酌情调节 |
| iou阈值    | 否       | FLOAT    | 0.70   | 是       | 0-1      | 0.01 | 指的是算法识别的iou灵敏度阈值，阈值越小，重叠框越少，但检测出目标数量越少，可根据实际情况酌情调节 |

### 输出参数

| 参数名           | 参数类型 | 描述                                 |
| ---------------- | -------- | ------------------------------------ |
| 冒火星识别结果 | STRING   | 输出值为“检测到冒火星”或“未检测到冒火星” |
| 冒火星检测布尔值 |BOOLEAN | "检测到冒火星": True, "未检测到冒火星": False|

### 结果展示

| 名称     | 描述                                                      |
| -------- | --------------------------------------------------------- |
| 识别位置 | 矩形框，识别出的目标的位置坐标                            |
| 置信度   | 浮点型值，范围0-1之间，精度0.01，代表识别到的物体的可信度 |
| 类别名称 | 检测出目标的类别，例如水等                          |