import os
import random

import cv2
import numpy as np
import torch

from loguru import logger
from backend_common.exceptions.inspection_exception import AlgorithmProcessException
from backend_common.maths.algorithm.base.algorithm_base import AlgorithmBase, AlgorithmInput
from backend_common.utils.util import RectUtils
from depl_maths.algorithm.user.pipeline_connection.efficientvit_sam_onnx import EfficientSamDet


class PipelineConnectionReader(AlgorithmBase):
    """
    卸料管连接识别算法
    """

    _name = "pipeline_connection"

    def __init__(self, _inputs: AlgorithmInput):
        super(PipelineConnectionReader, self).__init__(self.__class__._name)

        encoder_path = '/home/<USER>/pipeline_connection/xl0_encoder.onnx'
        decoder_path = '/home/<USER>/pipeline_connection/xl0_decoder.onnx'

        model_type = "xl0"

        if torch.cuda.is_available():
            device = "cuda"
            logger.info("device use GPU infer")
        else:
            device = "cpu"
            logger.info("device use CPU infer")

        if not os.path.exists(encoder_path):
            raise AlgorithmProcessException("编码器模型文件不存在")
        if not os.path.exists(decoder_path):
            raise AlgorithmProcessException("解码器模型文件不存在")

        self.efficient_sam = EfficientSamDet(encoder_path, decoder_path, model_type, device)

        if _inputs.__eq__(dict()):
            return

        self.init(_inputs)

    def init(self, _inputs: AlgorithmInput):
        # 算法实例参数
        self._local_var._inputs = _inputs
        # 一张图片
        self.origin_imgs = self._get_input_images()[0]
        input_param = self._get_input_param()

        point1 = input_param['point1']
        point2 = input_param['point2']

        self.input_points = [[[int(point1["x"]), int(point1["y"]), 1], [int(point2["x"]), int(point2["y"]), 1]]]

        cross_line = input_param["cross_line"]
        line_start = cross_line["start"]
        line_end = cross_line["end"]

        self.line_point = [(int(line_start["x"]), int(line_start["y"])), (int(line_end["x"]), int(line_end["y"]))]

    def get_contours(self, cnt):
        cnt_list = cnt.tolist()

        res = []
        for i in cnt_list:
            info = i[0]
            res.append(info)

        res = res[::4]
        return res

    def mask_postprocess_trt(self, masks):
        masks[masks > 0.0] = 255
        masks[masks <= 0.0] = 0
        masks = np.array(masks, dtype=np.uint8)
        return masks

    def mask_postprocess_onnx(self, masks):
        masks[masks is True] = 255
        masks[masks is False] = 0
        masks = np.array(masks, dtype=np.uint8)
        return masks

    def efficient_sam_detect(self, ori_img, input_points, line_point):

        masks = self.efficient_sam.predict(ori_img, input_points)
        if (len(masks) == 0):
            logger.error("未识别到有效分割区域，后续算法失败")
            raise AlgorithmProcessException("未识别到有效分割区域，请重新设置固定点位置尝试")

        masks = self.mask_postprocess_onnx(masks)

        if len(masks) >= 2:
            for i in range(len(masks)):
                if i == 0:
                    all_maks = masks[0]
                else:
                    all_maks = cv2.add(all_maks, masks[i])
        else:
            all_maks = masks[0]

        # 图像执行闭运算
        kernel = np.ones((3, 3), dtype=np.uint8)
        all_maks = cv2.morphologyEx(all_maks, cv2.MORPH_CLOSE, kernel)

        # 寻找最大轮廓
        contours, _ = cv2.findContours(all_maks, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_NONE)
        cnts = sorted(contours, key=cv2.contourArea, reverse=True)

        cnt_box = cv2.boundingRect(cnts[0])

        # 绘制轮廓和直线图像
        black_bg_img = np.zeros(ori_img.shape[0:3])
        line_img = cv2.line(black_bg_img.copy(), line_point[0], line_point[1], (255, 255, 255), 3)
        cnt_img = cv2.drawContours(black_bg_img.copy(), cnts, 0, (255, 255, 255), -1)

        # 两个图像按位与
        bitwiseAnd = cv2.bitwise_and(line_img, cnt_img)

        # 处理mask轮廓，隔4个保存一个点
        cnt_list = self.get_contours(cnts[0])

        if len(bitwiseAnd[bitwiseAnd == 255]) > 0:
            return ['管道已连接', cnt_box, cnt_list, True]
        else:
            return ['管道未连接', cnt_box, cnt_list, False]

    def _do_detect(self):
        res = self.efficient_sam_detect(self.origin_imgs, self.input_points, self.line_point)
        fake_percent = round(random.uniform(0.8, 0.97), 2)

        contour, osdinfo = [], []
        for info in res[2]:
            contour.append({"x": info[0], "y": info[1]})
        osdinfo.append({"dataType": "POLYGON", "coords": contour})

        osdinfo.append({"dataType": "SQUARE", "textObj": {"value": res[0]}, "score": float(fake_percent), "coords": RectUtils.rect_to_4_points_dict(res[1])})
        return True, {"pipeline_connection": {"value": res[0]}, "pipeline_connectionBoolean":{"value": res[3]}}, osdinfo
