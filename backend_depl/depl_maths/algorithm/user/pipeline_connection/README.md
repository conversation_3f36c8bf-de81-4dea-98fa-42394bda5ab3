## 卸料管连接识别算法V1.0.0

### 描述

 该算法用于识别罐车的卸料管和管道是否连接

- 是否配置ROI：否

- 是否配置多图输入：否

### 输入参数

| 参数名      | 是否绘制 | 参数类型  | 默认值 | 是否必填 | 数据范围 | 精度  | 描述           |
| -------- | ---- | ----- | --- | ---- | ---- | --- | ------------ |
| 管道固定位置点1 | 是    | POINT | -   | 是    | -    | -   | 管道固定位置点      |
| 管道固定位置点2 | 是    | POINT | -   | 是    | -    | -   | 管道固定位置点      |
| 边界线      | 是    | LINE  | -   | 是    | -    | -   | 用于判断是否连接的参考线 |

### 输出参数

| 参数名    | 参数类型   | 描述                  |
| ------ | ------ | ------------------- |
| 管道连接结果 | STRING | 输出值为“管道已连接”或“管道未连接” |
| 管道连接结果布尔值 |BOOLEAN| "管道已连接": True, "管道未连接": False|

### 结果展示

| 名称     | 描述                            |
| ------ | ------------------------------ |
| 识别位置   | 矩形框，管道区域的位置坐标                |
| 识别结果   | 输出值为“管道已连接”或“管道未连接”            |
| 管道区域轮廓 | 绘制多边形轮廓                        |
| 置信度    | 浮点型值，范围0-1之间，精度0.01，代表识别目标的可信度 |