{"algorithmMetadata": {"name": "卸料管连接识别", "code": "pipeline_connection", "modelCode": "pipeline_connection", "version": "1.0.0", "description": "卸料管连接识别算法.V1", "type": "DeepLearning", "isBatchProcessing": false, "roiDrawType": "POLYGON", "roiRequired": false, "classification": "OTHERS"}, "input": [{"key": "point1", "label": "管道固定位置点1", "dataType": "POINT", "value": {"x": 100, "y": 100}, "constraints": {"required": true}, "drawToOsd": true}, {"key": "point2", "label": "管道固定位置点2", "dataType": "POINT", "value": {"x": 100, "y": 100}, "constraints": {"required": true}, "drawToOsd": true}, {"key": "cross_line", "label": "边界线", "dataType": "LINE", "value": {"start": {"x": 0, "y": 0}, "end": {"x": 0, "y": 0}}, "constraints": {"required": true}, "drawToOsd": true}], "output": [{"key": "pipeline_connection", "label": "管道连接结果", "dataType": "STRING"}, {"key": "pipeline_connectionBoolean", "label": "管道连接结果布尔值", "dataType": "BOOLEAN"}]}