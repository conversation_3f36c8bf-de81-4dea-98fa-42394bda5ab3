{"algorithmMetadata": {"name": "水汽泄漏检测", "code": "steam_leakage", "version": "1.0.0", "description": "水汽泄漏检测算法.V1", "type": "DeepLearning", "isBatchProcessing": false, "roiDrawType": "SQUARE", "roiRequired": false, "classification": "ENVIRONMENTAL_SAFETY"}, "input": [{"key": "input_roi", "label": "ROI区域", "dataType": "SQUARE", "constraints": {"required": false}, "drawToOsd": false}, {"key": "conf", "label": "框检测阈值", "dataType": "FLOAT", "defaultValue": 0.5, "constraints": {"min": 0, "max": 1, "precision": 0.01, "required": true}, "drawToOsd": false}, {"key": "iou", "label": "iou阈值", "dataType": "FLOAT", "defaultValue": 0.7, "constraints": {"min": 0, "max": 1, "precision": 0.01, "required": true}, "drawToOsd": false}], "output": [{"key": "steamLeakage", "label": "水汽泄漏检测结果", "dataType": "STRING"}, {"key": "steamLeakageBoolean", "label": "水汽泄漏检测布尔值", "dataType": "BOOLEAN"}]}