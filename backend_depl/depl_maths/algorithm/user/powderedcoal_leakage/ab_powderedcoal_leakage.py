import os

from backend_common.exceptions.inspection_exception import AlgorithmProcessException
from backend_common.maths.algorithm.base.algorithm_base import AlgorithmBase, AlgorithmInput
from backend_common.utils.util import RectUtils
from depl_maths.algorithm.system.paomaodilou.ab_paomaodilou import yoloModel
import torch

class PowderedcoalLeakageReader(AlgorithmBase):
    """
    煤粉泄漏识别算法
    """
    _name = "煤粉泄漏检测"

    def __init__(self, _inputs: AlgorithmInput, YoloModel: yoloModel = None):
        super(PowderedcoalLeakageReader, self).__init__(self.__class__._name)

        if YoloModel is not None:
            self.model = YoloModel
        else:
            print('YoloModel is None, create new model...')
            current_working_directory = os.getcwd()
            checkpoint_path = os.path.join(current_working_directory, 'depl_maths/algorithm/system/paomaodilou/inference_model/yolov8_m_32.pt')
            # checkpoint_path = os.path.join(os.path.dirname(__file__), 'inference_model/yolov8_m_32.pt')
            self.model = yoloModel(checkpoint_path)
        self.label = {0: '油污', 1: '煤粉', 2: '水渍', 3: '水汽', 4: '污渍', 5: '水', 6: '冒火星', 7: '阳光'}
        self.alarm_label = [1]
        if torch.cuda.is_available():
            self.device = '0'
        else:
            self.device = 'cpu'
        
        if _inputs.__eq__(dict()):
            return

        self.init(_inputs)

    def init(self, _inputs: AlgorithmInput):
        # 算法实例参数
        self._local_var._inputs = _inputs

        # 获取单张图像
        self.inputImages = self._get_input_images()[0]

        input_param = self._get_input_param()
        self.conf = input_param["conf"]
        self.iou = input_param["iou"]

        input_roi = input_param["input_roi"]
        if input_roi is None:
            self.input_roi = None
        else:
            self.input_roi = [[coor['x'], coor['y']] for coor in input_roi]

    def _do_detect(self):

        detect_results = self.model.detect(self.inputImages, self.conf, self.iou, self.input_roi, classes=self.alarm_label, device=self.device)

        osdinfo = []
        class_name = []
        for res in detect_results:
            [xmin, ymin, xmax, ymax], box_score, box_class = res

            coord = RectUtils.ltrb_to_4_points_dict([xmin, ymin], [xmax, ymax])
            class_name.append(box_class)

            osdinfo.append({"dataType": "SQUARE", "textObj": {"value": "煤粉"}, "score": round(float(box_score), 2), "coords": coord})

        if len(class_name) > 0:
            output = {"powderedcoalLeakage": {"value": "检测到煤粉泄漏"}, "powderedcoalLeakageBoolean":{"value": True}}
        else:
            output = {"powderedcoalLeakage": {"value": "未检测到煤粉泄漏"}, "powderedcoalLeakageBoolean":{"value": False}}

        return True, output, osdinfo
