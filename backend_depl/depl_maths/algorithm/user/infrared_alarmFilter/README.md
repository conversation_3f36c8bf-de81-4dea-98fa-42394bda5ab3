## 红外温度过滤报警识别算法

## 描述:

 该算法用于红外温度过滤报警识别，包括在厂区，料区进行红外温度过高报警，过滤车辆等影响因素。支持厂区大多数红外温度过滤报警识别。
- 是否配置ROI：否
- 是否配置多图输入：否

### 输入参数

| 参数名     | 是否绘制 | 参数类型 | 默认值 | 是否必填 | 数据范围 | 精度 | 描述                                                         |
| ---------- | -------- | -------- | ------ | -------- | -------- | ---- | ------------------------------------------------------------ |
| 红外通道 | 否     | CHANNEL_PIC | - | 是 |-| -| 下拉选项，选择此通道对应的红外通道 |
| 框检测阈值 | 否       | FLOAT    | 0.5    | 是       | 0-1      | 0.01 | 检测框的置信度阈值，阈值越大，可信度越高，但检测出目标数量越少，可根据实际情况酌情调节 |
| 识别类型  | 否    | SELECTOR | -    | 是    | 多选，支持1-6个选项 | -    | 需要检测的目标类别，支持多类别                   |
| 红外尺寸高度    | 否  | INTEAGER  | 230   | 是       | 0-5000      | 1 | 指的是红外图片在可见光图片中的尺寸高度，由可见光图片中心向两边计算 |
| 红外尺寸宽度    | 否  | INTEAGER  | 290   | 是       | 0-5000      | 1 | 指的是红外图片在可见光图片中的尺寸宽度，由可见光图片中心向两边计算 |

### 输出参数

| 参数名           | 参数类型 | 描述                                 |
| ---------------- | -------- | ------------------------------------ |
| 红外温度过滤结果 | STRING   | 输出值为"已过滤"、"未过滤"或"未检测到温度值" |
| 红外温度 |FLOAT | 具体红外相机的最大温度|

### 结果展示

| 名称     | 描述                                                      |
| -------- | --------------------------------------------------------- |
| 红外图像中车辆的轮廓   | mask，代表识别到的物体的轮廓 |
| 置信度 | 检测出目标的可信度，0-1之间                         |
| 过滤结果| 输出值为"已过滤"、"未过滤"或"未检测到温度值" |
| 最大温度| 具体红外相机的最大温度 |