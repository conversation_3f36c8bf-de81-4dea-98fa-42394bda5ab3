{"algorithmMetadata": {"name": "红外温度过滤报警", "code": "infrared_alarmFilter", "version": "1.0.0", "description": "红外温度过滤报警算法.V1", "type": "DeepLearning", "isBatchProcessing": false, "roiDrawType": "SQUARE", "roiRequired": false, "classification": "ENVIRONMENTAL_SAFETY"}, "input": [{"key": "common_pic", "label": "可见光图片", "dataType": "CHANNEL_PIC", "constraints": {"required": true}, "drawToOsd": false}, {"key": "conf", "label": "框检测阈值", "dataType": "FLOAT", "defaultValue": 0.5, "constraints": {"min": 0, "max": 1, "precision": 0.01, "required": true}, "drawToOsd": false}, {"key": "multi_select", "label": "识别类型", "dataType": "SELECTOR", "constraints": {"required": true, "maxLength": 6, "options": [{"key": 0, "label": "人"}, {"key": 1, "label": "自行车"}, {"key": 2, "label": "汽车"}, {"key": 3, "label": "摩托车"}, {"key": 5, "label": "公共汽车"}, {"key": 7, "label": "卡车"}]}, "drawToOsd": false}, {"key": "visible2red_h", "label": "红外尺寸高度", "dataType": "INTEGER", "defaultValue": 230, "constraints": {"min": 0, "max": 5000, "precision": 1, "required": true}, "drawToOsd": false}, {"key": "visible2red_w", "label": "红外尺寸宽度", "dataType": "INTEGER", "defaultValue": 290, "constraints": {"min": 0, "max": 5000, "precision": 1, "required": true}, "drawToOsd": false}], "output": [{"key": "infraredAlarmFilter", "label": "红外温度过滤结果", "dataType": "STRING"}, {"key": "temperature", "label": "红外温度", "dataType": "FLOAT"}]}