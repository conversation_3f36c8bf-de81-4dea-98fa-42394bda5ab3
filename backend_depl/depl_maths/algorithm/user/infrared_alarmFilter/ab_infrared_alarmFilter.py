import os

from backend_common.exceptions.inspection_exception import AlgorithmProcessException
from backend_common.maths.algorithm.base.algorithm_base import AlgorithmBase, AlgorithmInput
from backend_common.utils.util import RectUtils
from backend_common.utils.util import get_image
# from depl_maths.algorithm.system.ppocr.ab_ppocr import PpocrReader
import torch
from loguru import logger
# from ultralytics import YOLO
from depl_maths.algorithm.user.infrared_alarmFilter.mask_inference_om import yoloMask
from depl_maths.algorithm.system.ppocr.ocr_predictor import TextSystem
import numpy as np
from PIL import Image, ImageDraw, ImageFont
import cv2
import re
from backend_common.utils.MinioUtil import save_image, upload



SCHEMA_DICT = {0: '人', 1: '自行车', 2: '汽车', 3: '摩托车', 5: '公共汽车', 7: '卡车'}

class InfraredAlarmfilterReader(AlgorithmBase):
    """
    红外温度过滤报警算法
    """
    _name = "红外温度过滤报警"

    def __init__(self, _inputs: AlgorithmInput, OcrModel: TextSystem = None):
        super(InfraredAlarmfilterReader, self).__init__(self.__class__._name)

        # Create YOLO model 
        mask_path = '/home/<USER>/infrared_alarmFilter/yolo11s-seg_input640_310P3.om'
        if not os.path.exists(mask_path):
            logger.error(f"分割模型文件不存在: {mask_path}")
            raise AlgorithmProcessException(f"分割模型文件不存在")
        self.yoloModel = yoloMask('/home/<USER>/infrared_alarmFilter/yolo11s-seg_input640_310P3.om')
        # Initialize PaddleOCR
        if OcrModel is not None:
            self.ocr = OcrModel
        else:
            print('OcrModel is None, create new model...')
            current_working_directory = os.getcwd()
            det_model_dir = os.path.join(current_working_directory, 'depl_maths/algorithm/system/ppocr/inference_model/ch_PP-OCRv4_det_960_960_310P3.om')
            rec_model_dir = os.path.join(current_working_directory, 'depl_maths/algorithm/system/ppocr/inference_model/ch_PP-OCRv4_rec_48_3000_310P3.om')
            self.ocr = TextSystem(det_model_dir, rec_model_dir)

        if _inputs.__eq__(dict()):
            return

        self.init(_inputs)

    def init(self, _inputs: AlgorithmInput):
        # 算法实例参数
        self._local_var._inputs = _inputs

        # 获取图像
        self.origin_imgs = self._get_input_images()
        # 获取可见光图像
        self.visibleLightImg = self.origin_imgs[0]
        self.visible_h, self.visible_w = self.visibleLightImg.shape[:2]
        logger.info(f"visible image shape: {self.visible_h}, {self.visible_w}")

        input_param = self._get_input_param()

        # 获取红外图像
        self.infraredImgPath = input_param['red_pic']
        self.infraredImg = get_image(self.infraredImgPath)
        self.red_h, self.red_w = self.infraredImg.shape[:2]
        logger.info(f"red image shape: {self.red_h}, {self.red_w}")

        # 算法输入可见光到红外图像的截取尺寸
        self.visible2red_h = input_param["visible2red_h"]
        self.visible2red_w = input_param["visible2red_w"]
        if self.visible2red_h > self.visible_h:
            self.visible2red_h = self.visible_h
            logger.info(f"visible2red_h set to visible_h: {self.visible2red_h}")
        if self.visible2red_w > self.visible_w:
            self.visible2red_w = self.visible_w
            logger.info(f"visible2red_w set to visible_w: {self.visible2red_w}")

        self.conf = input_param["conf"]

        multi_select = input_param["multi_select"]
        self.multi_select = [int(i) for i in multi_select]
        self.select_class_name = [SCHEMA_DICT[i] for i in self.multi_select]
        logger.info(f"input muti_select: {self.multi_select}, select_class_name: {self.select_class_name}")
    
    def draw_box_string(self, img, topRight, downRight, string, RGB=(255,0,0)):
        """
        img: imread读取的图片;
        topRight, downRight: 矩形框的左上角和右下角坐标;
        string: 显示的文字;
        return: img
        """
        img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
        img = Image.fromarray(img)
        draw = ImageDraw.Draw(img)
        # simhei.ttf 是字体，你如果没有字体，需要下载
        font = ImageFont.truetype("/home/<USER>/backend_common/utils/font/simsun.ttf", 30, encoding="utf-8")
        draw.text((topRight[0], topRight[1]), string, RGB, font=font)
        draw.rectangle((topRight[0], topRight[1], downRight[0], downRight[1]), outline=RGB, width=3)
        img = cv2.cvtColor(np.array(img), cv2.COLOR_RGB2BGR)
        # cv2.rectangle(img, topRight, downRight, (255,0,0), 2)
        return img
    
    def release_resource(self):
        self.yoloModel.release_resource()

    def _do_detect(self):
        roi_min_x = int(self.visible_w / 2 - self.visible2red_w / 2) - int((self.visible_w / 2 - self.visible2red_w / 2) / 2)
        roi_min_y = int(self.visible_h / 2 - self.visible2red_h / 2) - int((self.visible_h / 2 - self.visible2red_h / 2) / 2)
        roi_max_x = int(self.visible_w / 2 + self.visible2red_w / 2) + int((self.visible_w / 2 - self.visible2red_w / 2) / 2)
        roi_max_y = int(self.visible_h / 2 + self.visible2red_h / 2) + int((self.visible_h / 2 - self.visible2red_h / 2) / 2)
        logger.info(f"roi: {roi_min_x}, {roi_min_y}, {roi_max_x}, {roi_max_y}")
        # 裁剪可见光图像
        cropped_img = self.visibleLightImg[roi_min_y:roi_max_y, roi_min_x:roi_max_x]
        # segResults = self.yoloModel.predict(source=cropped_img, conf=self.conf, save=False, classes=self.multi_select, device=self.device, verbose=False)
        segResults, _ = self.yoloModel.predict(cropped_img, conf_thres=self.conf, classes=self.multi_select)

        ocrResult = self.ocr(self.infraredImg)
        osdinfo = []
        temp = None
        if len(ocrResult[0]) == 0:
            logger.info('No text!!')
            # return False, {"infraredAlarmFilter": {"value": "未检测到温度值"}, "temperature": {"value": None}}, []
        else:
            for boxinfo in ocrResult[0]:
                text_box, (text, score) = boxinfo
                logger.info(f"text: {text}, score: {score}")
                if 'X' in text.upper() or 'A' in text:
                    text_box = np.array(text_box).astype('int32').tolist()
                    max_coor = text_box[0]
                    pattern = r'\d*\.?\d+'
                    re_temp = re.findall(pattern, text)
                    if len(re_temp) == 0:
                        break
                    temp = max(re_temp, key=len)
                    if '.' not in temp and len(temp) > 2:
                        temp = temp[:2] + '.' + temp[2:]
                    logger.info(f"Detected temperature: {temp}")
                    # self.infraredImg = self.draw_box_string(self.infraredImg, text_box[0], text_box[2], temp + '℃') 
                    osdinfo.append({"dataType": "STRING", "text": '最高温度: ' + str(float(temp)) + '℃ ', "coords": {"x": 10,"y": 280}})
                    break
        for result in segResults:
            masks = result["masks"]
            # cls = result.boxes.cls.cpu().numpy().tolist()
            if masks is None:
                logger.info('No targets were detected !!')
                if temp is not None:
                    osdinfo.append({"dataType": "STRING","text": "结果：未过滤","coords": {"x": 10,"y": 200}})
                    return True, {"temperature": {"value": float(temp)}}, osdinfo, self.infraredImgPath
                else:
                    osdinfo.append({"dataType": "STRING","text": "结果：未检测到温度值","coords": {"x": 10,"y": 200}})
                    return True, {"temperature": {"value": None}}, osdinfo, self.infraredImgPath
            # xy = masks.xy  # mask in polygon format
            logger.info("detect {} targets".format(len(masks)))
            confs = result["boxes"][:, 4].tolist()  # 置信度
            for mask_points, conf in zip(masks, confs):
                points = np.array(mask_points, dtype=np.int32)  # 转为整数坐标
                # 可见光图像坐标
                ori_points = points + np.array([roi_min_x, roi_min_y]).astype('int32')
                # cv2.polylines(self.visibleLightImg, [ori_points], True, color=(0,255,0), thickness=2, lineType=cv2.LINE_AA)  # 启用抗锯齿)

                # 红外图像坐标在原图裁剪后的坐标
                red_points = ori_points - np.array([self.visible_w/2-self.visible2red_w/2, self.visible_h/2-self.visible2red_h/2]).astype('int32')
                red_points[:, 0] = np.where(red_points[:, 0] > self.visible2red_w, self.visible2red_w, red_points[:, 0])
                red_points[:, 1] = np.where(red_points[:, 1] > self.visible2red_h, self.visible2red_h, red_points[:, 1])
                red_points = np.where(red_points < 0, 0, red_points)

                # 将红外图像坐标映射到红外图像上
                red_points[:, 0] = (red_points[:, 0] * self.red_w /self.visible2red_w).astype('int32')
                red_points[:, 1] = (red_points[:, 1] * self.red_h / self.visible2red_h).astype('int32')
                # 计算最小外接矩形
                x_min, y_min = red_points.min(axis=0)
                x_max, y_max = red_points.max(axis=0)
                box = [int(x_min), int(y_min), int(x_max), int(y_max)]
                # draw_mask = [{"x": i[0], "y": i[1]} for i in red_points.tolist()]
                if temp is not None:
                    flag = cv2.pointPolygonTest(np.array(red_points), max_coor, True)
                    if flag >= 0:
                        cv2.polylines(self.infraredImg, [red_points], True, color=(0,0,255), thickness=2, lineType=cv2.LINE_AA)  # 启用抗锯齿)
                        self.infraredImg = self.draw_box_string(self.infraredImg, box[:2], box[2:], str(round(float(conf), 2)), RGB=(255,0,0))
                        # cv2.imwrite('/opt/tjh/model_server/lts/intelligent_inspection_backend/backend_depl/red_img.jpg', self.infraredImg)
                        # osdinfo.append({"dataType": "SQUARE", "textObj": {"value": "已过滤"}, "score": round(float(conf), 2), "coords": RectUtils.ltrb_to_4_points_dict(box[:2], box[2:])})
                        # osdinfo.append({"dataType": "POLYGON", "coords": draw_mask})
                        resultImgPath = save_image(self.infraredImg)
                        logger.info(f"resultImgPath: {resultImgPath}")
                        resultImgUrl = upload(resultImgPath)
                        logger.info(f"resultImgUrl: {resultImgUrl}")
                        osdinfo.append({"dataType": "STRING","text": "结果：已过滤","coords": {"x": 10,"y": 200}})
                        return True, {"temperature": {"value": None}}, osdinfo, resultImgUrl
                cv2.polylines(self.infraredImg, [red_points], True, color=(0,255,0), thickness=2, lineType=cv2.LINE_AA)  # 启用抗锯齿)
                self.infraredImg = self.draw_box_string(self.infraredImg, box[:2], box[2:], str(round(float(conf), 2)), RGB=(0,255,0))
                # osdinfo.append({"dataType": "SQUARE", "textObj": {"value": "未过滤"}, "score": round(float(conf), 2), "coords": RectUtils.ltrb_to_4_points_dict(box[:2], box[2:])})
                # osdinfo.append({"dataType": "POLYGON", "coords": draw_mask})
        # cv2.imwrite('/opt/tjh/model_server/lts/intelligent_inspection_backend/backend_depl/red_img.jpg', self.infraredImg)
        if temp is not None:
            osdinfo.append({"dataType": "STRING","text": "结果：未过滤","coords": {"x": 10,"y": 200}})
            output = {"temperature": {"value": float(temp)}}
        else:
            osdinfo.append({"dataType": "STRING","text": "结果：未检测到温度值","coords": {"x": 10,"y": 200}})
            output = {"temperature": {"value": None}}
        resultImgPath = save_image(self.infraredImg)
        logger.info(f"resultImgPath: {resultImgPath}")
        resultImgUrl = upload(resultImgPath)
        logger.info(f"resultImgUrl: {resultImgUrl}")
        return True, output, osdinfo, resultImgUrl
