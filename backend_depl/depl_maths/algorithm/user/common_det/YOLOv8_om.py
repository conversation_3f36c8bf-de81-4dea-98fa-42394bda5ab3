import cv2
import numpy as np
import acl

from loguru import logger
from depl_maths.algorithm.user.common_det.utils import xywh2xyxy, multiclass_nms


NPY_FLOAT32 = 11
ACL_MEMCPY_HOST_TO_HOST = 0
ACL_MEMCPY_HOST_TO_DEVICE = 1
ACL_MEMCPY_DEVICE_TO_HOST = 2
ACL_MEMCPY_DEVICE_TO_DEVICE = 3
ACL_MEM_MALLOC_HUGE_FIRST = 0
ACL_DEVICE, ACL_HOST = 0, 1
ACL_SUCCESS = 0


class YOLOv8:
    def __init__(self, device_id, model_path, model_width, model_height):
        self.device_id = device_id
        self.context = None
        self.stream = None

        self.model_id = None
        self.model_desc = None
        self.input_dataset = None
        self.output_dataset = None
        self.input_buffer = None
        self.output_buffer = None
        self.input_buffer_size = None
        self.image_bytes = None
        self.runMode_ = acl.rt.get_run_mode()

        self.model_path = model_path
        self.model_width = model_width
        self.model_height = model_height

        self.init_resource()

    def init_resource(self):
        # init acl resource
        # ret = acl.init()
        # if ret != ACL_SUCCESS:
        #     logger.error(f"acl init failed, errorCode is {ret}")

        ret = acl.rt.set_device(self.device_id)
        if ret != ACL_SUCCESS:
            logger.error(f"set device failed, errorCode is {ret}")

        self.context, ret = acl.rt.create_context(self.device_id)
        if ret != ACL_SUCCESS:
            logger.error(f"create context failed, errorCode is {ret}")

        self.stream, ret = acl.rt.create_stream()
        if ret != ACL_SUCCESS:
            logger.error(f"create stream failed, errorCode is {ret}")

        # load model from file
        self.model_id, ret = acl.mdl.load_from_file(self.model_path)
        if ret != ACL_SUCCESS:
            logger.error(f"load model failed, errorCode is {ret}")

        # create description of model
        self.model_desc = acl.mdl.create_desc()
        ret = acl.mdl.get_desc(self.model_desc, self.model_id)
        if ret != ACL_SUCCESS:
            logger.error(f"get desc failed, errorCode is {ret}")

        # create data set of input
        self.input_dataset = acl.mdl.create_dataset()
        input_index = 0
        self.input_buffer_size = acl.mdl.get_input_size_by_index(self.model_desc, input_index)
        self.input_buffer, ret = acl.rt.malloc(self.input_buffer_size, ACL_MEM_MALLOC_HUGE_FIRST)
        input_data = acl.create_data_buffer(self.input_buffer, self.input_buffer_size)
        self.input_dataset, ret = acl.mdl.add_dataset_buffer(self.input_dataset, input_data)
        if ret != ACL_SUCCESS:
            logger.error(f"acl.mdl.add_dataset_buffer failed, errorCode is {ret}")

        # create data set of output
        self.output_dataset = acl.mdl.create_dataset()
        output_index = 0
        output_buffer_size = acl.mdl.get_output_size_by_index(self.model_desc, output_index)
        logger.info(f"output_buffer_size {output_buffer_size}")
        self.output_buffer, ret = acl.rt.malloc(output_buffer_size, ACL_MEM_MALLOC_HUGE_FIRST)
        output_data = acl.create_data_buffer(self.output_buffer, output_buffer_size)
        self.output_dataset, ret = acl.mdl.add_dataset_buffer(self.output_dataset, output_data)
        if ret != ACL_SUCCESS:
            logger.error(f"acl.mdl.add_dataset_buffer failed, errorCode is {ret}")

    def inference(self):
        if self.runMode_ == ACL_DEVICE:
            kind = ACL_MEMCPY_DEVICE_TO_DEVICE
        else:
            kind = ACL_MEMCPY_HOST_TO_DEVICE
        if "bytes_to_ptr" in dir(acl.util):
            bytes_data = self.image_bytes.tobytes()
            ptr = acl.util.bytes_to_ptr(bytes_data)
        else:
            ptr = acl.util.numpy_to_ptr(self.image_bytes)

        ret = acl.rt.memcpy(self.input_buffer,
                            self.input_buffer_size,
                            ptr,
                            self.input_buffer_size,
                            kind)

        if ret != ACL_SUCCESS:
            logger.error(f"memcpy failed, errorCode is {ret}")

        # inference
        ret = acl.mdl.execute(self.model_id,
                              self.input_dataset,
                              self.output_dataset)
        if ret != ACL_SUCCESS:
            logger.error(f"execute failed, errorCode is {ret}")

    def get_result(self):
        # get result from output data set
        output_index = 0
        output_data_buffer = acl.mdl.get_dataset_buffer(self.output_dataset, output_index)
        output_data_buffer_addr = acl.get_data_buffer_addr(output_data_buffer)
        output_data_size = acl.get_data_buffer_size(output_data_buffer)
        ptr, ret = acl.rt.malloc_host(output_data_size)

        # copy device output data to host
        ret = acl.rt.memcpy(ptr,
                            output_data_size,
                            output_data_buffer_addr,
                            output_data_size,
                            ACL_MEMCPY_HOST_TO_DEVICE)
        if ret != ACL_SUCCESS:
            logger.error(f"memcpy failed, errorCode is {ret}")

        index = 0
        dims, ret = acl.mdl.get_cur_output_dims(self.model_desc, index)

        if ret != ACL_SUCCESS:
            logger.error(f"get output dims failed, errorCode is {ret}")
        out_dim = dims['dims']

        # 从内存地址获取bytes对象
        bytes_out = acl.util.ptr_to_bytes(ptr, output_data_size)
        # 按照float32格式将数据转为numpy数组
        data = np.frombuffer(bytes_out, dtype=np.float32).reshape(out_dim)

        ret = acl.rt.free_host(ptr)
        if ret != ACL_SUCCESS:
            logger.error(f"free host failed, errorCode is {ret}")

        return data

    def release_resource(self):
        # release resource includes acl resource, data set and unload model
        acl.rt.free(self.input_buffer)
        acl.mdl.destroy_dataset(self.input_dataset)

        acl.rt.free(self.output_buffer)
        acl.mdl.destroy_dataset(self.output_dataset)
        ret = acl.mdl.unload(self.model_id)
        if ret != ACL_SUCCESS:
            logger.error(f"unload model failed, errorCode is {ret}")

        if self.model_desc:
            acl.mdl.destroy_desc(self.model_desc)
            self.model_desc = None

        if self.stream:
            ret = acl.rt.destroy_stream(self.stream)
            if ret != ACL_SUCCESS:
                logger.error(f"destroy stream failed, errorCode is {ret}")
            self.stream = None

        if self.context:
            ret = acl.rt.destroy_context(self.context)
            if ret != ACL_SUCCESS:
                logger.error(f"destroy context failed, errorCode is {ret}")
            self.context = None

        ret = acl.rt.reset_device(self.device_id)
        if ret != ACL_SUCCESS:
            logger.error(f"reset device failed, errorCode is {ret}")

        ret = acl.finalize()
        if ret != ACL_SUCCESS:
            logger.error(f"finalize failed, errorCode is {ret}")

    def prepare_input(self, image):
        self.img_height, self.img_width = image.shape[:2]

        input_img = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)

        # Resize input image
        input_img = cv2.resize(input_img, (self.model_width, self.model_height))

        # Scale input pixel values to 0 to 1
        input_img = input_img / 255.0
        input_img = input_img.transpose(2, 0, 1)
        input_tensor = input_img[np.newaxis, :, :, :].astype(np.float32)

        # 模型输入
        self.image_bytes = np.frombuffer(input_tensor.tobytes())

    def process_output(self, output, conf_threshold, iou_threshold):
        predictions = np.squeeze(output[0]).T

        # Filter out object confidence scores below threshold
        scores = np.max(predictions[:, 4:], axis=1)
        predictions = predictions[scores > conf_threshold, :]
        scores = scores[scores > conf_threshold]

        if len(scores) == 0:
            return [], [], []

        # Get the class with the highest confidence
        class_ids = np.argmax(predictions[:, 4:], axis=1)

        # Get bounding boxes for each object
        boxes = self.extract_boxes(predictions)

        # Apply non-maxima suppression to suppress weak, overlapping bounding boxes
        # indices = nms(boxes, scores, self.iou_threshold)
        indices = multiclass_nms(boxes, scores, class_ids, iou_threshold)

        return boxes[indices], scores[indices], class_ids[indices]

    def extract_boxes(self, predictions):
        # Extract boxes from predictions
        boxes = predictions[:, :4]

        # Scale boxes to original image dimensions
        boxes = self.rescale_boxes(boxes)

        # Convert boxes to xyxy format
        boxes = xywh2xyxy(boxes)

        return boxes

    def rescale_boxes(self, boxes):
        # Rescale boxes to original image dimensions
        input_shape = np.array([self.model_width, self.model_height, self.model_width, self.model_height])
        boxes = np.divide(boxes, input_shape, dtype=np.float32)
        boxes *= np.array([self.img_width, self.img_height, self.img_width, self.img_height])
        return boxes

    def detect_objects(self, image, conf_thres, iou_thres):

        self.prepare_input(image)
        acl.rt.set_context(self.context)
        self.inference()
        data = self.get_result()

        # 检测框后处理
        self.boxes, self.scores, self.class_ids = self.process_output(data, conf_thres, iou_thres)
        return self.boxes, self.scores, self.class_ids


if __name__ == '__main__':
    device = 0
    model_width = 640
    model_height = 640

    conf_thres = 0.2
    iou_thres = 0.3

    model_path = "./yolov8m_640_310P3.om"
    img_path = './person.JPEG'
    image = cv2.imread(img_path)

    yolo_net = YOLOv8(device, model_path, model_width, model_height)

    box, score, class_ids = yolo_net.detect_objects(image, conf_thres, iou_thres)

    # om_vis_img = draw_detections(image, box, score, class_ids, 0.4)
    # cv2.imwrite("./om_detected_objects.jpg", om_vis_img)

    yolo_net.release_resource()
