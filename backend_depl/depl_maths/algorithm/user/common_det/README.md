## 人物和物体识别算法V1.0.0

### 描述

 该算法用于人物、物体识别，识别类型包括人、车等，支持大多数场景下的人员、物体识别。 

- 是否配置ROI：否

- 是否配置多图输入：否

### 输入参数

| 参数名   | 是否绘制 | 参数类型     | 默认值  | 是否必填 | 数据范围 | 精度   | 描述                       |
| ----- | ---- | -------- | ---- | ---- | ---- | ---- | ------------------------ |
| ROI区域 | 是    | SQUARE   | -    | 否    | -    | -    | 矩形ROI区域                  |
| 识别类型  | 否    | SELECTOR | -    | 是    | 单选   | -    | 单选下拉框，可选选项为人，自行车、轿车      |
| 识别灵敏度 | 否    | FLOAT    | 0.70 | 是    | 0-1  | 0.01 | 灵敏度越高识别准确率越低，可根据实际情况酌情调节 |

### 输出参数

| 参数名    | 参数类型   | 描述                   |
| ------ | ------ | -------------------- |
| 目标检测结果 | STRING | 输出值为“检测到目标”或“未检测到目标” |
| 目标检测布尔值 | BOOLEAN | "检测到目标": True, "未检测到目标": False |

### 结果展示

| 名称   | 描述                                  |
| ---- | ----------------------------------- |
| 目标位置 | 矩形框，识别出的目标的位置坐标                     |
| 置信度  | 浮点型值，范围0-1之间，精度0.01，代表识别出人物、物体的可信度 |
| 类别名称 | 检测出目标的类别，例如人、车等                     |