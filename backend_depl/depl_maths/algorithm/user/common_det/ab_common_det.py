
import os
import numpy as np
import torch

from backend_common.exceptions.inspection_exception import AlgorithmProcessException
from backend_common.maths.algorithm.base.algorithm_base import AlgorithmBase, AlgorithmInput
from backend_common.utils.util import RectUtils
from depl_maths.algorithm.user.common_det.YOLOv8 import Y<PERSON>Ov8
from depl_maths.algorithm.user.common_det.utils import class_names
from loguru import logger


SCHEMA_DICT = {0: "person", 1: "bicycle", 2: "car"}
EN2CH_DICT = {"person": "人", "bicycle": "自行车", "car": "汽车"}


class CommonDetReader(AlgorithmBase):
    """
        人物和物体识别识别算法yolov8-onnx
    """

    _name = "common_det"

    def __init__(self, _inputs: AlgorithmInput):
        super(CommonDetReader, self).__init__(self.__class__._name)

        # 具体模型路径检查
        detect_model_path = '/home/<USER>/common_det/yolov8m.onnx'
        if not os.path.exists(detect_model_path):
            raise AlgorithmProcessException("人物和物体识别算法模型文件不存在")

        if torch.cuda.is_available():
            device = "cuda"
            logger.info("device use GPU infer")
        else:
            device = "cpu"
            logger.info("device use CPU infer")

        self.yolov8_model = YOLOv8(detect_model_path, device)

        if _inputs.__eq__(dict()):
            return

        self.init(_inputs)

    def init(self, _inputs: AlgorithmInput):
        # 算法实例参数
        self._local_var._inputs = _inputs
        # 获取一张图片
        self.origin_imgs = self._get_input_images()[0]
        # 获取输入参数
        input_param = self._get_input_param()

        input_roi = input_param["input_roi"]
        if input_roi is None:
            self.input_roi = None
        else:
            self.input_roi = [[coor['x'], coor['y']] for coor in input_roi]

        self.threshold = input_param["threshold"]
        self.select = input_param["select"][0]

        return self

    def _do_detect(self):

        if self.input_roi is None:
            logger.info("roi is empty, input the entire image")
            input_img = self.origin_imgs
        else:
            logger.info("input the set roi image")
            roi = np.array(self.input_roi).astype(int)
            xmin, ymin = np.min(roi[:, 0]), np.min(roi[:, 1])
            xmax, ymax = np.max(roi[:, 0]), np.max(roi[:, 1])
            input_img = self.origin_imgs[ymin:ymax, xmin:xmax]

        boxes, scores, class_ids = self.yolov8_model.detect_objects(input_img, self.threshold, iou_thres=0.3)

        osdinfo = []
        class_temp = []
        for class_id, box, score in zip(class_ids, boxes, scores):
            if class_names[class_id] != SCHEMA_DICT[int(self.select)]:
                continue

            x1, y1, x2, y2 = box.astype(int)
            if self.input_roi is not None:
                x1 = x1 + xmin
                y1 = y1 + ymin
                x2 = x2 + xmin
                y2 = y2 + ymin

            coord = RectUtils.ltrb_to_4_points_dict([int(x1), int(y1)], [int(x2), int(y2)])

            label_text = EN2CH_DICT[class_names[class_id]]
            class_temp.append(label_text)
            osdinfo.append({"dataType": "SQUARE", "textObj": {"value": label_text}, "score": round(float(score), 2), "coords": coord})

        if len(class_temp) > 0:
            output = {"common_det": {"value": "检测到目标"}, "common_detBoolean": {"value": True}}
        else:
            output = {"common_det": {"value": "未检测到目标"}, "common_detBoolean": {"value": False}}

        return True, output, osdinfo
