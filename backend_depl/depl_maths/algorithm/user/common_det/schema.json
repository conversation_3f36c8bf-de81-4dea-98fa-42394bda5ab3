{"algorithmMetadata": {"name": "人物和物体识别算法", "code": "common_det", "modelCode": "common_det", "version": "1.0.0", "description": "人物、物体识别算法.V1", "type": "DeepLearning", "isBatchProcessing": false, "roiDrawType": "SQUARE", "roiRequired": false, "classification": "PERSONNEL_BEHAVIOR"}, "input": [{"key": "input_roi", "label": "ROI区域", "dataType": "SQUARE", "constraints": {"required": false}, "drawToOsd": true}, {"key": "select", "label": "识别类型", "dataType": "SELECTOR", "constraints": {"required": true, "maxLength": 1, "options": [{"key": 0, "label": "人"}, {"key": 1, "label": "自行车"}, {"key": 2, "label": "汽车"}]}, "drawToOsd": false}, {"key": "threshold", "label": "识别灵敏度", "dataType": "FLOAT", "defaultValue": 0.7, "constraints": {"min": 0, "max": 1, "precision": 0.01, "required": true}, "drawToOsd": false}], "output": [{"key": "common_det", "label": "目标检测结果", "dataType": "STRING"}, {"key": "common_detBoolean", "label": "目标检测布尔值", "dataType": "BOOLEAN"}]}