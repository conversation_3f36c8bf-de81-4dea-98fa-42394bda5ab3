{"algorithmMetadata": {"name": "禁区闯入识别算法", "code": "target_cross", "modelCode": "common_det", "version": "1.0.0", "description": "禁区闯入识别算法.V1", "type": "DeepLearning", "isBatchProcessing": false, "roiDrawType": "POLYGON", "roiRequired": false, "classification": "PERSONNEL_BEHAVIOR"}, "input": [{"key": "noGoAreas", "label": "禁区", "dataType": "POLYGON", "constraints": {"required": true}, "drawToOsd": true}, {"key": "multi_select", "label": "识别类型", "dataType": "SELECTOR", "constraints": {"required": true, "maxLength": 6, "options": [{"key": 0, "label": "人"}, {"key": 1, "label": "自行车"}, {"key": 2, "label": "汽车"}, {"key": 3, "label": "摩托车"}, {"key": 4, "label": "公共汽车"}, {"key": 5, "label": "卡车"}]}, "drawToOsd": false}, {"key": "threshold", "label": "识别灵敏度", "dataType": "FLOAT", "defaultValue": 0.5, "constraints": {"min": 0, "max": 1, "precision": 0.01, "required": true}, "drawToOsd": false}], "output": [{"key": "target_cross", "label": "禁区闯入识别结果", "dataType": "STRING"}, {"key": "target_crossBoolean", "label": "禁区闯入识别布尔值", "dataType": "BOOLEAN"}]}