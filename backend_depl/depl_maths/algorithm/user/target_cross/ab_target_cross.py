import os
import torch

from loguru import logger
from backend_common.exceptions.inspection_exception import AlgorithmProcessException
from backend_common.maths.algorithm.base.algorithm_base import AlgorithmBase, AlgorithmInput
from backend_common.utils.util import RectUtils

from depl_maths.algorithm.user.target_cross.YOLOv8 import Y<PERSON>Ov8
from depl_maths.algorithm.user.target_cross.utils import class_names, vehicle_cross, person_cross


SCHEMA_DICT = {0: "person", 1: "bicycle", 2: "car", 3: "motorcycle", 4: 'bus', 5: "truck"}
EN2CH_DICT = {"person": "人", "bicycle": "自行车", "car": "汽车", "motorcycle": "摩托车", "truck": "卡车"}


class TargetCrossReader(AlgorithmBase):
    """
    禁区闯入识别算法
    """
    _name = "禁区闯入"

    def __init__(self, _inputs: AlgorithmInput):
        super(TargetCrossReader, self).__init__(self.__class__._name)

        # 和common_det共用一个模型
        detect_model_path = '/home/<USER>/common_det/yolov8m.onnx'
        if not os.path.exists(detect_model_path):
            raise AlgorithmProcessException("人物和物体识别算法模型文件不存在")

        if torch.cuda.is_available():
            device = "cuda"
            logger.info("device use GPU infer")
        else:
            device = "cpu"
            logger.info("device use CPU infer")

        self.yolov8_model = YOLOv8(detect_model_path, device)

        if _inputs.__eq__(dict()):
            return

        self.init(_inputs)

    def init(self, _inputs: AlgorithmInput):
        # 算法实例参数
        self._local_var._inputs = _inputs

        # 获取一张图片
        self.origin_imgs = self._get_input_images()[0]
        input_param = self._get_input_param()

        # 算法输入参数获取
        noGoAreas = input_param["noGoAreas"]

        self.noGoAreas = []
        for coor in noGoAreas:
            temp = [coor['x'], coor['y']]
            self.noGoAreas.append(temp)

        multi_select = input_param["multi_select"]
        logger.info(f"input muti_select: {multi_select}")
        self.select_class_name = [SCHEMA_DICT[int(i)] for i in multi_select]
        self.threshold = input_param["threshold"]
        return self

    def _do_detect(self):

        boxes, scores, class_ids = self.yolov8_model.detect_objects(self.origin_imgs, self.threshold, iou_thres=0.3)

        temp = []
        osdinfo = []
        for class_id, box, score in zip(class_ids, boxes, scores):
            if class_names[class_id] not in self.select_class_name:
                continue

            box = [int(i) for i in box]

            if class_names[class_id] == "person":
                cross_result = person_cross(self.noGoAreas, box)
            else:
                cross_result = vehicle_cross(self.noGoAreas, box)
            if cross_result == 'not_cross':
                continue

            box_name = EN2CH_DICT[class_names[class_id]]
            temp.append(box_name)

            coord = RectUtils.ltrb_to_4_points_dict([box[0], box[1]], [box[2], box[3]])
            osdinfo.append({"dataType": "SQUARE", "textObj": {"value": box_name}, "score": round(float(score), 2), "coords": coord})

        if len(temp) > 0:
            output = {"target_cross": {"value": "闯入"}, "target_crossBoolean":{"value": True}}
        else:
            output = {"target_cross": {"value": "未闯入"}, "target_crossBoolean": {"value": False}}

        return True, output, osdinfo
