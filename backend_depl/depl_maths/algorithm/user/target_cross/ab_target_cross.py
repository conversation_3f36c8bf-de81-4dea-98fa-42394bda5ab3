import os

from loguru import logger
from backend_common.exceptions.inspection_exception import AlgorithmProcessException
from backend_common.maths.algorithm.base.algorithm_base import AlgorithmBase, AlgorithmInput
from backend_common.utils.util import RectUtils

from depl_maths.algorithm.user.target_cross.yolo_om import YOLOv8
from depl_maths.algorithm.user.target_cross.utils import class_names, vehicle_cross, person_cross
import cv2
import numpy as np

SCHEMA_DICT = {0: "person", 1: "bicycle", 2: "car", 3: "motorcycle", 4: 'bus', 5: "truck"}
EN2CH_DICT = {"person": "人", "bicycle": "自行车", "car": "汽车", "motorcycle": "摩托车", "truck": "卡车"}


class TargetCrossReader(AlgorithmBase):
    """
    禁区闯入识别算法
    """
    _name = "禁区闯入"

    def __init__(self, _inputs: AlgorithmInput):
        super(TargetCrossReader, self).__init__(self.__class__._name)

        detect_model_path = '/home/<USER>/common_det/yolov8m_640_310B1.om'
        if not os.path.exists(detect_model_path):
            raise AlgorithmProcessException("通用检测模型文件不存在")

        device_id = 0

        self.yolov8_model = YOLOv8(device_id, detect_model_path, 640, 640)

        if _inputs.__eq__(dict()):
            return

        self.init(_inputs)

    def init(self, _inputs: AlgorithmInput):
        # 算法实例参数
        self._local_var._inputs = _inputs

        # 获取一张图片
        self.origin_img = self._get_input_images()[0]
        input_param = self._get_input_param()

        # 算法输入参数获取
        noGoAreas = input_param["noGoAreas"]

        self.noGoAreas = []
        for coor in noGoAreas:
            temp = [coor['x'], coor['y']]
            self.noGoAreas.append(temp)

        multi_select = input_param["multi_select"]
        logger.info(f"input muti_select: {multi_select}")
        self.select_class_name = [SCHEMA_DICT[int(i)] for i in multi_select]
        self.threshold = input_param["threshold"]
        return self

    def release_resource(self):
        self.yolov8_model.release_resource()

    def drow_figure(self, img):
        # 画禁区
        cv2.polylines(img, [np.array(self.noGoAreas, dtype=np.int32)], True, (0,0,255), 2)
        
        for class_id, box, score in zip(self.class_ids, self.boxes, self.scores):
            if class_names[class_id] not in self.select_class_name:
                continue
            box = [int(i) for i in box]

            if class_names[class_id] == "person":
                cross_result = person_cross(self.noGoAreas, box)
            else:
                cross_result = vehicle_cross(self.noGoAreas, box)
            color = (0,0,255) if cross_result == 'cross' else (0,255,0)
            # box_name = EN2CH_DICT[class_names[class_id]]
            cv2.rectangle(img, (box[0], box[1]), (box[2], box[3]), color, 2)
            cv2.putText(img, f"{round(float(score), 2)}", (box[0], box[1]-10), cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)
        return img

    def _do_detect(self):

        self.boxes, self.scores, self.class_ids = self.yolov8_model.detect_objects(self.origin_img, self.threshold, iou_thres=0.3)

        temp = []
        osdinfo = []
        for class_id, box, score in zip(self.class_ids, self.boxes, self.scores):
            if class_names[class_id] not in self.select_class_name:
                continue

            box = [int(i) for i in box]

            if class_names[class_id] == "person":
                cross_result = person_cross(self.noGoAreas, box)
            else:
                cross_result = vehicle_cross(self.noGoAreas, box)
            if cross_result == 'not_cross':
                continue

            box_name = EN2CH_DICT[class_names[class_id]]
            temp.append(box_name)

            coord = RectUtils.ltrb_to_4_points_dict([box[0], box[1]], [box[2], box[3]])
            osdinfo.append({"dataType": "SQUARE", "textObj": {"value": box_name}, "score": round(float(score), 2), "coords": coord})

        if len(temp) > 0:
            output = {"target_cross": {"value": "闯入"}, "target_crossBoolean": {"value": True}}
        else:
            output = {"target_cross": {"value": "未闯入"}, "target_crossBoolean": {"value": False}}

        return True, output, osdinfo
