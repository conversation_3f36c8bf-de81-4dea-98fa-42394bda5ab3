## 禁区闯入识别算法V1.0.0

### 描述

 该算法用于禁止区域检测，对于此区域有目标时进行报警，包括人、车等。支持工厂、实验室等大多数场景下的禁区闯入的识别。

- 是否配置ROI：否

- 是否配置多图输入：否

### 输入参数

| 参数名   | 是否绘制 | 参数类型     | 默认值  | 是否必填 | 数据范围        | 精度   | 描述                                |
| ----- | ---- | -------- | ---- | ---- | ----------- | ---- | --------------------------------- |
| 禁区    | 是    | POLYGON  | -    | 是    | -           | -    | 闭合多边形区域为禁区                        |
| 识别类型  | 否    | SELECTOR | -    | 是    | 多选，支持1-6个选项 | -    | 需要检测的目标类别，支持多类别                   |
| 识别灵敏度 | 否    | FLOAT    | 0.50 | 是    | 0-1         | 0.01 | 算法识别的灵敏度，<br>灵敏度越高识别准确率越低，<br>可根据实际情况酌情调节 |

### 输出参数

| 参数名      | 参数类型   | 描述               |
| -------- | ------ | ---------------- |
| 禁区闯入识别结果 | STRING | 输出值为"闯入" 或 “未闯入” |
| 禁区闯入识别布尔值 | BOOLEAN	 | "闯入":True, "未闯入":False |


### 结果展示

| 名称   | 描述                                |
| ---- | ---------------------------------- |
| 识别位置 | 矩形框，识别出的目标的位置坐标                    |
| 识别类型 | 检测出目标的类别，例如人、车等                    |
| 置信度  | 浮点型值，范围0-1之间，精度0.01，代表识别出人物、物体的可信度 |