import os.path
from typing import Any, List

from backend_common.maths.algorithm.base.algorithm_base import AlgorithmInput

from depl_maths.algorithm.system.grounding_dino.ab_grounding_dino import GroundingDinoReader
from depl_maths.algorithm.system.grounding_dino.grounding_dino_model import GroundingDINODetect


class GroundingDinoReaderSub0(GroundingDinoReader):
    """
    泄漏检测算法
    """

    _index = 0
    _description = "泄漏检测算法.主模型.版本v1"
    _cn_name = "泄漏检测算法"

    def __init__(self, _inputs: AlgorithmInput, _id: int = -1):
        super(GroundingDinoReaderSub0, self).__init__(_inputs, _id)

        config_file = os.path.join(os.path.dirname(__file__), 'config/GroundingDINO_SwinB_cfg.py')
        checkpoint_path = os.path.join(os.path.dirname(__file__), 'inference_model/groundingdino_swinb_cogcoor.pth')
        cpu_only = False

        self.grounding_dino_model = GroundingDINODetect(config_file, checkpoint_path, cpu_only)

    def _postprocess(self) -> Any:
        pass

    def _alarm_trig(self, detect_ret) -> List[List]:
        return [[(True, "H", "检测到指定目标") if len(detect_ret) > 0
                 else (False, None, None)] for detect_ret in detect_ret[1]]

    def grounding_dino_detect(self, batch_img: list, prompt_category, filter_category, box_threshold, text_threshold, box_area_threshold, iou_threshold, roi_coor):
        results = []
        for img in batch_img:
            detect_results = self.grounding_dino_model.predict(img, prompt_category, filter_category, box_threshold, text_threshold, box_area_threshold, iou_threshold, roi_coor)
            results.append(detect_results)
        return results
