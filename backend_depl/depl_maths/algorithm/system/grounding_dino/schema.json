{"algorithm": {"name": "泄露检测", "code": "grounding_dino", "des": "泄露检测算法.V1", "type": "DeepLearning", "alarmSupport": true, "isOverrideAlarm": false, "batchSupport": true, "resultShowType": "ALARM"}, "images": ["e:/images/test.jpg"], "roi": {"name": "ROI区域", "type": "polygon", "des": "支持绘制的ROI区域类型, polygon:多边形, circle:圆, ellipse:椭圆", "value": [[325, 116], [331, 115], [346, 310], [337, 310]], "nullable": true}, "targetSceneImage": {"in_use": false, "uri": "", "name": "场景基准图"}, "inputParam": {"box_threshold": {"name": "框检测阈值", "type": "number", "des": "目标框识别阈值", "value": 0.1, "val_range": [0, 1, 0.01, 0.1], "nullable": false}, "text_threshold": {"name": "类别阈值", "type": "number", "des": "类别阈值", "value": 0.1, "val_range": [0, 1, 0.01, 0.1], "nullable": false}, "box_area_threshold": {"name": "框面积占比阈值", "type": "number", "des": "检测框面积与原图面积占比阈值,高于该值的框会过滤掉", "value": 0.95, "val_range": [0, 1, 0.01, 0.95], "nullable": false}, "iou_threshold": {"name": "iou阈值", "type": "number", "des": "检测框IOU过滤阈值", "value": 0.4, "val_range": [0, 1, 0.01, 0.4], "nullable": false}}, "outputDefine": {"type": "string", "desc": "泄露检测", "val_enum": {"target_detected": 0, "target_not_detected": 1}, "ruleInfo": [{"desc": "检测到目标", "isBind": false, "type": "HH", "val": 0.0, "placeHolder": "数值大于高"}, {"desc": "检测到目标", "isBind": true, "type": "H", "val": 1.0, "placeHolder": "数值小于高高"}, {"desc": "检测到目标", "isBind": false, "type": "L", "val": 0.0, "placeHolder": "数值小于高"}, {"desc": "检测到目标", "isBind": false, "type": "LL", "val": 0.0, "placeHolder": "数值小于低"}]}}