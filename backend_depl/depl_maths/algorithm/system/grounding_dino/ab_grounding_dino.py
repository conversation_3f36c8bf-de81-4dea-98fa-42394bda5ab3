import importlib
import os
from typing import Any, List

import numpy as np
from backend_common.utils.util import RectUtils, get_image

from backend_common.constants.constant import AlgorithmClassifyEnum, AlgorithmStrategyEnum
from backend_common.constants.math_constants import ALGORITHM_DEPL_USER_DEFINE_PATH, ALGORITHM_DEPL_SYSTEM_INNER_PATH
from backend_common.exceptions.inspection_exception import InspectionException
from backend_common.maths.algorithm.base.algorithm_base import AlgorithmBase, AlgorithmStrategy, AlgorithmSettings, AlgorithmDetail, \
    AlgorithmBaseInfo, AlgorithmInput

from depl_maths.algorithm.system.grounding_dino.input_parameter import PROMPT_CATEGORY, FILTER_CATEGORY

SUFFIX_START = "sub"


class GroundingDinoReader(AlgorithmBase):
    """
    泄漏检测算法
    """

    schema_path = os.path.join(os.path.dirname(__file__), "schema.json")

    _name = "grounding_dino"
    _description = "泄漏检测算法.基础.版本v1"
    _cn_name = "泄漏检测算法"

    # 支持的算法分类个数
    __supported_classify = (AlgorithmClassifyEnum.Primary,)
    # 支持的算法策略，primary -> main & secondary -> main
    __supported_strategies = [
        AlgorithmStrategy(__supported_classify[0], AlgorithmStrategyEnum.Main, "泄漏检测主识别策略",
                          "泄漏检测识别主策略", kv_param={}),
    ]

    # 支持的算法参数，若干
    __supported_params = AlgorithmBase.json_schema_2_algorithm_params(schema_path)

    def __init__(self, _inputs: AlgorithmInput, _id: int = -1):
        super(GroundingDinoReader, self).__init__(self.__class__._name, self.__class__._description,
                                            self.__class__._cn_name,
                                            self.__class__.__supported_strategies,
                                            self.__class__.__supported_params)

        if _inputs.__eq__(dict()):
            return

        self.init(_inputs, None)

    def init(self, _inputs: AlgorithmInput, _settings: AlgorithmSettings):
        # 算法实例参数
        self._local_var._settings = _settings
        self._local_var._inputs = _inputs

        self._local_var._inputImages = self._get_input_images()

        self.prompt_category = PROMPT_CATEGORY
        self.filter_category = FILTER_CATEGORY
        self.box_threshold = self._get_input_value_by_name("box_threshold")
        self.text_threshold = self._get_input_value_by_name("text_threshold")
        self.box_area_threshold = self._get_input_value_by_name("box_area_threshold")
        self.iou_threshold = self._get_input_value_by_name("iou_threshold")

        self.roi = self._local_var._inputs.get("roi")

        self._local_var._processImagesNormalized = []
        # self._local_var._processTempRet = []
        self._local_var._processFinalRet = [[] for _ in self._local_var._inputImages]
        self._local_var._processFinalRect = [[] for _ in self._local_var._inputImages]
        self._local_var._processOutputDir = os.getcwd()
        return self

    def _supported_classify(self):
        return self.__supported_classify

    def _supported_strategy(self):
        return self.__supported_strategies

    def _supported_params(self):
        return self.__supported_params

    def _preprocess(self) -> Any:  # TODO
        pass

    def _do_detect(self) -> (bool, Any, tuple):
        ret = self.grounding_dino_detect(self._local_var._inputImages,
                                         self.prompt_category,
                                         self.filter_category,
                                         self.box_threshold,
                                         self.text_threshold,
                                         self.box_area_threshold,
                                         self.iou_threshold,
                                         self.roi)
        self._local_var._processTempRet = ret

        self._local_var._processFinalRect = [[] for _ in self._local_var._inputImages]
        """ 按约定格式输出 """
        for img_idx, img in enumerate(self._local_var._inputImages):
            temp, bboxs = [], []
            for b_idx, res in enumerate(self._local_var._processTempRet[img_idx]):
                box = []
                xmin, ymin, xmax, ymax, box_score, box_class = res
                temp.append(box_class)
                _4points_score = RectUtils.ltrb_to_4_points([xmin, ymin], [xmax, ymax])
                box.append(_4points_score)
                box.append(float(box_score))
                bboxs.append(box)
            self._local_var._processFinalRet[img_idx] = temp
            self._local_var._processFinalRect[img_idx] = bboxs

        # TODO 当前只支持一张图, val 返回什么呢？
        return True, self._local_var._processFinalRet, self._local_var._processFinalRect

    def _postprocess(self) -> Any:
        pass

    def _gen_result_img(self) -> List[np.ndarray]:
        pass

    def draw_bbox_image(self, img_idx, img_str):
        """ 画 bbox到图上 """
        pass

    def determine_algorithm_instance(self, settings: AlgorithmSettings = None, index: int = 0,
                                     a_type: str = "system_inner"):
        suffix = f"{SUFFIX_START}{index}"
        module_name = f".{self._name}.ab_{self._name}_{suffix}"

        base_path = ALGORITHM_DEPL_SYSTEM_INNER_PATH
        if a_type == "user_define":
            base_path = ALGORITHM_DEPL_USER_DEFINE_PATH

        try:
            M = importlib.import_module(module_name, base_path)
        except ModuleNotFoundError:
            raise InspectionException(f"Auto-load module '{module_name}' from '{base_path}' \
                                failure, please check it manually")

        # !!!!  找出子类中名称以当前 suffix.capitalize()  为 END 的作为 target 算法类  !!!!
        target_clazz_ = [clazz for clazz in GroundingDinoReader.__subclasses__()
                         if clazz.__name__.endswith(suffix.capitalize())][0]

        algorithm_instance: GroundingDinoReader = target_clazz_(self._local_var._inputs, self._id)
        algorithm_instance._settings = settings

        return algorithm_instance

    def get_algorithm_detail(self, a_type) -> AlgorithmDetail:
        schema_base = AlgorithmBase._get_algorithm_schema_info(self.schema_path, "algorithm")
        info = AlgorithmBaseInfo(self._id, self._name, self._name, self.cn_name, self._description,
                                 batch_support=schema_base.get("batchSupport"),
                                 alarm_support=schema_base.get("alarmSupport"),
                                 is_override_alarm=schema_base.get("isOverrideAlarm"),
                                 result_show_type=schema_base.get("resultShowType"))
        classifies = [self._classify2info(c) for c in self.__supported_classify]
        strategies = [self._strategy2info(s) for s in self.__supported_strategies]
        roi = self._get_roi_define(self.schema_path)
        scene_image = self._get_scene_image(self.schema_path)
        params = AlgorithmBase.json_schema_2_algorithm_params(self.schema_path)
        output_define = self._output_define2info(
            AlgorithmBase._get_algorithm_schema_info(self.schema_path, "outputDefine"))
        sub_list = self.load_algorithm_sublist(self._name, a_type, 'depl')

        return AlgorithmDetail(info, classifies, strategies, roi, scene_image, params, output_define, sub_list)

    def grounding_dino_detect(self,
                              batch_img: list,
                              prompt_category,
                              filter_category,
                              box_threshold,
                              text_threshold,
                              box_area_threshold,
                              iou_threshold,
                              roi_coor):
        """
        漏水检测算法
        """
        raise NotImplementedError("you should implement it in sub-algorithm")
