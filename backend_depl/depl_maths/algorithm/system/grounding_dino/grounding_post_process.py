import torch
import numpy as np
import cv2


def preprocess_caption(caption):
    caption = caption.replace('"', '')
    result = caption.lower().strip()
    if result.endswith("."):
        return result
    return result + "."


def result_post_process(text_prompt, pred_dict):
    # tensor to numpy
    boxes = pred_dict["boxes"].numpy()
    labels = pred_dict["labels"]
    logits = pred_dict["logits"]

    texts = preprocess_caption(text_prompt)
    texts = texts.replace(' ', '').split(".")[0:-1]

    # first label filter
    label_select = [True if i in texts else False for i in labels]

    new_labels = []
    new_logits = []
    for i in range(len(label_select)):
        if label_select[i]:
            new_labels.append(labels[i])
            new_logits.append(logits[i])

    boxes = boxes[label_select]

    boxes = torch.from_numpy(boxes)

    pred_dict["boxes"] = boxes
    pred_dict["labels"] = new_labels
    pred_dict["logits"] = new_logits
    return pred_dict


def boxes_pross(boxes, size):
    H, W = size[1], size[0]

    new_boxes = []
    # draw boxes and masks
    for box in boxes:
        # from 0..1 to 0..W, 0..H
        box = box * torch.Tensor([W, H, W, H])
        # from xywh to xyxy
        box[:2] -= box[2:] / 2
        box[2:] += box[:2]
        # draw
        x0, y0, x1, y1 = box
        x0, y0, x1, y1 = int(x0), int(y0), int(x1), int(y1)
        x0, y0, x1, y1 = x0, y0, x1, y1
        new_boxes.append([x0, y0, x1, y1])
    return new_boxes


def get_index(text_list=None, item=''):
    return [i for i in range(len(text_list)) if text_list[i] == item]


def intersection_over_union(boxA, boxB):
    # 计算两个边界框的交并比(IOU)
    xA = max(boxA[0], boxB[0])
    yA = max(boxA[1], boxB[1])
    xB = min(boxA[2], boxB[2])
    yB = min(boxA[3], boxB[3])

    interArea = max(0, xB - xA + 1) * max(0, yB - yA + 1)

    boxAArea = (boxA[2] - boxA[0] + 1) * (boxA[3] - boxA[1] + 1)
    boxBArea = (boxB[2] - boxB[0] + 1) * (boxB[3] - boxB[1] + 1)

    iou = interArea / float(boxAArea + boxBArea - interArea)
    return iou


def non_max_suppression(boxes, scores, iou_threshold=0.5):
    """
    实现非极大值抑制(NMS)，输入是边界框和对应的分数，
    返回经过NMS处理后的边界框列表。
    """
    # 根据分数排序
    sorted_indices = np.argsort(scores)[::-1]

    keep_boxes = []
    while sorted_indices.size > 0:
        # 选择当前最高分的框
        idx = sorted_indices[0]
        keep_boxes.append(idx)

        # 计算当前框与其他所有框的IOU
        ious = np.array([intersection_over_union(boxes[idx], boxes[i]) for i in sorted_indices[1:]])

        # 删除与当前框IOU大于阈值的框
        remove_indices = np.where(ious > iou_threshold)[0] + 1
        sorted_indices = np.delete(sorted_indices, remove_indices)
        sorted_indices = np.delete(sorted_indices, 0)
    return keep_boxes


def get_info_from_index(index_list, grounding_output):
    boxes, logits = [], []
    for i in index_list:
        boxes.append(grounding_output[i][0:4])
        logits.append(grounding_output[i][4])
    return boxes, logits


def class_nms(grounding_output, iou_thresh, name_conversion_dict):

    for i in grounding_output:
        i[5] = name_conversion_dict[i[5]]

    labels = [i[5] for i in grounding_output]
    no_repet_labels = set(labels)

    box_index = []
    for i in no_repet_labels:
        label_index = get_index(labels, i)
        boxes, logits = get_info_from_index(label_index, grounding_output)
        keep_boxes = non_max_suppression(np.array(boxes), np.array(logits), iou_thresh)

        for i in keep_boxes:
            box_index.append(label_index[i])

    res = []
    for i in box_index:
        res.append(grounding_output[i])
    return res


def boxes_area_filter(grounding_output, img_size, area_thresh=0.9):

    # img_h, img_w = img_size[:2]
    img_h, img_w = img_size[1], img_size[0]

    res = []
    for i, info in enumerate(grounding_output):
        box_h = info[3] - info[1]
        box_w = info[2] - info[0]

        if (box_w * box_h) / (img_w * img_h) < area_thresh:
            res.append(info)
    return res


def class_filter(grounding_output, text_prompt, filter_category):

    text_prompt = preprocess_caption(text_prompt)
    text_prompt = text_prompt.split(".")[0:-1]
    print("text_prompt:", text_prompt)

    filter_category = preprocess_caption(filter_category)
    filter_category = filter_category.split(".")[0:-1]
    print("filter_category", filter_category)

    temp, res = [], []
    for info in grounding_output:
        if info[5] in text_prompt:
            temp.append(info)

    for info in temp:
        if info[5] not in filter_category:
            res.append(info)
    return res


def object_cross(polygon, bbox):
    xmin, ymin, xmax, ymax = bbox
    middle = [int((xmax-xmin)/2)+xmin, int((ymax-ymin)/2)+ymin]
    midle_left = [int((middle[0]-xmin)/2)+xmin, int((ymax-middle[1])/2)+middle[1]]
    middle_right = [int((xmax-middle[0])/2)+middle[0], int((ymax-middle[1])/2)+middle[1]]
    for point in [middle, midle_left, middle_right]:
        flag = cv2.pointPolygonTest(np.array(polygon).astype('int32'), point, True)
        if flag < 0:
            return 'nocross'
    return 'cross'


def boxes_in_roi(grounding_output, roi_coor):
    res = []
    for info in grounding_output:
        flag = object_cross(roi_coor, info[0:4])
        if flag == 'cross':
            res.append(info)

    return res
