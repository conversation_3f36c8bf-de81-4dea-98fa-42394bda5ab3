## 泄漏检测识别算法

## 版本:

 v 1.0.0

## 描述:

 该算法用于对工厂环境中常见的漏水、漏油等进行检测。

## 输入:

| 名称      | 类型      | 描述                                 |
| ------- | ------- | ---------------------------------- |
| ROI     | polygon | 多边形，代表显示该区域的检测结果                   |
| 框面积占比阈值 | number  | 范围0-1，检测框面积与原图面积占比阈值,高于该值的检测框会被过滤掉 |
| 框检测阈值   | number  | 范围0-1，选择阈值高于该值的框                   |
| IOU阈值   | number  | 范围0-1，对同类别的重叠框进行屏蔽                 |
| 类别阈值    | number  | 范围0-1，在框检测阈值的基础上对类别阈值二次过滤          |

* 是否支持单图批量识别：否

## 输出：

| 名称   | 类型     | 描述                  |
| ---- | ------ | ------------------- |
| 识别位置 | square | 矩形，代表识别目标的位置坐标      |
| 类别   | string | 字符串，类别名称            |
| 置信度  | number | 数值，0-1之间，代表识别目标的可信度 |