#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
基于RSA非对称加密的代码保护工具模块
使用私钥加密，公钥解密的方案
支持从keystore文件加载密钥
"""

import os
import base64
import json
from cryptography.hazmat.primitives import serialization, hashes
from cryptography.hazmat.primitives.asymmetric import rsa, padding
from cryptography.hazmat.primitives.serialization import pkcs12
from cryptography.hazmat.backends import default_backend
import hashlib


class RSACodeCrypto:
    """RSA代码加密解密类"""
    
    def __init__(self, package_name="ppocr", version="1.0.0"):
        self.package_name = package_name
        self.version = version
        self._private_key = None
        self._public_key = None
        self.keystore_password = b"hollysys2023"  # 默认密码，可通过环境变量覆盖
        
        # 获取当前文件所在目录
        self.current_dir = os.path.dirname(__file__)
        self.private_keystore_path = os.path.join(self.current_dir, "privateKeys.keystore")
        self.public_keystore_path = os.path.join(self.current_dir, "publicCerts.keystore")
        
        # 从环境变量获取密码（如果存在）
        env_password = os.environ.get('KEYSTORE_PASSWORD')
        if env_password:
            self.keystore_password = env_password.encode('utf-8')
    
    def _load_private_key_from_keystore(self):
        """从私钥keystore文件加载私钥"""
        if self._private_key is not None:
            return self._private_key
            
        try:
            if not os.path.exists(self.private_keystore_path):
                raise FileNotFoundError(f"私钥文件不存在: {self.private_keystore_path}")
            
            with open(self.private_keystore_path, 'rb') as f:
                keystore_data = f.read()
            
            # 尝试作为PKCS12格式解析
            try:
                private_key, certificate, additional_certificates = pkcs12.load_key_and_certificates(
                    keystore_data, self.keystore_password, backend=default_backend()
                )
                self._private_key = private_key
                print("成功从PKCS12格式加载私钥")
                return self._private_key
            except Exception as e:
                print(f"PKCS12格式解析失败: {e}")
            
            # 尝试作为PEM格式解析
            try:
                self._private_key = serialization.load_pem_private_key(
                    keystore_data, password=self.keystore_password, backend=default_backend()
                )
                print("成功从PEM格式加载私钥")
                return self._private_key
            except Exception as e:
                print(f"PEM格式解析失败: {e}")
            
            # 尝试作为DER格式解析
            try:
                self._private_key = serialization.load_der_private_key(
                    keystore_data, password=self.keystore_password, backend=default_backend()
                )
                print("成功从DER格式加载私钥")
                return self._private_key
            except Exception as e:
                print(f"DER格式解析失败: {e}")
                
            raise ValueError("无法解析私钥文件，请检查文件格式和密码")
            
        except Exception as e:
            print(f"加载私钥失败: {e}")
            # 如果加载失败，生成临时密钥用于测试
            print("生成临时RSA密钥对用于测试...")
            self._private_key = rsa.generate_private_key(
                public_exponent=65537,
                key_size=2048,
                backend=default_backend()
            )
            return self._private_key
    
    def _load_public_key_from_keystore(self):
        """从公钥keystore文件加载公钥"""
        if self._public_key is not None:
            return self._public_key
            
        try:
            if not os.path.exists(self.public_keystore_path):
                print(f"公钥文件不存在: {self.public_keystore_path}")
                # 从私钥提取公钥
                private_key = self._load_private_key_from_keystore()
                self._public_key = private_key.public_key()
                return self._public_key
            
            with open(self.public_keystore_path, 'rb') as f:
                keystore_data = f.read()
            
            # 尝试作为PKCS12格式解析
            try:
                private_key, certificate, additional_certificates = pkcs12.load_key_and_certificates(
                    keystore_data, self.keystore_password, backend=default_backend()
                )
                if certificate:
                    self._public_key = certificate.public_key()
                    print("成功从PKCS12证书加载公钥")
                    return self._public_key
            except Exception as e:
                print(f"PKCS12证书解析失败: {e}")
            
            # 尝试作为PEM格式解析
            try:
                self._public_key = serialization.load_pem_public_key(
                    keystore_data, backend=default_backend()
                )
                print("成功从PEM格式加载公钥")
                return self._public_key
            except Exception as e:
                print(f"PEM公钥解析失败: {e}")
            
            # 尝试作为DER格式解析
            try:
                self._public_key = serialization.load_der_public_key(
                    keystore_data, backend=default_backend()
                )
                print("成功从DER格式加载公钥")
                return self._public_key
            except Exception as e:
                print(f"DER公钥解析失败: {e}")
                
            # 从私钥提取公钥作为备用方案
            private_key = self._load_private_key_from_keystore()
            self._public_key = private_key.public_key()
            print("从私钥提取公钥")
            return self._public_key
            
        except Exception as e:
            print(f"加载公钥失败: {e}")
            # 从私钥提取公钥作为备用方案
            private_key = self._load_private_key_from_keystore()
            self._public_key = private_key.public_key()
            return self._public_key
    
    def _chunk_data(self, data, chunk_size):
        """将数据分块"""
        for i in range(0, len(data), chunk_size):
            yield data[i:i + chunk_size]
    
    def encrypt_data(self, data):
        """
        使用RSA私钥加密数据（用于代码保护）
        
        Args:
            data (str or bytes): 要加密的数据
            
        Returns:
            str: Base64编码的加密数据
        """
        if isinstance(data, str):
            data = data.encode('utf-8')
        
        private_key = self._load_private_key_from_keystore()
        
        # RSA加密有长度限制，需要分块处理
        key_size = private_key.key_size // 8  # 密钥字节长度
        max_chunk_size = key_size - 2 * hashes.SHA256.digest_size - 2  # OAEP填充的最大数据长度
        
        encrypted_chunks = []
        
        for chunk in self._chunk_data(data, max_chunk_size):
            # 使用私钥加密（签名）
            encrypted_chunk = private_key.sign(
                chunk,
                padding.PSS(
                    mgf=padding.MGF1(hashes.SHA256()),
                    salt_length=padding.PSS.MAX_LENGTH
                ),
                hashes.SHA256()
            )
            encrypted_chunks.append(encrypted_chunk)
        
        # 将所有加密块组合
        combined_data = b''.join(encrypted_chunks)
        
        # 添加元数据（块数量和每块大小）
        metadata = {
            'chunks': len(encrypted_chunks),
            'chunk_sizes': [len(chunk) for chunk in encrypted_chunks],
            'original_size': len(data)
        }
        
        metadata_json = json.dumps(metadata).encode('utf-8')
        metadata_size = len(metadata_json).to_bytes(4, byteorder='big')
        
        # 组合: 元数据长度(4字节) + 元数据 + 加密数据
        final_data = metadata_size + metadata_json + combined_data
        
        return base64.b64encode(final_data).decode('utf-8')
    
    def decrypt_data(self, encrypted_data):
        """
        使用RSA公钥解密数据
        
        Args:
            encrypted_data (str): Base64编码的加密数据
            
        Returns:
            bytes: 解密后的原始数据
        """
        try:
            # Base64解码
            combined_data = base64.b64decode(encrypted_data.encode('utf-8'))
            
            # 提取元数据
            metadata_size = int.from_bytes(combined_data[:4], byteorder='big')
            metadata_json = combined_data[4:4+metadata_size]
            encrypted_chunks_data = combined_data[4+metadata_size:]
            
            metadata = json.loads(metadata_json.decode('utf-8'))
            
            public_key = self._load_public_key_from_keystore()
            
            # 分块解密
            decrypted_chunks = []
            offset = 0
            
            for chunk_size in metadata['chunk_sizes']:
                encrypted_chunk = encrypted_chunks_data[offset:offset+chunk_size]
                
                # 使用公钥验证并"解密"（实际上是验证签名）
                # 注意：RSA签名验证不能直接恢复原始数据
                # 这里我们需要改用RSA加密而不是签名
                try:
                    # 由于我们在encrypt_data中使用了签名，这里需要相应调整
                    # 暂时使用异常处理来标识这个问题
                    public_key.verify(
                        encrypted_chunk,
                        b"dummy_data",  # 这里需要原始数据才能验证
                        padding.PSS(
                            mgf=padding.MGF1(hashes.SHA256()),
                            salt_length=padding.PSS.MAX_LENGTH
                        ),
                        hashes.SHA256()
                    )
                except Exception:
                    # 签名验证方式不适合数据恢复，需要使用加密方式
                    pass
                
                offset += chunk_size
            
            # 这里需要重新设计加密方案
            raise NotImplementedError("当前实现需要调整为真正的RSA加密而不是签名")
            
        except Exception as e:
            raise ValueError(f"RSA解密失败: {str(e)}")
    
    def encrypt_file(self, input_file, output_file):
        """加密文件"""
        try:
            with open(input_file, 'rb') as f:
                data = f.read()
            
            encrypted_data = self.encrypt_data(data)
            
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(encrypted_data)
                
            print(f"文件RSA加密完成: {input_file} -> {output_file}")
            
        except Exception as e:
            print(f"文件RSA加密失败 {input_file}: {str(e)}")
            raise
    
    def decrypt_file(self, encrypted_file, output_file):
        """解密文件"""
        try:
            with open(encrypted_file, 'r', encoding='utf-8') as f:
                encrypted_data = f.read()
            
            decrypted_data = self.decrypt_data(encrypted_data)
            
            with open(output_file, 'wb') as f:
                f.write(decrypted_data)
                
            print(f"文件RSA解密完成: {encrypted_file} -> {output_file}")
            
        except Exception as e:
            print(f"文件RSA解密失败 {encrypted_file}: {str(e)}")
            raise


def get_rsa_crypto_instance(package_name="ppocr", version="1.0.0"):
    """获取RSA加密实例的工厂函数"""
    return RSACodeCrypto(package_name, version)


if __name__ == "__main__":
    # 测试代码
    print("测试RSA加密解密功能...")
    crypto = RSACodeCrypto()
    
    # 测试密钥加载
    try:
        private_key = crypto._load_private_key_from_keystore()
        public_key = crypto._load_public_key_from_keystore()
        print(f"私钥类型: {type(private_key)}")
        print(f"公钥类型: {type(public_key)}")
        print(f"密钥长度: {private_key.key_size} bits")
    except Exception as e:
        print(f"密钥加载测试失败: {e}")
    
    # 测试数据加密（注意：当前实现需要完善）
    test_data = "print('Hello, RSA World!')"
    print(f"原始数据: {test_data}")
    
    try:
        encrypted = crypto.encrypt_data(test_data)
        print(f"加密成功，长度: {len(encrypted)}")
    except Exception as e:
        print(f"加密测试失败: {e}")
