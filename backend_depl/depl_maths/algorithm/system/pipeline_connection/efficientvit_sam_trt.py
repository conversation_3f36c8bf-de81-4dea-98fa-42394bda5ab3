from copy import deepcopy
from typing import Tuple

import cv2
import matplotlib.pyplot as plt
import numpy as np
import tensorrt as trt
import torch
import torch.nn.functional as F
import torchvision.transforms as transforms
from torch2trt import TRTModule
from torchvision.transforms.functional import resize


class SamResize():
    def __init__(self, size: int) -> None:
        self.size = size

    def __call__(self, image: torch.Tensor) -> torch.Tensor:
        h, w, _ = image.shape
        long_side = max(h, w)
        if long_side != self.size:
            return self.apply_image(image)
        else:
            return image.permute(2, 0, 1)

    def apply_image(self, image: torch.Tensor) -> torch.Tensor:
        """
        Expects a torch tensor with shape HxWxC in float format.
        """

        target_size = self.get_preprocess_shape(image.shape[0], image.shape[1], self.size)
        return resize(image.permute(2, 0, 1), target_size)

    @staticmethod
    def get_preprocess_shape(oldh: int, oldw: int, long_side_length: int) -> Tuple[int, int]:
        """
        Compute the output size given input size and target long side length.
        """
        scale = long_side_length * 1.0 / max(oldh, oldw)
        newh, neww = oldh * scale, oldw * scale
        neww = int(neww + 0.5)
        newh = int(newh + 0.5)
        return (newh, neww)

    def __repr__(self) -> str:
        return f"{type(self).__name__}(size={self.size})"


class EfficientSamDet():
    def __init__(self, encoder_path, decoder_path, model_type):

        self.model_type = model_type

        with trt.Logger() as logger, trt.Runtime(logger) as runtime:
            with open(encoder_path, 'rb') as f:
                engine_bytes = f.read()
            engine = runtime.deserialize_cuda_engine(engine_bytes)
        # init encoder model
        self.trt_encoder = TRTModule(
            engine,
            input_names=["input_image"],
            output_names=["image_embeddings"]
        )

        with trt.Logger() as logger, trt.Runtime(logger) as runtime:
            with open(decoder_path, 'rb') as f:
                engine_bytes = f.read()
            engine = runtime.deserialize_cuda_engine(engine_bytes)
        # init decoder model
        self.trt_decoder = TRTModule(
            engine,
            input_names=["image_embeddings", "point_coords", "point_labels"],
            output_names=["masks", "iou_predictions"]
        )

    def preprocess(self, x, img_size, device):
        pixel_mean = [123.675 / 255, 116.28 / 255, 103.53 / 255]
        pixel_std = [58.395 / 255, 57.12 / 255, 57.375 / 255]

        x = torch.tensor(x).to(device)
        resize_transform = SamResize(img_size)
        x = resize_transform(x).float() / 255
        x = transforms.Normalize(mean=pixel_mean, std=pixel_std)(x)

        h, w = x.shape[-2:]
        th, tw = img_size, img_size
        assert th >= h and tw >= w
        x = F.pad(x, (0, tw - w, 0, th - h), value=0).unsqueeze(0)
        return x

    def resize_longest_image_size(self, input_image_size, longest_side):
        input_image_size = input_image_size.to(torch.float32)
        scale = longest_side / torch.max(input_image_size)
        transformed_size = scale * input_image_size
        transformed_size = torch.floor(transformed_size + 0.5).to(torch.int64)
        return transformed_size

    def mask_postprocessing(self, masks, orig_im_size):
        img_size = 1024
        masks = torch.tensor(masks)
        orig_im_size = torch.tensor(orig_im_size)

        masks = F.interpolate(
            masks,
            size=(img_size, img_size),
            mode="bilinear",
            align_corners=False,
        )

        prepadded_size = self.resize_longest_image_size(orig_im_size, img_size)
        masks = masks[..., : int(prepadded_size[0]), : int(prepadded_size[1])]
        orig_im_size = orig_im_size.to(torch.int64)
        h, w = orig_im_size[0], orig_im_size[1]
        masks = F.interpolate(masks, size=(h, w), mode="bilinear", align_corners=False)
        return masks

    def get_preprocess_shape(self, oldh: int, oldw: int, long_side_length: int):
        """
        Compute the output size given input size and target long side length.
        """
        scale = long_side_length * 1.0 / max(oldh, oldw)
        newh, neww = oldh * scale, oldw * scale
        neww = int(neww + 0.5)
        newh = int(newh + 0.5)
        return (newh, neww)

    def apply_coords(self, coords, original_size, new_size):
        old_h, old_w = original_size
        new_h, new_w = new_size
        coords = deepcopy(coords).astype(float)
        coords[..., 0] = coords[..., 0] * (new_w / old_w)
        coords[..., 1] = coords[..., 1] * (new_h / old_h)
        return coords

    def apply_boxes(self, boxes, original_size, new_size):
        boxes = self.apply_coords(boxes.reshape(-1, 2, 2), original_size, new_size)
        return boxes

    def predict(self, image, input_points):

        raw_img = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        origin_image_size = raw_img.shape[:2]


        if self.model_type in ["l0", "l1", "l2"]:
            img = self.preprocess(raw_img, img_size=512, device="cuda")
        elif self.model_type in ["xl0", "xl1"]:
            img = self.preprocess(raw_img, img_size=1024, device="cuda")

        # trt encoder inference
        image_embedding = self.trt_encoder(img)
        image_embedding = image_embedding[0].reshape(1, 256, 64, 64)

        input_size = self.get_preprocess_shape(*origin_image_size, long_side_length=1024)

        # input_point [[[720,726,1],[742,678,1],[1232,816,1]]]
        point = np.array(input_points, dtype=np.float32)

        point_coords = point[..., :2].transpose(1,0,2)
        point_labels = point[..., 2].transpose(1,0)

        point_coords = self.apply_coords(point_coords, origin_image_size, input_size).astype(np.float32)

        inputs = (image_embedding, torch.from_numpy(point_coords).to("cuda"), torch.from_numpy(point_labels).to("cuda"))
        assert all([x.dtype == torch.float32 for x in inputs])

        # trt decoder inference
        low_res_masks, _ = self.trt_decoder(*inputs)
        low_res_masks = low_res_masks.reshape(1, -1, 256, 256)

        # masks [[[-1 0.95 0.46][-1 0.95 0.46]], [[-1 0.95 0.46][-1 0.95 0.46]]]
        masks = self.mask_postprocessing(low_res_masks, origin_image_size)[0]
        masks = masks.cpu().numpy()
        return masks
