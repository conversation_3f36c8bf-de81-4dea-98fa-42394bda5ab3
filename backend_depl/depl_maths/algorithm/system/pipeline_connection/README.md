## 卸料管连接识别算法

## 版本:

 v 1.0.0

## 描述:

 该算法用于识别罐车的卸料管和管道是否连接。

## 输入:

| 名称       | 类型    | 描述         |
| -------- | ----- | ---------- |
| 管道固定位置点1 | point | 需要检测的区域点   |
| 管道固定位置点2 | point | 需要检测的区域点   |
| 边界线      | line  | 用于判断连接的边界线 |

* 是否支持单图批量识别：否

## 输出：

| 名称   | 类型     | 描述                       |
| ---- | ------ | ------------------------ |
| 识别位置 | square | 矩形,item为4个point，代表管道位置坐标 |
| 是否连接 | string | 字符串，代表管道是否连接             |
| 置信度  | number | 数值，0-1之间，代表识别出来目标的可信度    |