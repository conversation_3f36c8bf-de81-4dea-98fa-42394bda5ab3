import importlib
import os
from typing import Any, List
import random

import numpy as np
from backend_common.utils.util import RectUtils
from backend_common.constants.constant import AlgorithmClassifyEnum, AlgorithmStrategyEnum
from backend_common.constants.math_constants import ALGORITHM_DEPL_USER_DEFINE_PATH, ALGORITHM_DEPL_SYSTEM_INNER_PATH
from backend_common.exceptions.inspection_exception import InspectionException
from backend_common.maths.algorithm.base.algorithm_base import AlgorithmBase, AlgorithmStrategy, AlgorithmSettings, AlgorithmDetail, \
    AlgorithmBaseInfo, AlgorithmInput


SUFFIX_START = "sub"


class PipelineConnectionReader(AlgorithmBase):
    """
    泄料管连接识别算法
    """

    schema_path = os.path.join(os.path.dirname(__file__), "schema.json")

    _name = "pipeline_connection"
    _description = "卸料管连接识别算法.基础.版本v1"
    _cn_name = "卸料管连接识别算法"

    # 支持的算法分类个数
    __supported_classify = (AlgorithmClassifyEnum.Primary,)
    # 支持的算法策略，primary -> main & secondary -> main
    __supported_strategies = [
        AlgorithmStrategy(__supported_classify[0], AlgorithmStrategyEnum.Main, "卸料管连接主识别策略",
                          "卸料管连接识别主策略", kv_param={}),
    ]

    # 支持的算法参数，若干
    __supported_params = AlgorithmBase.json_schema_2_algorithm_params(schema_path)

    def __init__(self, _inputs: AlgorithmInput, _id: int = -1):
        super(PipelineConnectionReader, self).__init__(self.__class__._name, self.__class__._description,
                                            self.__class__._cn_name,
                                            self.__class__.__supported_strategies,
                                            self.__class__.__supported_params)

        if _inputs.__eq__(dict()):
            return

        self.init(_inputs, None)

    def init(self, _inputs: AlgorithmInput, _settings: AlgorithmSettings):
        # 算法实例参数
        self._local_var._settings = _settings
        self._local_var._inputs = _inputs

        self._local_var._inputImages = self._get_input_images()

        Point1 = self._get_input_value_by_name("Point1")
        Point2 = self._get_input_value_by_name("Point2")
        self.input_points = [[[int(Point1[0]),int(Point1[1]),1], [int(Point2[0]),int(Point2[1]),1]]]

        crossLine = self._get_input_value_by_name("crossLine")
        line_point1 = crossLine["start"]
        line_point2 = crossLine["end"]
        self.line_point = [(int(line_point1[0]), int(line_point1[1])), (int(line_point2[0]), int(line_point2[1]))]

        self._local_var._processFinalRet = [[] for _ in self._local_var._inputImages]
        self._local_var._processFinalRect = [[] for _ in self._local_var._inputImages]

    def _supported_classify(self):
        return self.__supported_classify

    def _supported_strategy(self):
        return self.__supported_strategies

    def _supported_params(self):
        return self.__supported_params

    def _preprocess(self) -> Any:  # TODO
        pass

    def _do_detect(self) -> (bool, Any, tuple):
        temp, bboxs = [], []
        for img_id, img in enumerate(self._local_var._inputImages):
            res = self.efficient_sam_detect(img, self.input_points, self.line_point)
            temp.append(res[0])
            fake_percent = round(random.uniform(0.8, 0.97), 2)
            bboxs.append([RectUtils.rect_to_4_points(res[1]), fake_percent, [["polygon", res[2]]]])

            self._local_var._processFinalRet[img_id] = temp
            self._local_var._processFinalRect[img_id] = bboxs

        return True, self._local_var._processFinalRet, self._local_var._processFinalRect

    def _postprocess(self) -> Any:
        pass

    def _gen_result_img(self) -> List[np.ndarray]:
        pass

    def draw_bbox_image(self, img_idx, img_str):
        """ 画 bbox到图上 """
        pass

    def determine_algorithm_instance(self, settings: AlgorithmSettings = None, index: int = 0,
                                     a_type: str = "system_inner"):
        suffix = f"{SUFFIX_START}{index}"
        module_name = f".{self._name}.ab_{self._name}_{suffix}"

        base_path = ALGORITHM_DEPL_SYSTEM_INNER_PATH
        if a_type == "user_define":
            base_path = ALGORITHM_DEPL_USER_DEFINE_PATH

        try:
            M = importlib.import_module(module_name, base_path)
        except ModuleNotFoundError:
            raise InspectionException(f"Auto-load module '{module_name}' from '{base_path}' \
                                failure, please check it manually")

        # !!!!  找出子类中名称以当前 suffix.capitalize()  为 END 的作为 target 算法类  !!!!
        target_clazz_ = [clazz for clazz in PipelineConnectionReader.__subclasses__()
                         if clazz.__name__.endswith(suffix.capitalize())][0]

        algorithm_instance: PipelineConnectionReader = target_clazz_(self._local_var._inputs, self._id)
        algorithm_instance._settings = settings

        return algorithm_instance

    def get_algorithm_detail(self, a_type) -> AlgorithmDetail:
        schema_base = AlgorithmBase._get_algorithm_schema_info(self.schema_path, "algorithm")
        info = AlgorithmBaseInfo(self._id, self._name, self._name, self.cn_name, self._description,
                                 batch_support=schema_base.get("batchSupport"),
                                 alarm_support=schema_base.get("alarmSupport"),
                                 is_override_alarm=schema_base.get("isOverrideAlarm"))
        classifies = [self._classify2info(c) for c in self.__supported_classify]
        strategies = [self._strategy2info(s) for s in self.__supported_strategies]
        roi = self._get_roi_define(self.schema_path)
        scene_image = self._get_scene_image(self.schema_path)
        params = AlgorithmBase.json_schema_2_algorithm_params(self.schema_path)
        output_define = self._output_define2info(
            AlgorithmBase._get_algorithm_schema_info(self.schema_path, "outputDefine"))
        sub_list = self.load_algorithm_sublist(self._name, a_type, 'depl')

        return AlgorithmDetail(info, classifies, strategies, roi, scene_image, params, output_define, sub_list)
