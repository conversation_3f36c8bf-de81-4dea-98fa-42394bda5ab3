{"algorithm": {"name": "卸料管连接识别", "code": "pipeline_connection", "des": "卸料管连接识别算法.V1", "type": "DeepLearning", "alarmSupport": true, "isOverrideAlarm": false, "batchSupport": true, "resultShowType": "ALARM"}, "images": ["e:/images/test.jpg"], "roi": {"name": "ROI区域", "type": "polygon", "des": "支持绘制的ROI区域类型, polygon:多边形, circle:圆, ellipse:椭圆", "value": [[325, 116], [331, 115], [346, 310], [337, 310]], "nullable": true}, "targetSceneImage": {"in_use": false, "uri": "", "name": "场景基准图"}, "inputParam": {"Point1": {"name": "管道固定位置点1", "type": "point", "value": [123, 456], "showable": true, "desc": "一个point对象,点的坐标"}, "Point2": {"name": "管道固定位置点2", "type": "point", "value": [123, 456], "showable": true, "desc": "一个point对象,点的坐标"}, "crossLine": {"name": "边界线", "type": "line", "value": {"start": [100, 200], "end": [100, 200]}, "showable": true, "desc": "直线的两个点"}}, "outputDefine": {"type": "string", "desc": "卸料管连接识别", "val_enum": {"connected": 0, "not_connected": 1}, "ruleInfo": [{"desc": "检测到目标", "isBind": false, "type": "HH", "val": 0.0, "placeHolder": "数值大于高"}, {"desc": "检测到目标", "isBind": true, "type": "H", "val": 1.0, "placeHolder": "数值小于高高"}, {"desc": "检测到目标", "isBind": false, "type": "L", "val": 0.0, "placeHolder": "数值小于高"}, {"desc": "检测到目标", "isBind": false, "type": "LL", "val": 0.0, "placeHolder": "数值小于低"}]}}