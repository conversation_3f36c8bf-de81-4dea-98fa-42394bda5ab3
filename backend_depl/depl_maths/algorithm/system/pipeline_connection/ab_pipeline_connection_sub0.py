import os
import numpy as np
import cv2
from typing import Any, List

from backend_common.maths.algorithm.base.algorithm_base import AlgorithmInput
from backend_common.exceptions.inspection_exception import AlgorithmProcessException
from depl_maths.algorithm.system.pipeline_connection.ab_pipeline_connection import PipelineConnectionReader
from depl_maths.algorithm.system.pipeline_connection.efficientvit_sam_trt import EfficientSamDet


class PipelineConnectionReaderSub0(PipelineConnectionReader):
    """
    卸料管连接识别算法
    """

    _index = 0
    _description = "卸料管连接识别算法.主模型.版本v1"
    _cn_name = "卸料管连接识别算法"

    def __init__(self, _inputs: AlgorithmInput, _id: int = -1):
        super(PipelineConnectionReaderSub0, self).__init__(_inputs, _id)

        encoder_path = os.path.join(os.path.dirname(__file__), 'inference_model/xl0_encoder.engine')
        decoder_path = os.path.join(os.path.dirname(__file__), 'inference_model/xl0_decoder.engine')
        model_type = "xl0"

        self.efficient_sam = EfficientSamDet(encoder_path, decoder_path, model_type)

    def _postprocess(self) -> Any:
        pass

    def get_contours(self, cnt):
        cnt_list = cnt.tolist()

        res = []
        for i in cnt_list:
            info = i[0]
            res.append(info)

        res = res[::4]
        return res

    def efficient_sam_detect(self, ori_img, input_points, line_point):
        masks = self.efficient_sam.predict(ori_img, input_points)

        if (len(masks) == 0):
            print("未识别到有效分割区域，算法执行失败")
            raise AlgorithmProcessException("未识别到有效分割区域，算法执行失败")

        masks[masks > 0.0] = 255
        masks[masks <= 0.0] = 0
        masks = np.array(masks, dtype=np.uint8)

        if len(masks) >= 2:
            for i in range(len(masks)):
                if i == 0:
                    all_maks = masks[0]
                else:
                    all_maks = cv2.add(all_maks, masks[i])
        else:
            all_maks = masks[0]

        # 图像执行闭运算
        kernel = np.ones((3, 3), dtype=np.uint8)
        all_maks = cv2.morphologyEx(all_maks, cv2.MORPH_CLOSE, kernel)

        # 寻找最大轮廓
        contours, _ = cv2.findContours(all_maks, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_NONE)
        cnts = sorted(contours, key=cv2.contourArea, reverse=True)

        cnt_box = cv2.boundingRect(cnts[0])

        # 绘制轮廓和直线图像
        black_bg_img = np.zeros(ori_img.shape[0:3])
        line_img = cv2.line(black_bg_img.copy(), line_point[0], line_point[1], (255, 255, 255), 3)
        cnt_img = cv2.drawContours(black_bg_img.copy(), cnts, 0, (255, 255, 255), -1)

        # 两个图像按位与
        bitwiseAnd = cv2.bitwise_and(line_img, cnt_img)

        # 处理mask轮廓，隔4个保存一个点
        cnt_list = self.get_contours(cnts[0])

        if len(bitwiseAnd[bitwiseAnd == 255]) > 0:
            return ['管道已连接', cnt_box, cnt_list]
        else:
            return ['管道未连接', cnt_box, cnt_list]

    def _alarm_trig(self, detect_ret) -> List[List]:
        return [[(True, "H", "卸料管已连接") if detect_ret[0] == "管道已连接"
                 else (False, None, None)] for detect_ret in detect_ret[1]]
