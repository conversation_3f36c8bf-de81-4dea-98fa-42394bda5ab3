#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Python加密模块导入钩子
在运行时自动解密并导入加密的Python模块
"""

import sys
import os
import importlib.util
import importlib.machinery
from importlib.abc import Loader, MetaPathFinder
import types
from crypto_utils import get_crypto_instance


class EncryptedModuleLoader(Loader):
    """加密模块加载器"""
    
    def __init__(self, fullname, path, crypto_instance):
        self.fullname = fullname
        self.path = path
        self.crypto = crypto_instance
    
    def create_module(self, spec):
        """创建模块对象"""
        return None  # 使用默认模块创建
    
    def exec_module(self, module):
        """执行模块代码"""
        try:
            # 读取加密文件
            with open(self.path, 'r', encoding='utf-8') as f:
                encrypted_data = f.read()
            
            # 解密代码
            decrypted_code = self.crypto.decrypt_data(encrypted_data)
            code_str = decrypted_code.decode('utf-8')
            
            # 编译并执行代码
            code_obj = compile(code_str, self.path, 'exec')
            module.__file__ = self.path
            module.__loader__ = self
            exec(code_obj, module.__dict__)
            
        except Exception as e:
            raise ImportError(f"无法加载加密模块 {self.fullname}: {str(e)}")


class EncryptedModuleFinder(MetaPathFinder):
    """加密模块查找器"""
    
    def __init__(self, package_name="ppocr", version="1.0.0"):
        self.package_name = package_name
        self.crypto = get_crypto_instance(package_name, version)
        self.package_path = None
        
    def find_spec(self, fullname, path, target=None):
        """查找模块规范"""
        # 只处理当前包内的模块
        if not fullname.startswith(self.package_name):
            return None
            
        # 获取包路径
        if self.package_path is None:
            self.package_path = self._get_package_path()
            
        if self.package_path is None:
            return None
            
        # 构建加密文件路径
        module_parts = fullname.split('.')
        if len(module_parts) == 1:
            # 顶级包
            encrypted_file = os.path.join(self.package_path, '__init__.pye')
        else:
            # 子模块
            relative_parts = module_parts[1:]  # 去掉包名
            if len(relative_parts) == 1:
                # 直接子模块
                encrypted_file = os.path.join(self.package_path, f"{relative_parts[0]}.pye")
            else:
                # 嵌套子模块
                subdir = os.path.join(self.package_path, *relative_parts[:-1])
                encrypted_file = os.path.join(subdir, f"{relative_parts[-1]}.pye")
        
        # 检查加密文件是否存在
        if not os.path.exists(encrypted_file):
            return None
            
        # 创建模块规范
        loader = EncryptedModuleLoader(fullname, encrypted_file, self.crypto)
        spec = importlib.machinery.ModuleSpec(fullname, loader, origin=encrypted_file)
        
        return spec
    
    def _get_package_path(self):
        """获取包的安装路径"""
        try:
            # 尝试导入包以获取路径
            spec = importlib.util.find_spec(self.package_name)
            if spec and spec.origin:
                return os.path.dirname(spec.origin)
        except ImportError:
            pass
            
        # 在sys.path中搜索
        for path in sys.path:
            package_dir = os.path.join(path, self.package_name)
            if os.path.isdir(package_dir):
                return package_dir
                
        return None


def install_import_hook(package_name="ppocr", version="1.0.0"):
    """
    安装导入钩子
    
    Args:
        package_name (str): 包名
        version (str): 版本号
    """
    # 检查是否已经安装
    for finder in sys.meta_path:
        if isinstance(finder, EncryptedModuleFinder) and finder.package_name == package_name:
            return  # 已经安装
    
    # 创建并安装查找器
    finder = EncryptedModuleFinder(package_name, version)
    sys.meta_path.insert(0, finder)
    
    print(f"已安装加密模块导入钩子: {package_name} v{version}")


def uninstall_import_hook(package_name="ppocr"):
    """
    卸载导入钩子
    
    Args:
        package_name (str): 包名
    """
    # 查找并移除对应的查找器
    to_remove = []
    for finder in sys.meta_path:
        if isinstance(finder, EncryptedModuleFinder) and finder.package_name == package_name:
            to_remove.append(finder)
    
    for finder in to_remove:
        sys.meta_path.remove(finder)
        print(f"已卸载加密模块导入钩子: {package_name}")


# 自动安装钩子（当模块被导入时）
def auto_install_hook():
    """自动安装导入钩子"""
    # 从当前模块路径推断包名
    current_file = __file__
    package_dir = os.path.dirname(current_file)
    package_name = os.path.basename(package_dir)
    
    # 安装钩子
    install_import_hook(package_name, "1.0.0")


if __name__ == "__main__":
    # 测试代码
    print("测试加密模块导入钩子...")
    install_import_hook("ppocr", "1.0.0")
    print("导入钩子安装完成")
    
    # 显示当前的meta_path
    print("当前meta_path:")
    for i, finder in enumerate(sys.meta_path):
        print(f"  {i}: {type(finder).__name__}")
