#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
简化的加密功能测试脚本
测试核心的加密解密功能
"""

import os
import sys
import tempfile

def test_crypto_basic():
    """测试基本的加密解密功能"""
    print("=" * 50)
    print("测试1: 基本加密解密功能")
    print("=" * 50)
    
    try:
        from crypto_utils import get_crypto_instance
        
        crypto = get_crypto_instance("ppocr", "1.0.0")
        
        # 测试数据
        test_code = '''#!/usr/bin/env python
# -*- coding: utf-8 -*-

def hello_world():
    """测试函数"""
    return "Hello, World from encrypted code!"

class TestClass:
    def __init__(self, name):
        self.name = name
    
    def greet(self):
        return f"Hello, {self.name}!"

if __name__ == "__main__":
    print(hello_world())
    obj = TestClass("Encrypted Module")
    print(obj.greet())
'''
        
        print("原始代码长度:", len(test_code))
        
        # 加密
        encrypted_data = crypto.encrypt_data(test_code)
        print(f"加密数据长度: {len(encrypted_data)} 字符")
        print(f"加密数据预览: {encrypted_data[:50]}...")
        
        # 解密
        decrypted_data = crypto.decrypt_data(encrypted_data)
        decrypted_code = decrypted_data.decode('utf-8')
        
        print(f"解密数据长度: {len(decrypted_code)} 字符")
        success = test_code == decrypted_code
        print("解密验证:", "成功" if success else "失败")
        
        return success
        
    except Exception as e:
        print(f"测试失败: {e}")
        return False


def test_file_encryption():
    """测试文件加密解密功能"""
    print("\n" + "=" * 50)
    print("测试2: 文件加密解密功能")
    print("=" * 50)
    
    try:
        from crypto_utils import get_crypto_instance
        
        crypto = get_crypto_instance("ppocr", "1.0.0")
        
        # 创建临时测试文件
        test_content = '''def test_function():
    """这是一个测试函数"""
    return "文件加密测试成功!"

def another_function(x, y):
    """另一个测试函数"""
    return x + y

class TestClass:
    def __init__(self):
        self.value = "测试值"
    
    def get_value(self):
        return self.value

print(test_function())
print(another_function(1, 2))
obj = TestClass()
print(obj.get_value())
'''
        
        # 创建临时文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False, encoding='utf-8') as f:
            f.write(test_content)
            test_file = f.name
        
        try:
            print(f"原始文件: {test_file}")
            print(f"原始内容长度: {len(test_content)} 字符")
            
            # 加密文件
            encrypted_file = test_file.replace('.py', '.pye')
            crypto.encrypt_file(test_file, encrypted_file)
            
            print(f"加密文件: {encrypted_file}")
            
            # 验证加密文件存在
            if not os.path.exists(encrypted_file):
                print("错误: 加密文件未创建")
                return False
            
            # 检查加密文件大小
            encrypted_size = os.path.getsize(encrypted_file)
            print(f"加密文件大小: {encrypted_size} 字节")
            
            # 解密文件
            decrypted_file = test_file.replace('.py', '_decrypted.py')
            crypto.decrypt_file(encrypted_file, decrypted_file)
            
            print(f"解密文件: {decrypted_file}")
            
            # 验证解密结果
            with open(decrypted_file, 'r', encoding='utf-8') as f:
                decrypted_content = f.read()
            
            print(f"解密内容长度: {len(decrypted_content)} 字符")
            
            success = test_content == decrypted_content
            print("文件加密解密验证:", "成功" if success else "失败")
            
            return success
            
        finally:
            # 清理临时文件
            for file in [test_file, encrypted_file, decrypted_file]:
                if os.path.exists(file):
                    os.remove(file)
                    
    except Exception as e:
        print(f"测试失败: {e}")
        return False


def test_setup_integration():
    """测试setup.py集成"""
    print("\n" + "=" * 50)
    print("测试3: setup.py集成测试")
    print("=" * 50)
    
    try:
        # 检查必要文件是否存在
        current_dir = os.path.dirname(__file__)
        required_files = [
            'setup.py',
            'crypto_utils.py',
            'import_hook.py',
            'build_encrypted.py'
        ]
        
        missing_files = []
        for file in required_files:
            file_path = os.path.join(current_dir, file)
            if not os.path.exists(file_path):
                missing_files.append(file)
            else:
                print(f"✓ 找到文件: {file}")
        
        if missing_files:
            print(f"✗ 缺少必要文件: {missing_files}")
            return False
        
        print("✓ 所有必要文件都存在")
        
        # 测试导入构建模块
        try:
            from build_encrypted import EncryptedBuildPy, EncryptCommand
            print("✓ 成功导入构建模块")
        except ImportError as e:
            print(f"✗ 导入构建模块失败: {e}")
            return False
        
        # 测试导入钩子模块
        try:
            from import_hook import install_import_hook, uninstall_import_hook
            print("✓ 成功导入钩子模块")
        except ImportError as e:
            print(f"✗ 导入钩子模块失败: {e}")
            return False
        
        print("✓ 所有模块导入成功")
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        return False


def run_simple_tests():
    """运行简化的测试套件"""
    print("开始运行简化的加密功能测试...")
    print("当前工作目录:", os.getcwd())
    print("Python版本:", sys.version)
    
    tests = [
        ("基本加密解密", test_crypto_basic),
        ("文件加密解密", test_file_encryption),
        ("setup.py集成", test_setup_integration),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"测试 {test_name} 出现异常: {e}")
            results.append((test_name, False))
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("测试结果汇总")
    print("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "通过" if result else "失败"
        print(f"{test_name:20} : {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 个测试通过")
    
    if passed == len(results):
        print("\n🎉 所有测试通过！加密方案实现成功！")
    else:
        print(f"\n⚠️  有 {len(results) - passed} 个测试失败，请检查实现。")
    
    return passed == len(results)


if __name__ == "__main__":
    success = run_simple_tests()
    sys.exit(0 if success else 1)
