#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
增强安全的代码加密解密工具模块
提供更安全的密钥管理和代码保护机制
"""

import os
import hashlib
import base64
import platform
import ctypes
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.primitives import padding
from cryptography.hazmat.backends import default_backend
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from cryptography.hazmat.primitives import hashes


class SecureCodeCrypto:
    """增强安全的代码加密解密类"""
    
    def __init__(self, package_name="ppocr", version="1.0.0"):
        self.package_name = package_name
        self.version = version
        self._key = None
        self._salt = None
        
    def _get_hardware_fingerprint(self):
        """获取硬件指纹（更难伪造）"""
        try:
            # CPU信息
            import cpuinfo
            cpu_info = cpuinfo.get_cpu_info()
            cpu_brand = cpu_info.get('brand_raw', '')
        except:
            cpu_brand = platform.processor()
        
        # 系统信息
        system_info = f"{platform.system()}{platform.release()}{platform.machine()}"
        
        # MAC地址（更稳定的硬件标识）
        import uuid
        mac_addr = ':'.join(['{:02x}'.format((uuid.getnode() >> elements) & 0xff) 
                            for elements in range(0,2*6,2)][::-1])
        
        # 组合硬件指纹
        fingerprint = f"{cpu_brand}|{system_info}|{mac_addr}"
        return hashlib.sha256(fingerprint.encode()).hexdigest()
    
    def _derive_key_from_external_source(self):
        """从外部源派生密钥（可以是服务器、硬件令牌等）"""
        # 方案1：环境变量密钥
        env_key = os.environ.get('PPOCR_MASTER_KEY')
        if env_key:
            return base64.b64decode(env_key.encode())
        
        # 方案2：硬件指纹 + 隐藏的种子
        hardware_fp = self._get_hardware_fingerprint()
        
        # 使用更复杂的种子（可以从配置文件、注册表等获取）
        hidden_seeds = [
            self.package_name,
            self.version,
            hardware_fp,
            # 可以添加更多隐藏的种子源
            self._get_system_specific_seed(),
        ]
        
        # 使用PBKDF2派生密钥
        combined_seed = '|'.join(hidden_seeds).encode()
        salt = hashlib.sha256(f"{self.package_name}_salt".encode()).digest()[:16]
        
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,  # 增加计算成本
            backend=default_backend()
        )
        
        return kdf.derive(combined_seed)
    
    def _get_system_specific_seed(self):
        """获取系统特定的种子（增加逆向难度）"""
        try:
            # Windows: 从注册表获取
            if platform.system() == 'Windows':
                import winreg
                try:
                    key = winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, 
                                       r"SOFTWARE\Microsoft\Cryptography")
                    value, _ = winreg.QueryValueEx(key, "MachineGuid")
                    winreg.CloseKey(key)
                    return value
                except:
                    pass
            
            # Linux: 从machine-id获取
            elif platform.system() == 'Linux':
                try:
                    with open('/etc/machine-id', 'r') as f:
                        return f.read().strip()
                except:
                    pass
            
            # 备用方案
            return platform.node()
            
        except:
            return "fallback_seed"
    
    @property
    def key(self):
        """延迟加载密钥"""
        if self._key is None:
            self._key = self._derive_key_from_external_source()
        return self._key
    
    def _secure_encrypt_data(self, data):
        """安全加密数据"""
        if isinstance(data, str):
            data = data.encode('utf-8')
            
        # 生成随机IV
        iv = os.urandom(16)
        
        # 创建加密器
        cipher = Cipher(algorithms.AES(self.key), modes.CBC(iv), backend=default_backend())
        encryptor = cipher.encryptor()
        
        # 填充并加密数据
        padder = padding.PKCS7(128).padder()
        padded_data = padder.update(data) + padder.finalize()
        encrypted_data = encryptor.update(padded_data) + encryptor.finalize()
        
        # 添加完整性校验
        hmac_key = hashlib.sha256(self.key + b"hmac").digest()
        import hmac
        mac = hmac.new(hmac_key, iv + encrypted_data, hashlib.sha256).digest()
        
        # 组合: MAC + IV + 加密数据
        combined_data = mac + iv + encrypted_data
        return base64.b64encode(combined_data).decode('utf-8')
    
    def _secure_decrypt_data(self, encrypted_data):
        """安全解密数据"""
        try:
            # Base64解码
            combined_data = base64.b64decode(encrypted_data.encode('utf-8'))
            
            # 分离MAC、IV和加密数据
            mac = combined_data[:32]
            iv = combined_data[32:48]
            encrypted_bytes = combined_data[48:]
            
            # 验证完整性
            hmac_key = hashlib.sha256(self.key + b"hmac").digest()
            import hmac
            expected_mac = hmac.new(hmac_key, iv + encrypted_bytes, hashlib.sha256).digest()
            
            if not hmac.compare_digest(mac, expected_mac):
                raise ValueError("数据完整性校验失败")
            
            # 解密
            cipher = Cipher(algorithms.AES(self.key), modes.CBC(iv), backend=default_backend())
            decryptor = cipher.decryptor()
            
            padded_data = decryptor.update(encrypted_bytes) + decryptor.finalize()
            
            # 移除填充
            unpadder = padding.PKCS7(128).unpadder()
            data = unpadder.update(padded_data) + unpadder.finalize()
            
            return data
            
        except Exception as e:
            raise ValueError(f"解密失败: {str(e)}")
    
    def encrypt_data(self, data):
        """公共加密接口"""
        return self._secure_encrypt_data(data)
    
    def decrypt_data(self, encrypted_data):
        """公共解密接口"""
        return self._secure_decrypt_data(encrypted_data)
    
    def encrypt_file(self, input_file, output_file):
        """加密文件"""
        try:
            with open(input_file, 'rb') as f:
                data = f.read()
            
            encrypted_data = self.encrypt_data(data)
            
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(encrypted_data)
                
            print(f"文件加密完成: {input_file} -> {output_file}")
            
        except Exception as e:
            print(f"文件加密失败 {input_file}: {str(e)}")
            raise
    
    def decrypt_file(self, encrypted_file, output_file):
        """解密文件"""
        try:
            with open(encrypted_file, 'r', encoding='utf-8') as f:
                encrypted_data = f.read()
            
            decrypted_data = self.decrypt_data(encrypted_data)
            
            with open(output_file, 'wb') as f:
                f.write(decrypted_data)
                
            print(f"文件解密完成: {encrypted_file} -> {output_file}")
            
        except Exception as e:
            print(f"文件解密失败 {encrypted_file}: {str(e)}")
            raise
    
    def __del__(self):
        """清理敏感数据"""
        if hasattr(self, '_key') and self._key:
            # 尝试清零内存中的密钥
            try:
                if isinstance(self._key, bytes):
                    # 使用ctypes清零内存
                    ctypes.memset(id(self._key) + 32, 0, len(self._key))
            except:
                pass
            self._key = None


def get_secure_crypto_instance(package_name="ppocr", version="1.0.0"):
    """获取安全加密实例的工厂函数"""
    return SecureCodeCrypto(package_name, version)


if __name__ == "__main__":
    # 测试代码
    crypto = SecureCodeCrypto()
    
    # 测试数据加密解密
    test_data = "print('Hello, Secure World!')\ndef secure_function():\n    return 'This is securely encrypted'"
    print("原始数据:", test_data[:50] + "...")
    
    encrypted = crypto.encrypt_data(test_data)
    print("加密数据:", encrypted[:50] + "...")
    
    decrypted = crypto.decrypt_data(encrypted)
    print("解密验证:", "成功" if test_data == decrypted.decode('utf-8') else "失败")
