# Python 代码加解密方案使用说明

## 概述

本方案为 Python 包提供了完整的代码加密保护功能，在 setup.py 打包过程中对源代码进行 AES 加密，并在运行时自动解密执行。该方案确保代码在分发时得到保护，同时保持正常的 pip 安装和使用流程。

## 功能特性

- ✅ **AES-256-CBC 加密**：使用工业级加密算法保护源代码
- ✅ **自动解密导入**：运行时通过导入钩子自动解密并执行代码
- ✅ **无缝集成**：完全兼容标准的 pip 安装流程
- ✅ **确定性密钥**：基于包信息生成一致的加密密钥
- ✅ **选择性加密**：可配置哪些文件需要加密保护
- ✅ **错误处理**：完善的异常处理和错误提示

## 文件结构

```
backend_depl/depl_maths/algorithm/system/
├── crypto_utils.py          # 加密解密工具模块
├── import_hook.py           # 运行时导入钩子
├── build_encrypted.py       # 构建时加密脚本
├── setup.py                 # 修改后的安装脚本
├── test_encryption.py       # 测试验证脚本
├── README_ENCRYPTION.md     # 使用说明文档
└── ppocr/
    ├── __init__.py          # 修改后的包初始化文件
    └── ...                  # 其他源代码文件
```

## 使用方法

### 1. 安装依赖

首先确保安装了必要的依赖包：

```bash
pip install cryptography>=3.0.0
```

### 2. 加密构建

使用以下命令进行加密构建：

```bash
# 构建加密的 wheel 包
python setup.py build_py_encrypted bdist_wheel --dist-dir dist

# 构建加密的源码包
python setup.py build_py_encrypted sdist --dist-dir dist --formats=gztar

# 独立加密命令（用于测试）
python setup.py encrypt --package-name=ppocr --version=1.0.0
```

### 3. 安装和使用

安装过程与标准 Python 包完全相同：

```bash
# 安装 wheel 包
pip install dist/ppocr-1.0.0-py3-none-any.whl

# 安装源码包
pip install dist/ppocr-1.0.0.tar.gz

# 指定安装目录
pip install dist/ppocr-1.0.0.tar.gz --target /path/to/target
```

### 4. 代码使用

使用方式与普通 Python 包完全相同：

```python
# 导入包（自动安装导入钩子）
import ppocr

# 使用包的功能
reader = ppocr.PpocrReader()
result = reader.process_image("image.jpg")
```

## 技术原理

### 加密过程

1. **构建时加密**：在 `python setup.py build_py_encrypted` 时触发
2. **文件遍历**：扫描包目录中的所有 `.py` 文件
3. **选择性加密**：跳过 `__init__.py`、`setup.py` 等关键文件
4. **AES 加密**：使用 AES-256-CBC 算法加密源代码
5. **文件替换**：将 `.py` 文件替换为 `.pye` 加密文件

### 解密过程

1. **导入钩子注册**：包的 `__init__.py` 自动注册导入钩子
2. **模块查找**：拦截对 `.pye` 文件的导入请求
3. **自动解密**：读取并解密 `.pye` 文件内容
4. **代码执行**：编译并执行解密后的 Python 代码

### 密钥生成

密钥基于以下信息生成，确保在相同环境下的一致性：
- 包名 (package_name)
- 版本号 (version)
- 机器标识 (platform.node() + platform.machine())
- 固定盐值 ("hollysys_2023")

## 配置选项

### setup.py 配置

```python
# 修改包数据以包含加密文件
package_data={
    block_name: ['*.py', '*.pye', '*.json', '*.yml', 'inference_model/*'],
},

# 添加自定义构建命令
cmdclass={
    'build_py_encrypted': EncryptedBuildPy,
    'encrypt': EncryptCommand,
},

# 添加加密依赖
install_requires=[
    'cryptography>=3.0.0',
],
setup_requires=[
    'cryptography>=3.0.0',
],
```

### 加密选项

可以在 `build_encrypted.py` 中配置哪些文件需要跳过加密：

```python
# 跳过某些不需要加密的文件
if filename in ['__init__.py', 'setup.py', 'crypto_utils.py', 'import_hook.py', 'build_encrypted.py']:
    print(f"跳过文件: {py_file}")
    return
```

## 测试验证

运行测试脚本验证加密功能：

```bash
cd backend_depl/depl_maths/algorithm/system/
python test_encryption.py
```

测试包括：
- 基本加密解密功能测试
- 文件加密解密测试
- 导入钩子功能测试
- 构建过程测试

## 安全考虑

### 优势
- **代码保护**：源代码在分发时完全加密
- **透明使用**：用户使用时无需关心加密细节
- **兼容性好**：完全兼容标准 Python 生态

### 限制
- **密钥安全**：密钥生成算法是确定性的，高级攻击者可能逆向
- **运行时解密**：代码在内存中会被解密，可能被调试工具获取
- **性能影响**：首次导入时需要解密，会有轻微性能开销

### 增强建议
- 使用环境变量或外部密钥服务提供密钥
- 添加代码混淆层增加逆向难度
- 实现运行时环境检查防止调试

## 故障排除

### 常见问题

1. **导入失败**
   ```
   ImportError: 无法加载加密模块
   ```
   - 检查 cryptography 包是否正确安装
   - 确认导入钩子是否正确注册

2. **解密失败**
   ```
   ValueError: 解密失败
   ```
   - 检查密钥生成是否一致
   - 确认加密文件是否完整

3. **构建失败**
   ```
   ModuleNotFoundError: No module named 'build_encrypted'
   ```
   - 确认所有加密模块文件都在正确位置
   - 检查 setup.py 中的导入语句

### 调试方法

1. **启用详细输出**
   ```bash
   python setup.py build_py_encrypted --verbose
   ```

2. **测试单个组件**
   ```python
   from crypto_utils import get_crypto_instance
   crypto = get_crypto_instance("ppocr", "1.0.0")
   # 测试加密解密
   ```

3. **检查导入钩子**
   ```python
   import sys
   print([type(finder).__name__ for finder in sys.meta_path])
   ```

## 版本兼容性

- **Python**: 3.6+
- **cryptography**: 3.0.0+
- **setuptools**: 40.0.0+

## 更新日志

- **v1.0.0**: 初始版本，支持基本的代码加密和自动解密功能
