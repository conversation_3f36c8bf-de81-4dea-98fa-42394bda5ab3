import importlib
import os
from typing import Any, List

import cv2
import numpy as np
from backend_common.utils.util import RectUtils, get_image

from backend_common.constants.constant import AlgorithmClassifyEnum, AlgorithmStrategyEnum
from backend_common.constants.math_constants import ALGORITHM_DEPL_USER_DEFINE_PATH, ALGORITHM_DEPL_SYSTEM_INNER_PATH
from backend_common.exceptions.inspection_exception import InspectionException
from backend_common.maths.algorithm.base.algorithm_base import AlgorithmBase, AlgorithmStrategy, AlgorithmSettings, AlgorithmDetail, \
    AlgorithmBaseInfo, AlgorithmInput

SUFFIX_START = "sub"
CONFIDENCE_THRESHOLD = 0.45
LABEL_LIST = ['闯入', '未闯入']
cls_dict_chinese = {'人': 0, '自行车': 1, '汽车':2 , '摩托车':3 , '公共汽车':5 , '卡车':7}
schema_dict = {
        "0": "人",
        "1": "自行车",
        "2": "汽车",
        "3": "摩托车",
        "4": "公共汽车",
        "5": "卡车"
      }

class TargetCrossReader(AlgorithmBase):
    """
    禁区闯入识别算法
    """

    schema_path = os.path.join(os.path.dirname(__file__), "schema.json")

    _name = "target_cross"
    _description = "禁区闯入.基础.版本v1"
    _cn_name = "禁区闯入识别算法"

    # 支持的算法分类个数
    __supported_classify = (AlgorithmClassifyEnum.Primary,)
    # 支持的算法策略，primary -> main & secondary -> main
    __supported_strategies = [
        AlgorithmStrategy(__supported_classify[0], AlgorithmStrategyEnum.Main, "禁区闯入主识别策略",
                          "禁区闯入识别识别主策略", kv_param={}),
    ]

    # 支持的算法参数，若干
    __supported_params = AlgorithmBase.json_schema_2_algorithm_params(schema_path)

    def __init__(self, _inputs: AlgorithmInput, _id: int = -1):
        super(TargetCrossReader, self).__init__(self.__class__._name, self.__class__._description,
                                            self.__class__._cn_name,
                                            self.__class__.__supported_strategies,
                                            self.__class__.__supported_params)

        if _inputs.__eq__(dict()):
            return

        # self._local_var._inputImages = []
        # self._local_var._threshold = CONFIDENCE_THRESHOLD
        self.init(_inputs, None)

    def init(self, _inputs: AlgorithmInput, _settings: AlgorithmSettings):
        # 算法实例参数
        self._local_var._settings = _settings
        self._local_var._inputs = _inputs

        # self._local_var._inputImages = self._local_var._inputs.get("images", [])
        self._local_var._inputImages = self._get_input_images()

        self.noGoAreas = self._get_input_value_by_name("noGoAreas")
        
        multi_select = self._get_input_value_by_name("multi_select")
        schema_cls = [schema_dict[i] for i in multi_select]
        self.detect_cls = [cls_dict_chinese[i] for i in schema_cls]

        threshold = self._get_input_value_by_name("threshold")
        if threshold is None or threshold >= 1:
            threshold = CONFIDENCE_THRESHOLD
        self._local_var._threshold = threshold
        #threshold = CONFIDENCE_THRESHOLD
        #self._local_var._threshold = threshold

        self._local_var._processImagesNormalized = []
        self._local_var._processTempRet = {}
        self._local_var._processFinalRet = [[] for _ in self._local_var._inputImages]
        self._local_var._processFinalRect = [[] for _ in self._local_var._inputImages]
        self._local_var._processOutputDir = os.getcwd()
        return self

    def _supported_classify(self):
        return self.__supported_classify

    def _supported_strategy(self):
        return self.__supported_strategies

    def _supported_params(self):
        return self.__supported_params

    def _preprocess(self) -> Any:  # TODO
        # self._local_var._processImagesNormalized = [img_preprocess(image, IMG_TARGET_SIZE)
        #                                             for image in self._local_var._inputImages]
        pass

    def _do_detect(self) -> (bool, Any, tuple):
        ret = self.person_cross_detect(self.noGoAreas, self._local_var._inputImages, self._local_var._threshold, self.detect_cls)
        self._local_var._processTempRet = ret

        self._local_var._processFinalRect = [[] for _ in self._local_var._inputImages]
        """ 按约定格式输出 """
        for img_idx, img in enumerate(self._local_var._inputImages):
            temp, bboxs = [], []
            for b_idx, res in enumerate(self._local_var._processTempRet[img_idx]):
                box = []
                xmin, ymin, xmax, ymax, box_score, box_cls, cross_result = res
                if box_score < self._local_var._threshold:
                    continue
                temp.append(cross_result)
                _4points_score = RectUtils.ltrb_to_4_points([xmin, ymin], [xmax, ymax])
                box.append(_4points_score)
                box.append(float(box_score))
                bboxs.append(box)
            self._local_var._processFinalRet[img_idx] = temp
            self._local_var._processFinalRect[img_idx] = bboxs

        # TODO 当前只支持一张图, val 返回什么呢？
        return True, self._local_var._processFinalRet, self._local_var._processFinalRect

    def _postprocess(self) -> Any:
        pass

    def _gen_result_img(self) -> List[np.ndarray]:
        assert self._local_var._processFinalRet is not None
        # assert len(self._processFinalRet) == len(self._inputImages)

        ret_images = []
        for img_idx, raw_img in enumerate(self._local_var._inputImages):
            ret_img = self.draw_bbox_image(img_idx, raw_img, LABEL_LIST, threshold=self._local_var._threshold)
            ret_images.append(ret_img)
        return ret_images

    def draw_bbox_image(self, img_idx, img_str, label_list, threshold=CONFIDENCE_THRESHOLD):
        """ 画 bbox到图上 """
        # 将原始图像 resize 到 target_size 方便绘图
        # frame = img_resize_crop(get_image(img_str))
        frame = get_image(img_str)
        startPoint = self._no_goline['start']
        endPoint = self._no_goline['end']
        cv2.line(frame, startPoint, endPoint, [0, 0, 255], thickness=5, lineType=cv2.LINE_AA)
        for b_idx, res in enumerate(self._local_var._processTempRet[img_idx]):
            xmin, ymin, xmax, ymax, box_score, box_cls, cross_result = res
            if box_score < threshold:
                continue
            cv2.rectangle(frame, (xmin, ymin), (xmax, ymax), (255, 0, 255), 2)
            # #cv2.putText(图像, 文字, (x, y), 字体, 大小, (b, g, r), 宽度)
            cv2.putText(frame, cross_result, (int(xmin + 50), int(ymin - 2)), cv2.FONT_HERSHEY_SIMPLEX, 0.5,
                        (255, 0, 0), 2)
            cv2.putText(frame, str(round(box_score, 2)), (int(xmin), int(ymin - 2)), cv2.FONT_HERSHEY_SIMPLEX, 0.5,
                        (0, 255, 0), 2)
        return frame

    def determine_algorithm_instance(self, settings: AlgorithmSettings = None, index: int = 0,
                                     a_type: str = "system_inner"):
        suffix = f"{SUFFIX_START}{index}"
        module_name = f".{self._name}.ab_{self._name}_{suffix}"

        base_path = ALGORITHM_DEPL_SYSTEM_INNER_PATH
        if a_type == "user_define":
            base_path = ALGORITHM_DEPL_USER_DEFINE_PATH

        try:
            M = importlib.import_module(module_name, base_path)
        except ModuleNotFoundError:
            raise InspectionException(f"Auto-load module '{module_name}' from '{base_path}' \
                                failure, please check it manually")

        # !!!!  找出子类中名称以当前 suffix.capitalize()  为 END 的作为 target 算法类  !!!!
        target_clazz_ = [clazz for clazz in TargetCrossReader.__subclasses__()
                         if clazz.__name__.endswith(suffix.capitalize())][0]

        algorithm_instance: TargetCrossReader = target_clazz_(self._local_var._inputs, self._id)
        algorithm_instance._settings = settings

        return algorithm_instance

    def get_algorithm_detail(self, a_type) -> AlgorithmDetail:
        schema_base = AlgorithmBase._get_algorithm_schema_info(self.schema_path, "algorithm")
        info = AlgorithmBaseInfo(self._id, self._name, self._name, self.cn_name, self._description,
                                 batch_support=schema_base.get("batchSupport"),
                                 alarm_support=schema_base.get("alarmSupport"),
                                 is_override_alarm=schema_base.get("isOverrideAlarm"),
                                 result_show_type=schema_base.get("resultShowType"))
        classifies = [self._classify2info(c) for c in self.__supported_classify]
        strategies = [self._strategy2info(s) for s in self.__supported_strategies]
        roi = self._get_roi_define(self.schema_path)
        scene_image = self._get_scene_image(self.schema_path)
        params = AlgorithmBase.json_schema_2_algorithm_params(self.schema_path)  # 重新加载一下, 可能会有刷新
        output_define = self._output_define2info(
            AlgorithmBase._get_algorithm_schema_info(self.schema_path, "outputDefine"))
        sub_list = self.load_algorithm_sublist(self._name, a_type, 'depl')

        return AlgorithmDetail(info, classifies, strategies, roi, scene_image, params, output_define, sub_list)

    def person_cross_detect(self, noGoAreas, batch_img: list, threshold, detect_cls):
        """
        禁区闯入检测算法
        """
        raise NotImplementedError("you should implement it in sub-algorithm")
