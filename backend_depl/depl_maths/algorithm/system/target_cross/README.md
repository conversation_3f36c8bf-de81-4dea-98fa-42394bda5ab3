## 禁区闯入识别算法 
## 版本:

 v 1.0.0

## 描述:

 该算法用于禁止区域检测，对于此区域有目标时进行报警，包括人、车等。支持工厂、实验室等大多数场景下的禁区闯入的识别。

## 输入:

| 名称 | 类型 | 描述 |
|-------|--------------|----------------------|
| 禁区 | polygon | 多边形，代表次闭合区域为禁区 |
| 识别类型 | multi_select | 多选项，代表将要被识别的种类 |
| 识别灵敏度 | number | 0-1之间的数值， 指的是算法识别的灵敏度，灵敏度越高识别准确率越低，可根据实际情况酌情调节 |



* 是否支持单图批量识别：是

## 输出：

| 名称 | 类型 | 描述 |
|-------|--------------|----------------------|
| 识别位置 | square | 矩形,item为4个point，代表闯入禁区目标的位置坐标 |
| 是否闯入 | string | 字符串，代表识别出的目标是否有闯入行为 |
| 置信度 | number | 数值，0-1之间，代表识别出来目标的可信度 |