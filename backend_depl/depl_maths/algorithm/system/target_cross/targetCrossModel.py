import json
import os
from collections import OrderedDict, namedtuple

# paddle.disable_static()
import cv2
import numpy as np
import paddle
import tensorrt as trt


def xywh2xyxy(x):
    y = x.clone()
    y[..., 0] = x[..., 0] - x[..., 2] / 2  # top left x
    y[..., 1] = x[..., 1] - x[..., 3] / 2  # top left y
    y[..., 2] = x[..., 0] + x[..., 2] / 2  # bottom right x
    y[..., 3] = x[..., 1] + x[..., 3] / 2  # bottom right y
    return y

def non_max_suppression(
        prediction,
        conf_thres=0.25,
        iou_thres=0.45,
        classes=None,
        agnostic=False,
        multi_label=False,
        labels=(),
        max_det=300,
        nc=0,  # number of classes (optional)
        max_time_img=0.05,
        max_nms=30000,
        max_wh=7680,
):
    """
    Perform non-maximum suppression (NMS) on a set of boxes, with support for masks and multiple labels per box.

    Arguments:
        prediction (torch.Tensor): A tensor of shape (batch_size, num_classes + 4 + num_masks, num_boxes)
            containing the predicted boxes, classes, and masks. The tensor should be in the format
            output by a model, such as YOLO.
        conf_thres (float): The confidence threshold below which boxes will be filtered out.
            Valid values are between 0.0 and 1.0.
        iou_thres (float): The IoU threshold below which boxes will be filtered out during NMS.
            Valid values are between 0.0 and 1.0.
        classes (List[int]): A list of class indices to consider. If None, all classes will be considered.
        agnostic (bool): If True, the model is agnostic to the number of classes, and all
            classes will be considered as one.
        multi_label (bool): If True, each box may have multiple labels.
        labels (List[List[Union[int, float, torch.Tensor]]]): A list of lists, where each inner
            list contains the apriori labels for a given image. The list should be in the format
            output by a dataloader, with each label being a tuple of (class_index, x1, y1, x2, y2).
        max_det (int): The maximum number of boxes to keep after NMS.
        nc (int): (optional) The number of classes output by the model. Any indices after this will be considered masks.
        max_time_img (float): The maximum time (seconds) for processing one image.
        max_nms (int): The maximum number of boxes into torchvision.ops.nms().
        max_wh (int): The maximum box width and height in pixels

    Returns:
        (List[torch.Tensor]): A list of length batch_size, where each element is a tensor of
            shape (num_boxes, 6 + num_masks) containing the kept boxes, with columns
            (x1, y1, x2, y2, confidence, class, mask1, mask2, ...).
    """
    # Checks
    assert 0 <= conf_thres <= 1, f'Invalid Confidence threshold {conf_thres}, valid values are between 0.0 and 1.0'
    assert 0 <= iou_thres <= 1, f'Invalid IoU {iou_thres}, valid values are between 0.0 and 1.0'
    if isinstance(prediction, (list, tuple)):  # YOLOv8 model in validation model, output = (inference_out, loss_out)
        prediction = prediction[0]  # select only inference output

    bs = prediction.shape[0]  # batch size
    nc = nc or (prediction.shape[1] - 4)  # number of classes
    nm = prediction.shape[1] - nc - 4
    mi = 4 + nc  # mask start index
    #a = prediction[:, 4:mi].astype('float32')
    #a = a.amax(1)
    xc = prediction[:, 4:mi].amax(1) > conf_thres  # candidates

    # Settings
    # min_wh = 2  # (pixels) minimum box width and height
    #time_limit = 0.5 + max_time_img * bs  # seconds to quit after
    #redundant = True  # require redundant detections
    multi_label &= nc > 1  # multiple labels per box (adds 0.5ms/img)
    #merge = False  # use merge-NMS

    #t = time.time()
    #output = [paddle.zeros((0, 6 + nm), device=prediction.device)] * bs
    output = [paddle.zeros((0, 6 + nm))] * bs
    for xi, x in enumerate(prediction):  # image index, image inference
        # Apply constraints
        # x[((x[:, 2:4] < min_wh) | (x[:, 2:4] > max_wh)).any(1), 4] = 0  # width-height
        x = x.transpose([1, 0])[xc[xi]]  # confidence

        # Cat apriori labels if autolabelling
        if labels and len(labels[xi]):
            lb = labels[xi]
            v = paddle.zeros((len(lb), nc + nm + 5), device=x.device)
            v[:, :4] = lb[:, 1:5]  # box
            v[range(len(lb)), lb[:, 0].long() + 4] = 1.0  # cls
            x = paddle.concat((x, v), 0)

        # If none remain process next image
        if not x.shape[0]:
            return []

        # Detections matrix nx6 (xyxy, conf, cls)
        box, cls, mask = x.split((4, nc, nm), 1)
        box = xywh2xyxy(box)  # center_x, center_y, width, height) to (x1, y1, x2, y2)
        if multi_label:
            i, j = (cls > conf_thres).nonzero(as_tuple=False).T
            x = paddle.concat((box[i], x[i, 4 + j, None], j[:, None].float(), mask[i]), 1)
        else:  # best class only
            #conf, j = cls.max(1, keepdim=True)
            conf = paddle.max(cls, 1, keepdim=True)
            j = paddle.argmax(cls, 1, keepdim=True)
            x = paddle.concat((box, conf, j.astype('float32'), mask), 1)[conf.reshape([-1]) > conf_thres]

        # Filter by class
        if classes is not None:
            x = x[(x[:, 5:6] == paddle.to_tensor(classes, device=x.device)).any(1)]

        # Apply finite constraint
        # if not torch.isfinite(x).all():
        #     x = x[torch.isfinite(x).all(1)]

        # Check shape
        n = x.shape[0]  # number of boxes
        if not n:  # no boxes
            return 0
        x = x[x[:, 4].argsort(descending=True)[:max_nms]]  # sort by confidence and remove excess boxes
        x = x if len(x.shape)==2 else paddle.unsqueeze(x, axis=0)
        # Batched NMS
        c = x[:, 5:6] * (0 if agnostic else max_wh)  # classes
        boxes, scores = x[:, :4] + c, x[:, 4]  # boxes (offset by class)[11, 4], scores [11]
        #i = torchvision.ops.nms(boxes, scores, iou_thres)  # NMS
        if n==1:
            i=0
        else:
            i = paddle.vision.ops.nms(boxes, iou_thres, scores) # [1]
            i = i[:max_det]  # limit detections
        output[xi] = x[i]

    return output if len(output[0].shape)==2 else [paddle.unsqueeze(output[0], axis=0)]

def scale_boxes(img1_shape, boxes, img0_shape, ratio_pad=None):
    """
    Rescales bounding boxes (in the format of xyxy) from the shape of the image they were originally specified in
    (img1_shape) to the shape of a different image (img0_shape).

    Args:
      img1_shape (tuple): The shape of the image that the bounding boxes are for, in the format of (height, width).
      boxes (torch.Tensor): the bounding boxes of the objects in the image, in the format of (x1, y1, x2, y2)
      img0_shape (tuple): the shape of the target image, in the format of (height, width).
      ratio_pad (tuple): a tuple of (ratio, pad) for scaling the boxes. If not provided, the ratio and pad will be
                         calculated based on the size difference between the two images.

    Returns:
      boxes (torch.Tensor): The scaled bounding boxes, in the format of (x1, y1, x2, y2)
    """
    if ratio_pad is None:  # calculate from img0_shape
        gain = min(img1_shape[0] / img0_shape[0], img1_shape[1] / img0_shape[1])  # gain  = old / new
        pad = (img1_shape[1] - img0_shape[1] * gain) / 2, (img1_shape[0] - img0_shape[0] * gain) / 2  # wh padding
    else:
        gain = ratio_pad[0][0]
        pad = ratio_pad[1]
    boxes[..., 0] -= pad[0]  # x padding
    boxes[..., 2] -= pad[0]  # x padding
    boxes[..., 1] -= pad[1]  # y padding
    boxes[..., 3] -= pad[1]  # y padding
    boxes[..., :4] /= gain

    # boxes[..., 0].clip(0, img0_shape[1])  # x1
    # boxes[..., 1].clip(0, img0_shape[0])  # y1
    # boxes[..., 2].clip(0, img0_shape[1])  # x2
    # boxes[..., 3].clip(0, img0_shape[0])  # y2
    boxes[..., 0] = paddle.clip(boxes[..., 0], min=0, max=img0_shape[1])
    boxes[..., 1] = paddle.clip(boxes[..., 1], min=0, max=img0_shape[0])
    boxes[..., 2] = paddle.clip(boxes[..., 2], min=0, max=img0_shape[1])
    boxes[..., 3] = paddle.clip(boxes[..., 3], min=0, max=img0_shape[0])
    return boxes

def clip_coords(coords, shape):
    """
    Clip line coordinates to the image boundaries.

    Args:
        coords (torch.Tensor) or (numpy.ndarray): A list of line coordinates.
        shape (tuple): A tuple of integers representing the size of the image in the format (height, width).

    Returns:
        (None): The function modifies the input `coordinates` in place, by clipping each coordinate to the image boundaries.
    """
    if isinstance(coords, paddle.Tensor):  # faster individually
        coords[..., 0].clip(0, shape[1])  # x
        coords[..., 1].clip(0, shape[0])  # y
    else:  # np.array (faster grouped)
        coords[..., 0] = coords[..., 0].clip(0, shape[1])  # x
        coords[..., 1] = coords[..., 1].clip(0, shape[0])  # y

def scale_coords(img1_shape, coords, img0_shape, ratio_pad=None, normalize=False):
    """
    Rescale segment coordinates (xyxy) from img1_shape to img0_shape

    Args:
      img1_shape (tuple): The shape of the image that the coords are from.
      coords (torch.Tensor): the coords to be scaled
      img0_shape (tuple): the shape of the image that the segmentation is being applied to
      ratio_pad (tuple): the ratio of the image size to the padded image size.
      normalize (bool): If True, the coordinates will be normalized to the range [0, 1]. Defaults to False

    Returns:
      coords (torch.Tensor): the segmented image.
    """
    if ratio_pad is None:  # calculate from img0_shape
        gain = min(img1_shape[0] / img0_shape[0], img1_shape[1] / img0_shape[1])  # gain  = old / new
        pad = (img1_shape[1] - img0_shape[1] * gain) / 2, (img1_shape[0] - img0_shape[0] * gain) / 2  # wh padding
    else:
        gain = ratio_pad[0][0]
        pad = ratio_pad[1]

    coords[..., 0] -= pad[0]  # x padding
    coords[..., 1] -= pad[1]  # y padding
    coords[..., 0] /= gain
    coords[..., 1] /= gain
    clip_coords(coords, img0_shape)
    if normalize:
        coords[..., 0] /= img0_shape[1]  # width
        coords[..., 1] /= img0_shape[0]  # height
    return coords

def draw_one_result(im0, bbox):
    pose_palette = np.array([[255, 128, 0], [255, 153, 51], [255, 178, 102], [230, 230, 0], [255, 153, 255],
                                      [153, 204, 255], [255, 102, 255], [255, 51, 255], [102, 178, 255], [51, 153, 255],
                                      [255, 153, 153], [255, 102, 102], [255, 51, 51], [153, 255, 153], [102, 255, 102],
                                      [51, 255, 51], [0, 255, 0], [0, 0, 255], [255, 0, 0], [255, 255, 255]],
                                     dtype=np.uint8)
    skeleton = [[16, 14], [14, 12], [17, 15], [15, 13], [12, 13], [6, 12], [7, 13], [6, 7], [6, 8], [7, 9],
                         [8, 10], [9, 11], [2, 3], [1, 2], [1, 3], [2, 4], [3, 5], [4, 6], [5, 7]]
    limb_color = pose_palette[[9, 9, 9, 9, 7, 7, 7, 0, 0, 0, 0, 0, 16, 16, 16, 16, 16, 16, 16]]
    kpt_color = pose_palette[[16, 16, 16, 16, 16, 0, 0, 0, 0, 0, 0, 9, 9, 9, 9, 9, 9]]
    red = [0, 0, 255]
    green = [0, 255, 0]
    p1, p2 = (int(bbox[0]), int(bbox[1])), (int(bbox[2]), int(bbox[3]))
    cv2.rectangle(im0, p1, p2, green, thickness=0, lineType=cv2.LINE_AA)
    '''
    for i, k in enumerate(keypoints):
        color_k = [int(x) for x in kpt_color[i]]
        x_coord, y_coord = k[0], k[1]
        #if x_coord % shape[1] != 0 and y_coord % shape[0] != 0:
        #    if len(k) == 3:
        #        conf = k[2]
        #        if conf < 0.5:
        #            continue
        #    cv2.circle(self.im, (int(x_coord), int(y_coord)), radius, color_k, -1, lineType=cv2.LINE_AA)
        cv2.circle(im0, (int(x_coord), int(y_coord)), 2, color_k, thickness=2, lineType=cv2.LINE_AA)

    #ndim = keypoints.shape[-1]
    for i, sk in enumerate(skeleton):
        pos1 = (int(keypoints[(sk[0] - 1), 0]), int(keypoints[(sk[0] - 1), 1]))
        pos2 = (int(keypoints[(sk[1] - 1), 0]), int(keypoints[(sk[1] - 1), 1]))
        #if ndim == 3:
            #conf1 = keypoints[(sk[0] - 1), 2]
            #conf2 = keypoints[(sk[1] - 1), 2]
            #if conf1 < 0.5 or conf2 < 0.5:
            #    continue
        #if pos1[0] % shape[1] == 0 or pos1[1] % shape[0] == 0 or pos1[0] < 0 or pos1[1] < 0:
            #continue
        #if pos2[0] % shape[1] == 0 or pos2[1] % shape[0] == 0 or pos2[0] < 0 or pos2[1] < 0:
        #    continue
        cv2.line(im0, pos1, pos2, [int(x) for x in limb_color[i]], thickness=1, lineType=cv2.LINE_AA)
    '''

def draw_results(im0, bbox):
    #cv2.rectangle(im0, (5, 839), (545,341), [0, 255, 0], thickness=2, lineType=cv2.LINE_AA)
    #cv2.line(im0, (5, 839), (545,341), [0, 0, 255], thickness=5, lineType=cv2.LINE_AA)
    person_num = len(bbox)
    print(person_num)
    for i in range(person_num):
        if int(bbox[i][5]) != 0:
            continue
        print(bbox[i].numpy())
        draw_one_result(im0, bbox[i])
    return im0

def write_results(im, results, out_dir, out_img):
    """Write inference results to a file or directory."""
    bbox = results['boxes']
    visual_img = draw_results(im, bbox)
    cv2.imwrite(os.path.join(out_dir, out_img), visual_img)
        
def from_numpy(x):
        """
         Convert a numpy array to a tensor.
         Args:
             x (np.ndarray): The array to be converted.
         Returns:
             (torch.Tensor): The converted tensor
         """
        return paddle.to_tensor(x).cuda() if isinstance(x, np.ndarray) else x

class targetCrossDetect():
    def __init__(self, model_path) -> None:
        self.dynamic, self.bindings, self.model, self.context, self.output_names, self.binding_addrs = self.loadModel(model_path)

    def loadModel(self, model_path):

        Binding = namedtuple('Binding', ('name', 'dtype', 'shape', 'data', 'ptr'))
        logger = trt.Logger(trt.Logger.INFO)
        # Read file
        with open(model_path, 'rb') as f, trt.Runtime(logger) as runtime:
            meta_len = int.from_bytes(f.read(4), byteorder='little')  # read metadata length
            metadata = json.loads(f.read(meta_len).decode('utf-8'))  # read metadata
            model = runtime.deserialize_cuda_engine(f.read())  # read engine
        context = model.create_execution_context()
        bindings = OrderedDict()
        output_names = []
        fp16 = False  # default updated below
        dynamic = False
        for i in range(model.num_bindings):
            name = model.get_binding_name(i)
            dtype = trt.nptype(model.get_binding_dtype(i))
            if model.binding_is_input(i):
                if -1 in tuple(model.get_binding_shape(i)):  # dynamic
                    dynamic = True
                    context.set_binding_shape(i, tuple(model.get_profile_shape(0, i)[2]))
                if dtype == np.float16:
                    fp16 = True
            else:  # output
                output_names.append(name)
            shape = tuple(context.get_binding_shape(i))
            #import torch
            #im = torch.from_numpy(np.empty(shape, dtype=dtype)).cuda()
            im = paddle.to_tensor(np.empty(shape, dtype=dtype)).cuda()
            #bindings[name] = Binding(name, dtype, shape, im, int(im.data_ptr()))
            bindings[name] = Binding(name, dtype, shape, im, int(im.value().get_tensor()._ptr()))
        binding_addrs = OrderedDict((n, d.ptr) for n, d in bindings.items())
        batch_size = bindings['images'].shape[0]  # if dynamic, this is instead max batch size
        return dynamic, bindings, model, context, output_names, binding_addrs

    def preprocess(self, im): #cv2 images
        """Prepares input image before inference.

        Args:
            im (torch.Tensor | List(np.ndarray)): (N, 3, h, w) for tensor, [(h, w, 3) x N] for list.
        """
        shape = im.shape[:2]
        new_shape = [640, 640]
        # Scale ratio (new / old)
        r = min(new_shape[0] / shape[0], new_shape[1] / shape[1])
        # Compute padding
        ratio = r, r  # width, height ratios
        new_unpad = int(round(shape[1] * r)), int(round(shape[0] * r))
        dw, dh = new_shape[1] - new_unpad[0], new_shape[0] - new_unpad[1]  # wh padding
        dw /= 2  # divide padding into 2 sides
        dh /= 2
        if shape[::-1] != new_unpad:  # resize
            im = cv2.resize(im, new_unpad, interpolation=cv2.INTER_LINEAR)
        top, bottom = int(round(dh - 0.1)), int(round(dh + 0.1))
        left, right = int(round(dw - 0.1)), int(round(dw + 0.1))
        im = cv2.copyMakeBorder(im, top, bottom, left, right, cv2.BORDER_CONSTANT,
                                 value=(114, 114, 114))  # add border
        
        im = np.expand_dims(im,axis=0)
        im = im[..., ::-1].transpose((0, 3, 1, 2))  # BGR to RGB, BHWC to BCHW, (n, 3, h, w)
        im = np.ascontiguousarray(im)  # contiguous
        #im = torch.from_numpy(im)
        paddle.disable_static()
        im = paddle.to_tensor(im, dtype='float16')
        # NOTE: assuming im with (b, 3, h, w) if it's a tensor
        img = im.cuda()
        #img = img.float()  # uint8 to fp16/32
        #img = img.half()
        img /= 255  # 0 - 255 to 0.0 - 1.0
        return img

    def postprocess(self, preds, img, orig_img, noGoAreas, threshold=0.5, cls=[0]):
        """Return detection results for a given input image or list of images."""
        preds = non_max_suppression(preds.astype('float32'), conf_thres=threshold, iou_thres=0.7, agnostic=False, max_det=300)
        if len(preds)==0:
            print('detect person num is 0')
            return []
        repeated_preds = []
        preds = preds[0].numpy().tolist()
        for i, pre in enumerate(preds):
            if pre not in repeated_preds and int(pre[5]) in cls:
                repeated_preds.append(pre)
        if len(repeated_preds) == 0:
            print('detect target num is 0')
            return []
        print('detect target num is {}'.format(len(repeated_preds)))
        preds = [paddle.to_tensor(repeated_preds)]
        results = []
        for i, pred in enumerate(preds):
            orig_img = orig_img[i] if isinstance(orig_img, list) else orig_img
            shape = orig_img.shape
            pred[:, :4] = scale_boxes(img.shape[2:], pred[:, :4], shape).round()
            #results.append({'names': {0: 'person'}, 'boxes': pred})
            #img_result = []
            for box in pred:
                box = list(box.numpy())
                box, score, cls = [int(box[0]), int(box[1]), int(box[2]), int(box[3])], box[4], int(box[5])
                # crossResult = self.crossOrNot(no_goline, crossline, box[:4])
                if cls==0:
                    crossResult = self.person_cross(noGoAreas, box[:4])
                else:
                    crossResult = self.vehicle_cross(noGoAreas, box[:4])
                if crossResult == '未闯入':
                    continue
                box += [score, cls, crossResult]
                #img_result.append(box)
                results.append(box)
        return results

    def vehicle_cross(self, polygon, bbox):
        xmin, ymin, xmax, ymax = bbox
        middle = [int((xmax-xmin)/2)+xmin, int((ymax-ymin)/2)+ymin]
        midle_left = [int((middle[0]-xmin)/2)+xmin, int((ymax-middle[1])/2)+middle[1]]
        middle_right = [int((xmax-middle[0])/2)+middle[0], int((ymax-middle[1])/2)+middle[1]]
        for point in [middle, midle_left, middle_right]:
            flag = cv2.pointPolygonTest(np.array(polygon).astype('int32'), point, True)
            if flag < 0:
                return '未闯入'
        return '闯入'

    def person_cross(self, polygon, bbox):
        xmin, ymin, xmax, ymax = bbox
        medium_point = [int(xmin + (xmax - xmin) / 2), int(ymin + (ymax - ymin) / 2)]
        for point in [[xmin, ymax], medium_point, [xmax, ymax]]:
            flag = cv2.pointPolygonTest(np.array(polygon).astype('int32'), point, True)
            if flag >= 0:
                return '闯入'
        return '未闯入'

    def crossOrNot(self, no_goline, crossline, bbox):
        pointOne, pointTwo = no_goline['start'], no_goline['end']
        crosspoint = crossline['end']
        a = pointTwo[1] - pointOne[1]
        b = pointOne[0] - pointTwo[0]
        c = pointTwo[0] * pointOne[1] - pointOne[0] * pointTwo[1] 
        x1, y1, x2, y2 =   bbox
        p1, p2, p3, p4 = (x1, y1), (x1, y2), (x2, y2), (x2, y1)
        d = a*crosspoint[0] + b*crosspoint[1] + c
        distenceFlat = 'larger' if d > 0 else 'less'
        for point in [p1, p2, p3, p4]:
            distence = a*point[0] + b*point[1] + c
            #crossOrNotflag = False if distence > 0 else True
            if (distenceFlat=='larger' and distence > 0) or (distenceFlat=='less' and distence < 0):
                return 'cross'
        return 'notCross'

    def predict(self, ori_img, noGoAreas, threshold, cls):
        im = self.preprocess(ori_img)
        if self.dynamic and im.shape != self.bindings['images'].shape:
            i = self.model.get_binding_index('images')
            self.context.set_binding_shape(i, im.shape)  # reshape if dynamic
            self.bindings['images'] = self.bindings['images']._replace(shape=im.shape)
            for name in self.output_names:
                i = self.model.get_binding_index(name)
                self.bindings[name].data.resize_(tuple(self.context.get_binding_shape(i)))
        s = self.bindings['images'].shape
        assert im.shape == list(s), f"input size {im.shape} {'>' if self.dynamic else 'not equal to'} max model size {s}"
        self.binding_addrs['images'] = int(im.value().get_tensor()._ptr())
        self.context.execute_v2(list(self.binding_addrs.values()))
        preds = [self.bindings[x].data for x in sorted(self.output_names)]
        preds = from_numpy(preds[0]) if len(preds) == 1 else [from_numpy(x) for x in preds]
        results = self.postprocess(preds, im, ori_img, noGoAreas, threshold, cls)
        return results

if __name__=='__main__':
    model_path = "/opt/tjh/model_server/depl/intelligent_inspection_backend/backend_depl/depl_maths/algorithm/system/target_cross/inference_model/detect_yolov8m.engine"
    personDetectModel = targetCrossDetect(model_path)
    noGoAreas = [[288, 140], [264,343], [191,387], [497,395], [582, 318], [574,154]]
    cls_dict = {0: 'person', 1: 'bicycle', 2: 'car', 3: 'motorcycle', 4: 'airplane', 5: 'bus', 6: 'train', 7: 'truck', 8: 'boat', 9: 'traffic light', 10: 'fire hydrant', 11: 'stop sign', 12: 'parking meter', 13: 'bench', 14: 'bird', 15: 'cat', 16: 'dog', 17: 'horse', 18: 'sheep', 19: 'cow', 20: 'elephant', 21: 'bear', 22: 'zebra', 23: 'giraffe', 24: 'backpack', 25: 'umbrella', 26: 'handbag', 27: 'tie', 28: 'suitcase', 29: 'frisbee', 30: 'skis', 31: 'snowboard', 32: 'sports ball', 33: 'kite', 34: 'baseball bat', 35: 'baseball glove', 36: 'skateboard', 37: 'surfboard', 38: 'tennis racket', 39: 'bottle', 40: 'wine glass', 41: 'cup', 42: 'fork', 43: 'knife', 44: 'spoon', 45: 'bowl', 46: 'banana', 47: 'apple', 48: 'sandwich', 49: 'orange', 50: 'broccoli', 51: 'carrot', 52: 'hot dog', 53: 'pizza', 54: 'donut', 55: 'cake', 56: 'chair', 57: 'couch', 58: 'potted plant', 59: 'bed', 60: 'dining table', 61: 'toilet', 62: 'tv', 63: 'laptop', 64: 'mouse', 65: 'remote', 66: 'keyboard', 67: 'cell phone', 68: 'microwave', 69: 'oven', 70: 'toaster', 71: 'sink', 72: 'refrigerator', 73: 'book', 74: 'clock', 75: 'vase', 76: 'scissors', 77: 'teddy bear', 78: 'hair drier', 79: 'toothbrush'}
    cls_dict_chinese = {'人': 0, '自行车': 1, '小汽车':2 , '摩托车':3 , '公共汽车':5 , '卡车':7}
    detType = ['人', '卡车']
    cls = [cls_dict_chinese[i] for i in detType]
    img_path = '/opt/tjh/model_server/test_imgs/Snipaste_2024-08-02_10-12-37.png'
    im0 = cv2.imread(img_path)
    result = personDetectModel.predict(im0, noGoAreas, threshold=0.5, cls=cls)
    print(result)
    for info in result:
        box = info[:4]
        x1, y1, x2, y2 = box
        cv2.rectangle(im0, (x1, y1), (x2, y2), (0, 0, 255), 2)
    cv2.polylines(im0, [np.array(noGoAreas).astype('int32')], True, (0, 0, 255), 5)
    cv2.imwrite('/opt/tjh/model_server/test_imgs/output/detect.jpg', im0)
