{"algorithm": {"name": "禁区闯入识别算法", "code": "target_cross", "des": "禁区闯入识别算法.V1", "type": "DeepLearning", "alarmSupport": true, "isOverrideAlarm": false, "batchSupport": true, "resultShowType": "ALARM"}, "images": ["e:/images/test.jpg"], "roi": {"name": "ROI区域", "type": "polygon", "des": "支持绘制的ROI区域类型, polygon:多边形, circle:圆, ellipse:椭圆", "value": [[325, 116], [331, 115], [346, 310], [337, 310]], "nullable": true}, "targetSceneImage": {"in_use": false, "uri": "", "name": "场景基准图"}, "inputParam": {"threshold": {"name": "识别灵敏度", "type": "number", "des": "算识别灵敏度,灵敏度越高识别准确率越低,可根据实际情况酌情调节", "value": 0.5, "val_range": [0, 1, 0.01, 0.5], "nullable": true}, "noGoAreas": {"name": "禁区", "type": "polygon", "des": "禁止进入的区域, 为多边形, 进入则报警", "value": [[100, 200], [150, 230], [320, 200], [450, 460]], "showable": true}, "multi_select": {"name": "识别类型", "type": "multi_select", "des": "算法将要识别的类别，例如人、车等", "val_range": {"0": "人", "1": "自行车", "2": "汽车", "3": "摩托车", "4": "公共汽车", "5": "卡车"}}, "resultDes": {"name": "结果展示描述", "type": "string", "desc": "结果展示的描述文字"}}, "outputDefine": {"type": "string", "desc": "禁区闯入检测", "val_enum": {"cross": 0, "notcross": 1}, "ruleInfo": [{"desc": "有目标闯入禁区", "isBind": false, "type": "HH", "val": 0.0, "placeHolder": "数值大于高"}, {"desc": "有目标闯入禁区", "isBind": true, "type": "H", "val": 1.0, "placeHolder": "数值小于高高"}, {"desc": "有目标闯入禁区", "isBind": false, "type": "L", "val": 0.0, "placeHolder": "数值小于高"}, {"desc": "有目标闯入禁区", "isBind": false, "type": "LL", "val": 0.0, "placeHolder": "数值小于低"}]}}