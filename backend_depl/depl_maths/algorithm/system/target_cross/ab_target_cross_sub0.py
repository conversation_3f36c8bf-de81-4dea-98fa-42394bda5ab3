import os.path
from typing import Any, List

import cv2

from backend_common.maths.algorithm.base.algorithm_base import AlgorithmInput
from depl_maths.algorithm.system.target_cross.ab_target_cross import LABEL_LIST
from depl_maths.algorithm.system.target_cross.ab_target_cross import Target<PERSON><PERSON>Reader
# from depl_maths.ppdet.infer import Detector, bench_log
from depl_maths.algorithm.system.target_cross.targetCrossModel import targetCrossDetect


class TargetCrossReaderSub0(TargetCrossReader):
    """
    禁区闯入识别算法
    """

    _index = 0
    _description = "禁区闯入识别.主模型.版本v1"
    _cn_name = "禁区闯入识别算法"

    def __init__(self, _inputs: AlgorithmInput, _id: int = -1):
        super(TargetCrossReaderSub0, self).__init__(_inputs, _id)
        # Detector.__init__(self, model_dir=os.path.dirname(__file__), device="GPU")
        detect_model_path = os.path.join(os.path.dirname(__file__), 'inference_model/detect_yolov8m.engine')
        self.personCrossModel = targetCrossDetect(model_path=detect_model_path)

    def _postprocess(self) -> Any:
        pass

    def _alarm_trig(self, detect_ret) -> List[List]:
        alarms = []
        for img_ret in detect_ret[1]:
            alm_of_img = []
            cross = [label for label in img_ret if label == LABEL_LIST[0]]
            alarm = (True, "H", "有目标闯入禁区!") if len(cross) > 0 else (False, None, None)
            alm_of_img.append(alarm)  # 一张图仅能产生一个报警哦
            alarms.append(alm_of_img)
        return alarms

    def person_cross_detect(self, noGoAreas, batch_img: list, threshold, detect_cls):
        results = []
        for img in batch_img:
            # img = cv2.imread(img)
            detect_results = self.personCrossModel.predict(img, noGoAreas, threshold, detect_cls)
            results.append(detect_results)
        return results
