import cv2
import numpy as np
from ultralytics import YOLO


class yoloModel():
    def __init__(self, checkpoint_path):
        self.model = YOLO(checkpoint_path)
        self.label = {0: '油污', 1: '煤粉', 2: '水渍', 3: '水汽', 4: '污渍', 5: '水', 6: '冒火星', 7: '阳光'}

    def detect(self, img, conf=0.5, iou=0.7, roi=None, classes=[0, 1, 3, 5, 6], device='cpu'):
        detect_results = self.model.predict(img, conf=conf, iou=iou, classes=classes, device=device, verbose=False)
        for result in detect_results:
            cls_ = result.boxes.cls.cpu().numpy().tolist()
            cls = [self.label[i] for i in cls_]
            conf = result.boxes.conf.cpu().numpy().tolist()
            box = result.boxes.xyxy.cpu().numpy().astype('int32').tolist()
        index = np.argsort(np.array(conf))[::-1]
        img_res = []
        for i in index:
            in_or_out = self.box_in_Roi(roi, box[i])
            if in_or_out == 'in':
                res = [box[i], conf[i], cls[i]]
                img_res.append(res)
                break
        return img_res

    def box_in_Roi(self, roi, bbox):
        if roi is None or len(roi) == 0:
            return 'in'
        xmin, ymin, xmax, ymax = bbox
        medium_point = [int(xmin + (xmax - xmin) / 2), int(ymin + (ymax - ymin) / 2)]
        result = 0
        for point in [[xmin, ymin], medium_point, [xmax, ymax]]:
            flag = cv2.pointPolygonTest(np.array(roi).astype('int32'), point, True)
            if flag >= 0:
                result += 1
        if result > 1:
            return 'in'
        else:
            return 'out'
