import numpy as np
from PIL import Image, ImageDraw, ImageFont
import cv2

def draw_chinese_text(image, text, position):
    """
    在图像的指定位置绘制中文文本
    
    :param image: 输入的图像（OpenCV格式）
    :param text: 要绘制的中文文本
    :param position: 文本的位置（x, y），这里是指定为检测框的左上角
    :return: 绘制了文本后的图像
    """
    # 切换到PIL的图像格式
    pil_image = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
    
    # 使用一个字体文件，注意这里的路径需要是有效的中文字体文件路径
    font_path = "/home/<USER>/backend_common/utils/font/simsun.ttf"
    font_size = 30  # 根据实际需求调整字体大小
    font = ImageFont.truetype(font_path, font_size)
    
    # 创建一个可以画文的ImageDraw对象
    draw = ImageDraw.Draw(pil_image)
    
    # 计算背景矩形的大小
    text_width, text_height = draw.textsize(text, font=font)
    margin = 5
    rectangle_coords = (position[0], position[1], position[0] + text_width + 2 * margin, position[1] + text_height + 2 * margin)
    
    # 先画白底
    draw.rectangle(rectangle_coords, fill=(255, 255, 255))
    
    # 再画黑字
    draw.text((position[0] + margin, position[1] + margin), text, font=font, fill=(0, 0, 0))
    
    # 转回OpenCV格式
    result_image = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
    
    return result_image


def nms(boxes, scores, iou_threshold):
    # Sort by score
    sorted_indices = np.argsort(scores)[::-1]

    keep_boxes = []
    while sorted_indices.size > 0:
        # Pick the last box
        box_id = sorted_indices[0]
        keep_boxes.append(box_id)

        # Compute IoU of the picked box with the rest
        ious = compute_iou(boxes[box_id, :], boxes[sorted_indices[1:], :])

        # Remove boxes with IoU over the threshold
        keep_indices = np.where(ious < iou_threshold)[0]

        # print(keep_indices.shape, sorted_indices.shape)
        sorted_indices = sorted_indices[keep_indices + 1]

    return keep_boxes


def multiclass_nms(boxes, scores, class_ids, iou_threshold):

    unique_class_ids = np.unique(class_ids)

    keep_boxes = []
    for class_id in unique_class_ids:
        class_indices = np.where(class_ids == class_id)[0]
        class_boxes = boxes[class_indices, :]
        class_scores = scores[class_indices]

        class_keep_boxes = nms(class_boxes, class_scores, iou_threshold)
        keep_boxes.extend(class_indices[class_keep_boxes])

    return keep_boxes


def compute_iou(box, boxes):
    # Compute xmin, ymin, xmax, ymax for both boxes
    xmin = np.maximum(box[0], boxes[:, 0])
    ymin = np.maximum(box[1], boxes[:, 1])
    xmax = np.minimum(box[2], boxes[:, 2])
    ymax = np.minimum(box[3], boxes[:, 3])

    # Compute intersection area
    intersection_area = np.maximum(0, xmax - xmin) * np.maximum(0, ymax - ymin)

    # Compute union area
    box_area = (box[2] - box[0]) * (box[3] - box[1])
    boxes_area = (boxes[:, 2] - boxes[:, 0]) * (boxes[:, 3] - boxes[:, 1])
    union_area = box_area + boxes_area - intersection_area

    # Compute IoU
    iou = intersection_area / union_area

    return iou


def xywh2xyxy(x):
    # Convert bounding box (x, y, w, h) to bounding box (x1, y1, x2, y2)
    y = np.copy(x)
    y[..., 0] = x[..., 0] - x[..., 2] / 2
    y[..., 1] = x[..., 1] - x[..., 3] / 2
    y[..., 2] = x[..., 0] + x[..., 2] / 2
    y[..., 3] = x[..., 1] + x[..., 3] / 2
    return y
