import os
import cv2
import numpy as np
import acl

from backend_common.exceptions.inspection_exception import AlgorithmProcessException
from backend_common.maths.algorithm.base.algorithm_base import AlgorithmBase, AlgorithmInput
from backend_common.utils.util import RectUtils
from depl_maths.algorithm.system.paomaodilou.yolov8_om import YOLOv8
from loguru import logger
from depl_maths.algorithm.system.paomaodilou.utils import draw_chinese_text


LABEL_LIST = {0: '油污', 1: '煤粉',  3: '水汽',  5: '水', 6: '冒火星'}


class PaomaodilouReader(AlgorithmBase):
    """
    跑冒滴漏检测算法
    """
    _name = "paomaodilou"

    def __init__(self, _inputs: AlgorithmInput):
        super(PaomaodilouReader, self).__init__(self.__class__._name)

        # 模型路径检查
        detect_model_path = os.path.join(os.path.dirname(__file__), 'inference_model/yolov8m_640_310B1.om')
        if not os.path.exists(detect_model_path):
            raise AlgorithmProcessException("paomaodilou模型文件不存在")

        device_id = 0
        self.yolov8_model = YOLOv8(device_id, detect_model_path, 640, 640)

        if _inputs.__eq__(dict()):
            return

        self.init(_inputs)

    def init(self, _inputs: AlgorithmInput):
        # 算法实例参数
        self._local_var._inputs = _inputs

        # 获取单张图像
        self.origin_img = self._get_input_images()[0]

        input_param = self._get_input_param()
        self.conf = input_param["conf"]
        self.iou = input_param["iou"]

        input_roi = input_param["input_roi"]
        if input_roi is None:
            self.input_roi = None
        else:
            self.input_roi = [[coor['x'], coor['y']] for coor in input_roi]

    def release_resource(self):
        self.yolov8_model.release_resource()

    def box_in_roi(self, roi, bbox):
        if roi is None or len(roi) == 0:
            return 'in'
        xmin, ymin, xmax, ymax = bbox
        medium_point = [int(xmin + (xmax - xmin) / 2), int(ymin + (ymax - ymin) / 2)]
        result = 0
        for point in [[xmin, ymin], medium_point, [xmax, ymax]]:
            flag = cv2.pointPolygonTest(np.array(roi).astype('int32'), point, True)
            if flag >= 0:
                result += 1
        if result > 1:
            return 'in'
        else:
            return 'out'
        
    def drow_figure(self, img):
        # 画禁区
        if self.input_roi is not None:
            cv2.polylines(img, [np.array(self.input_roi, dtype=np.int32)], True, (0,0,255), 2)
        for class_id, box, score in zip(self.class_ids, self.boxes, self.scores):
            box = [int(i) for i in box]
            flag = self.box_in_roi(self.input_roi, box)
            if flag == 'in':
                color = (0,0,255)
                # box_name = EN2CH_DICT[class_names[class_id]]
                cv2.rectangle(img, (box[0], box[1]), (box[2], box[3]), color, 2)
                label = LABEL_LIST.get(class_id, "unknown")
                img = draw_chinese_text(img, label +  ' ' + str(round(float(score), 2)), (box[0], box[1]))
        return img

    def _do_detect(self):
        self.boxes, self.scores, self.class_ids = self.yolov8_model.detect_objects(self.origin_img, self.conf, self.iou)

        osdinfo = []
        class_name = []
        for class_id, boxes, score in zip(self.class_ids, self.boxes, self.scores):
            if class_id not in LABEL_LIST.keys():
                continue

            boxes = [int(i) for i in boxes]
            flag = self.box_in_roi(self.input_roi, boxes)
            if flag == 'in':
                box_name = LABEL_LIST[class_id]
                class_name.append(box_name)
                coord = RectUtils.ltrb_to_4_points_dict([boxes[0], boxes[1]], [boxes[2], boxes[3]])
                osdinfo.append({"dataType": "SQUARE", "textObj": {"value": box_name}, "score": round(float(score), 2), "coords": coord})

        if len(class_name) > 0:
            output = {"paomaodilou": {"value": "检测到泄漏"}, "paomaodilouBoolean": {"value": True}}
        else:
            output = {"paomaodilou": {"value": "未检测到泄漏"}, "paomaodilouBoolean": {"value": False}}

        return True, output, osdinfo
