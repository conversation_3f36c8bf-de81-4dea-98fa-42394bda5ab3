import os

from backend_common.maths.algorithm.base.algorithm_base import AlgorithmBase, AlgorithmInput
from backend_common.utils.util import RectUtils

from depl_maths.algorithm.system.paomaodilou.yolo_model import yoloModel
import torch
from loguru import logger


class Pa<PERSON>odilouReader(AlgorithmBase):
    """
    跑冒滴漏检测算法
    """
    _name = "paomaodilou"

    def __init__(self, _inputs: AlgorithmInput):
        super(PaomaodilouReader, self).__init__(self.__class__._name)

        checkpoint_path = os.path.join(os.path.dirname(__file__), 'inference_model/yolov8_m_32.pt')
        self.model = yoloModel(checkpoint_path)
        if torch.cuda.is_available():
            self.device = '0'
            logger.info("device use GPU infer")
        else:
            self.device = 'cpu'
            logger.info("device use CPU infer")

        self.alarm_label = [0, 1, 3, 5, 6]
        self.label = {0: '油污', 1: '煤粉', 2: '水渍', 3: '水汽', 4: '污渍', 5: '水', 6: '冒火星', 7: '阳光'}

        if _inputs.__eq__(dict()):
            return

        self.init(_inputs)

    def init(self, _inputs: AlgorithmInput):
        # 算法实例参数
        self._local_var._inputs = _inputs

        # 获取单张图像
        self.inputImages = self._get_input_images()[0]

        input_param = self._get_input_param()
        self.conf = input_param["conf"]
        self.iou = input_param["iou"]

        input_roi = input_param["input_roi"]
        if input_roi is None:
            self.input_roi = None
        else:
            self.input_roi = [[coor['x'], coor['y']] for coor in input_roi]

    def paomaodilou_detect(self, batch_img: list, conf, iou, roi=None):
        results = []
        for img in batch_img:
            detect_results = self.model.detect(img, conf, iou, roi, classes=self.alarm_label, device=self.device)
            results.append(detect_results)
        return results

    def _do_detect(self):

        detect_results = self.model.detect(self.inputImages, self.conf, self.iou, self.input_roi, classes=self.alarm_label, device=self.device)

        osdinfo = []
        class_name = []
        for res in detect_results:
            [xmin, ymin, xmax, ymax], box_score, box_class = res

            coord = RectUtils.ltrb_to_4_points_dict([xmin, ymin], [xmax, ymax])
            class_name.append(box_class)

            osdinfo.append({"dataType": "SQUARE", "textObj": {"value": box_class}, "score": round(float(box_score), 2), "coords": coord})

        if len(class_name) > 0:
            output = {"paomaodilou": {"value": "检测到泄漏"}, "paomaodilouBoolean": {"value": True}}
        else:
            output = {"paomaodilou": {"value": "未检测到泄漏"}, "paomaodilouBoolean": {"value": False}}

        return True, output, osdinfo
