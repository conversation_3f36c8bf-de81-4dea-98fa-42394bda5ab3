import os
import numpy as np
import torch

from loguru import logger
from backend_common.exceptions.inspection_exception import AlgorithmCheckException
from backend_common.maths.algorithm.base.algorithm_base import AlgorithmBase, AlgorithmInput

from depl_maths.algorithm.system.ppocr.onnxocr.onnx_paddleocr import ONNXPaddleOcr


class PpocrReader(AlgorithmBase):
    """
    OCR文字识别算法-ppocrv5-ONNX版本
    """

    _name = "ppocr"

    def __init__(self, _inputs: AlgorithmInput):
        super(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, self).__init__(self.__class__._name)

        det_model_dir = os.path.join(os.path.dirname(__file__), 'inference_model/ppocr_det_v5.onnx')
        rec_model_dir = os.path.join(os.path.dirname(__file__), 'inference_model/ppocr_rec_v5.onnx')

        if torch.cuda.is_available():
            use_gpu = True
            logger.info("device use GPU infer")
        else:
            use_gpu = False
            logger.info("device use CPU infer")

        self.predictor = ONNXPaddleOcr(use_angle_cls=False,
                                       use_gpu=use_gpu,
                                       det_model_dir=det_model_dir,
                                       rec_model_dir=rec_model_dir)

        if _inputs.__eq__(dict()):
            return

        self.imgs = []
        self.init(_inputs)

    def init(self, _inputs: AlgorithmInput):
        # 算法实例参数
        self._local_var._inputs = _inputs
        self.imgs = self._get_input_images()[0]  # 获取1张图像

        # 获取参数字典
        input_param = self._get_input_param()
        self.threshod = input_param["threshold"]
        self.input_ocr_roi = input_param["ocr_roi"]
        self.roi_rec_coor = [list(k.values()) for k in input_param["ocr_roi"]]
        return self

    def img_cut_check(self, img, xmax, ymax):
        if img is None:
            logger.error("读取图片失败, 图片为空")
            raise AlgorithmCheckException("读取图片失败, 图片为空")

        h, w = img.shape[:2]
        if xmax > w or ymax > h:
            msg = "ROI坐标超出图片大小，请检查！"
            logger.error(f"{msg}")
            raise AlgorithmCheckException(f"{msg}")

    def preprocess(self):
        y1 = self.roi_rec_coor[0][1]
        y2 = self.roi_rec_coor[2][1]
        x1 = self.roi_rec_coor[0][0]
        x2 = self.roi_rec_coor[2][0]

        self.img_cut_check(self.imgs, x2, y2)
        self.img_roi = self.imgs[y1:y2, x1:x2]

    def postprocess(self, result):
        text = ""
        bbox_and_score = []

        if len(result[0]) <= 0:
            logger.info("detect result of img is None")
            return text, bbox_and_score

        res = result[0]
        # 输入的ROI中可能有多个识别框的文本，所以这里需要处理将其合并为一行显示
        if len(res) > 1:
            text, scores_list = '', []
            for line in res:
                # 输出示例： [[[328.0, 622.0], [396.0, 627.0], [395.0, 643.0], [327.0, 638.0]], ('上限报警', 0.995436429977417)]
                logger.info(f"OCR result multi-line :: {line}")
                score = line[1][1]
                if score < self.threshod:
                    logger.warning(f"score < threshold, some det-text is ignored! {score}")
                    continue
                text += line[1][0]
                scores_list.append(score)

            if len(scores_list) == 0:
                return '', []
            score = np.mean(scores_list)

            # 此处bbox不用识别出来的多个，直接改用ROI输入的框
            bbox = self.roi_rec_coor
            bbox_and_score = [bbox, score]

        elif len(res) == 1:
            line = res[0]
            score = line[1][1]
            logger.info(f"OCR result single-line :: {line}")
            if score < self.threshod:
                logger.warning(f"score < threshold, some det-text is ignored! {score}")
                return '', []

            text = line[1][0]
            offset_x, offset_y = self.roi_rec_coor[0]
            bbox = np.array(line[0], dtype=np.int32)
            bbox += [offset_x, offset_y]
            bbox = [p.tolist() for p in list(bbox)]

            bbox_and_score = [bbox, score]

        return text, bbox_and_score

    def _do_detect(self):
        self.preprocess()
        result = self.predictor.ocr(self.img_roi)
        text, bbox_and_score = self.postprocess(result)

        if text == "":
            return True, {"ppocr": {"value": "未识别到字符"}}, []

        bbox_res = []
        for info in bbox_and_score[0]:
            bbox_res.append({"x": info[0], "y": info[1]})

        osdinfo = [{"dataType": "SQUARE", "textObj": {"value": text}, "score": round(bbox_and_score[1], 2), "coords": bbox_res}]

        return True, {"ppocr": {"value": text}}, osdinfo
