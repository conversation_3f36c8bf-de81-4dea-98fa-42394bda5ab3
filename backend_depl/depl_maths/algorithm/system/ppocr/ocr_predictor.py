import os
import cv2
import copy
import time
import acl
from loguru import logger

from depl_maths.algorithm.system.ppocr.util import get_image_file_list, get_rotate_crop_image
from depl_maths.algorithm.system.ppocr.predict_det import TextDetector
from depl_maths.algorithm.system.ppocr.predict_rec import TextRecognizer


class TextSystem(object):
    def __init__(self, det_model_path, rec_model_path):

        # acl接口初始化，只初始化一次
        ret = acl.init()
        if ret != 0:
            logger.error(f"acl init failed, errorCode is {ret}")
        else:
            logger.info("acl init sucess")

        self.text_detector = TextDetector(model_path=det_model_path)
        self.text_recognizer = TextRecognizer(model_path=rec_model_path)

    def __call__(self, img):
        time_dict = {"det": 0, "rec": 0, "cls": 0, "all": 0}

        if img is None:
            logger.debug("no valid image provided")
            return None, None, time_dict

        start = time.time()
        ori_im = img.copy()
        dt_boxes, elapse = self.text_detector(img)

        time_dict["det"] = elapse

        if dt_boxes is None:
            logger.debug("no dt_boxes found, time cost : {}".format(elapse))
            end = time.time()
            time_dict["all"] = end - start
            return None, None, time_dict
        else:
            logger.info(
                "dt_boxes num : {}, time cost : {}".format(len(dt_boxes), elapse)
            )
        img_crop_list = []

        dt_boxes = sorted_boxes(dt_boxes)

        for bno in range(len(dt_boxes)):
            tmp_box = copy.deepcopy(dt_boxes[bno])
            img_crop = get_rotate_crop_image(ori_im, tmp_box)
            img_crop_list.append(img_crop)
        if len(img_crop_list) > 1000:
            logger.debug(
                f"rec crops num: {len(img_crop_list)}, time and memory cost may be large."
            )

        rec_res, elapse = self.text_recognizer(img_crop_list)
        time_dict["rec"] = elapse
        logger.info("rec_res num  : {}, time cost : {}".format(len(rec_res), elapse))

        result = []
        for box, rec_result in zip(dt_boxes, rec_res):
            text, score = rec_result[0], rec_result[1]
            result.append([box.astype('int32').tolist(), (text, score)])
        end = time.time()
        time_dict["all"] = end - start
        return [result]


def sorted_boxes(dt_boxes):
    """
    Sort text boxes in order from top to bottom, left to right
    args:
        dt_boxes(array):detected text boxes with shape [4, 2]
    return:
        sorted boxes(array) with shape [4, 2]
    """
    num_boxes = dt_boxes.shape[0]
    sorted_boxes = sorted(dt_boxes, key=lambda x: (x[0][1], x[0][0]))
    _boxes = list(sorted_boxes)

    for i in range(num_boxes - 1):
        for j in range(i, -1, -1):
            if abs(_boxes[j + 1][0][1] - _boxes[j][0][1]) < 10 and (
                _boxes[j + 1][0][0] < _boxes[j][0][0]
            ):
                tmp = _boxes[j]
                _boxes[j] = _boxes[j + 1]
                _boxes[j + 1] = tmp
            else:
                break
    return _boxes


if __name__ == '__main__':

    import acl
    ret = acl.init()
    det_model_path = 'ch_PP-OCRv4_det_960_960.om'
    rec_model_path = 'ch_PP-OCRv4_rec_48_3000.om'
    font_path = 'simfang.ttf'
    drop_score = 0.5

    text_sys = TextSystem(det_model_path, rec_model_path)

    image_dir = '/opt/tjh/home-made-model/ppocr/inference/123.png'
    image_file_list = get_image_file_list(image_dir)

    draw_img_save_dir = '/opt/tjh/home-made-model/ppocr/inference/inference_om/inference_results'
    if not os.path.exists(draw_img_save_dir):
        os.makedirs(draw_img_save_dir)

    for idx, image_file in enumerate(image_file_list):
        print(image_file)
        img = cv2.imread(image_file)
        result = text_sys(img, drop_score)
        print(result)
