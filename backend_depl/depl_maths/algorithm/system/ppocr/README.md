## OCR文字识别算法V1.0.0

### 描述

 该算法用于将图像中的文字进行识别，并以文本的形式返回。

- 是否配置ROI：否

- 是否配置多图输入：否

### 输入参数

| 参数名      | 是否绘制 | 参数类型   | 默认值  | 是否必填 | 数据范围 | 精度   | 描述                                |
| -------- | ---- | ------ | ---- | ---- | ---- | ---- | --------------------------------- |
| 待识别ROI区域 | 是    | SQUARE | -    | 是    | -    | -    | 待识别ROI矩形区域                        |
| 识别灵敏度    | 否    | FLOAT  | 0.50 | 是    | 0-1  | 0.01 | 算法识别的灵敏度，<br>灵敏度越高识别准确率越低，<br>可根据实际情况酌情调节 |

### 输出参数

| 参数      | 值类型    | 描述                      |
| ------- | ------ | ----------------------- |
| ocr识别结果 | STRING | 输出值为"未识别到字符" 或 实际识别到的字符 |

### 结果展示

| 名称     | 描述                             |
| ------ | ------------------------------ |
| 识别文字内容 | 识别到的OCR内容                      |
| 置信度    | 浮点型值，范围0-1之间，精度0.01，代表识别目标的可信度 |