{"algorithmMetadata": {"name": "OCR文字识别算法", "code": "ppocr", "version": "1.0.0", "description": "OCR文字识别算法.V1", "type": "DeepLearning", "isBatchProcessing": false, "roiDrawType": "SQUARE", "roiRequired": false, "classification": "DEVICE_METER"}, "input": [{"key": "ocr_roi", "label": "待识别ROI区域", "dataType": "SQUARE", "constraints": {"required": true}, "drawToOsd": true}, {"key": "threshold", "label": "识别灵敏度", "dataType": "FLOAT", "defaultValue": 0.5, "constraints": {"min": 0, "max": 1, "precision": 0.01, "required": true}, "drawToOsd": false}], "output": [{"key": "ppocr", "label": "ocr识别结果", "dataType": "STRING"}]}