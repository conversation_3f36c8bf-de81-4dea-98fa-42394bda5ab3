
#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
ppocr - 加密Python包
自动安装导入钩子以支持加密模块
"""
__version__ = "1.0.0"

# 安装导入钩子以支持加密模块
try:
    import sys
    import os

    # 获取当前包路径
    current_dir = os.path.dirname(__file__)
    parent_dir = os.path.dirname(current_dir)

    # 添加父目录到sys.path以便导入加密工具
    if parent_dir not in sys.path:
        sys.path.insert(0, parent_dir)

    from import_hook import install_import_hook
    install_import_hook("ppocr", __version__)

except ImportError as e:
    print(f"警告: 无法安装导入钩子: {e}")
except Exception as e:
    print(f"警告: 导入钩子安装失败: {e}")

# 导入包的主要功能
try:
    from depl_maths.algorithm.system.ppocr.ab_ppocr import PpocrReader
except ImportError:
    # 如果标准导入失败，可能是因为文件已加密，导入钩子会处理
    try:
        from .ab_ppocr import PpocrReader
    except ImportError as e:
        print(f"警告: 无法导入PpocrReader: {e}")
        PpocrReader = None



__all__ = [
    "PpocrReader"
]
