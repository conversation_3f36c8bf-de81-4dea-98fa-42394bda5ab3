#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
构建时代码加密脚本
在setup.py构建过程中对Python源代码进行加密
"""

import os
import shutil
import glob
from setuptools import Command
from setuptools.command.build_py import build_py
try:
    from .crypto_utils import get_crypto_instance
except ImportError:
    from crypto_utils import get_crypto_instance


class EncryptedBuildPy(build_py):
    """自定义构建命令，支持代码加密"""
    
    def __init__(self, dist):
        super().__init__(dist)
        self.package_name = getattr(dist, 'name', 'ppocr')
        self.version = getattr(dist, 'version', '1.0.0')
        self.crypto = get_crypto_instance(self.package_name, self.version)
        
    def build_packages(self):
        """构建包，包含加密过程"""
        # 先执行标准构建
        super().build_packages()
        
        # 然后对构建结果进行加密
        self.encrypt_built_packages()
    
    def encrypt_built_packages(self):
        """加密已构建的包"""
        print("开始加密Python源代码...")
        
        # 获取构建目录
        build_lib = self.build_lib
        
        # 查找所有需要加密的Python文件
        for package in self.packages:
            package_dir = os.path.join(build_lib, package.replace('.', os.sep))
            if os.path.exists(package_dir):
                self.encrypt_package_directory(package_dir, package)
    
    def encrypt_package_directory(self, package_dir, package_name):
        """加密包目录中的Python文件"""
        print(f"加密包: {package_name} 在 {package_dir}")
        
        # 查找所有.py文件
        py_files = []
        for root, dirs, files in os.walk(package_dir):
            for file in files:
                if file.endswith('.py'):
                    py_files.append(os.path.join(root, file))
        
        # 加密每个Python文件
        for py_file in py_files:
            self.encrypt_python_file(py_file)
    
    def encrypt_python_file(self, py_file):
        """加密单个Python文件"""
        # 跳过某些不需要加密的文件
        filename = os.path.basename(py_file)
        if filename in ['__init__.py', 'setup.py', 'crypto_utils.py', 'import_hook.py', 'build_encrypted.py']:
            print(f"跳过文件: {py_file}")
            return
            
        try:
            # 生成加密文件路径
            encrypted_file = py_file.replace('.py', '.pye')
            
            # 加密文件
            self.crypto.encrypt_file(py_file, encrypted_file)
            
            # 删除原始.py文件
            os.remove(py_file)
            
            print(f"已加密: {py_file} -> {encrypted_file}")
            
        except Exception as e:
            print(f"加密文件失败 {py_file}: {str(e)}")


class EncryptCommand(Command):
    """独立的加密命令"""
    
    description = "加密Python源代码文件"
    user_options = [
        ('package-name=', 'p', '包名'),
        ('version=', 'v', '版本号'),
        ('source-dir=', 's', '源代码目录'),
        ('output-dir=', 'o', '输出目录'),
    ]
    
    def initialize_options(self):
        self.package_name = 'ppocr'
        self.version = '1.0.0'
        self.source_dir = None
        self.output_dir = None
    
    def finalize_options(self):
        if self.source_dir is None:
            self.source_dir = self.package_name
        if self.output_dir is None:
            self.output_dir = f"{self.package_name}_encrypted"
    
    def run(self):
        """执行加密命令"""
        print(f"开始加密包: {self.package_name} v{self.version}")
        print(f"源目录: {self.source_dir}")
        print(f"输出目录: {self.output_dir}")
        
        # 创建加密实例
        crypto = get_crypto_instance(self.package_name, self.version)
        
        # 创建输出目录
        if os.path.exists(self.output_dir):
            shutil.rmtree(self.output_dir)
        os.makedirs(self.output_dir)
        
        # 复制并加密文件
        self.copy_and_encrypt_directory(self.source_dir, self.output_dir, crypto)
        
        print("加密完成!")
    
    def copy_and_encrypt_directory(self, src_dir, dst_dir, crypto):
        """复制并加密目录"""
        if not os.path.exists(src_dir):
            print(f"源目录不存在: {src_dir}")
            return
            
        for root, dirs, files in os.walk(src_dir):
            # 计算相对路径
            rel_root = os.path.relpath(root, src_dir)
            if rel_root == '.':
                dst_root = dst_dir
            else:
                dst_root = os.path.join(dst_dir, rel_root)
            
            # 创建目标目录
            os.makedirs(dst_root, exist_ok=True)
            
            # 处理文件
            for file in files:
                src_file = os.path.join(root, file)
                
                if file.endswith('.py'):
                    # Python文件需要加密
                    if file in ['__init__.py', 'setup.py', 'crypto_utils.py', 'import_hook.py', 'build_encrypted.py']:
                        # 某些文件直接复制，不加密
                        dst_file = os.path.join(dst_root, file)
                        shutil.copy2(src_file, dst_file)
                        print(f"复制: {src_file} -> {dst_file}")
                    else:
                        # 加密Python文件
                        dst_file = os.path.join(dst_root, file.replace('.py', '.pye'))
                        crypto.encrypt_file(src_file, dst_file)
                else:
                    # 非Python文件直接复制
                    dst_file = os.path.join(dst_root, file)
                    shutil.copy2(src_file, dst_file)
                    print(f"复制: {src_file} -> {dst_file}")


def create_encrypted_init_py(package_dir, package_name="ppocr", version="1.0.0"):
    """
    创建包含导入钩子的__init__.py文件
    
    Args:
        package_dir (str): 包目录路径
        package_name (str): 包名
        version (str): 版本号
    """
    init_file = os.path.join(package_dir, '__init__.py')
    
    init_content = f'''#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
{package_name} - 加密Python包
自动安装导入钩子以支持加密模块
"""

# 安装导入钩子
try:
    from .import_hook import install_import_hook
    install_import_hook("{package_name}", "{version}")
except ImportError as e:
    print(f"警告: 无法安装导入钩子: {{e}}")

# 导入原始的包内容
# 这里可以添加包的公共API导入
'''
    
    with open(init_file, 'w', encoding='utf-8') as f:
        f.write(init_content)
    
    print(f"已创建加密包的__init__.py: {init_file}")


if __name__ == "__main__":
    # 测试加密命令
    import sys
    
    if len(sys.argv) > 1:
        package_name = sys.argv[1]
    else:
        package_name = "ppocr"
    
    print(f"测试加密包: {package_name}")
    
    # 创建加密命令实例
    cmd = EncryptCommand(None)
    cmd.package_name = package_name
    cmd.version = "1.0.0"
    cmd.source_dir = package_name
    cmd.output_dir = f"{package_name}_encrypted"
    
    cmd.finalize_options()
    cmd.run()
