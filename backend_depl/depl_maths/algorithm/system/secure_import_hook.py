#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
增强安全的Python加密模块导入钩子
防止解密后的代码落盘，增加逆向难度
"""

import sys
import os
import importlib.util
import importlib.machinery
from importlib.abc import Loader, MetaPathFinder
import types
import gc
import ctypes
from .secure_crypto_utils import get_secure_crypto_instance


class SecureEncryptedModuleLoader(Loader):
    """安全的加密模块加载器"""
    
    def __init__(self, fullname, path, crypto_instance):
        self.fullname = fullname
        self.path = path
        self.crypto = crypto_instance
        self._code_cache = {}  # 代码缓存（可选）
    
    def create_module(self, spec):
        """创建模块对象"""
        return None  # 使用默认模块创建
    
    def exec_module(self, module):
        """安全执行模块代码"""
        try:
            # 检查是否已缓存（避免重复解密）
            if self.fullname in self._code_cache:
                code_obj = self._code_cache[self.fullname]
            else:
                code_obj = self._load_and_compile_secure()
                # 可选：缓存编译后的代码对象（不是源码）
                self._code_cache[self.fullname] = code_obj
            
            # 设置模块属性
            module.__file__ = self.path
            module.__loader__ = self
            
            # 执行代码
            exec(code_obj, module.__dict__)
            
        except Exception as e:
            raise ImportError(f"无法加载加密模块 {self.fullname}: {str(e)}")
    
    def _load_and_compile_secure(self):
        """安全加载和编译代码"""
        encrypted_data = None
        decrypted_code = None
        code_str = None
        
        try:
            # 读取加密文件
            with open(self.path, 'r', encoding='utf-8') as f:
                encrypted_data = f.read()
            
            # 解密代码
            decrypted_code = self.crypto.decrypt_data(encrypted_data)
            code_str = decrypted_code.decode('utf-8')
            
            # 立即编译为字节码（避免源码在内存中停留）
            code_obj = compile(code_str, self.path, 'exec')
            
            return code_obj
            
        finally:
            # 立即清理敏感数据
            self._secure_cleanup([encrypted_data, decrypted_code, code_str])
    
    def _secure_cleanup(self, sensitive_vars):
        """安全清理敏感变量"""
        for var in sensitive_vars:
            if var is not None:
                try:
                    if isinstance(var, (str, bytes)):
                        # 尝试清零内存
                        if isinstance(var, str):
                            var_bytes = var.encode('utf-8')
                        else:
                            var_bytes = var
                        
                        # 使用ctypes清零内存（尽力而为）
                        try:
                            ctypes.memset(id(var_bytes) + 32, 0, len(var_bytes))
                        except:
                            pass
                except:
                    pass
        
        # 强制垃圾回收
        gc.collect()


class SecureEncryptedModuleFinder(MetaPathFinder):
    """安全的加密模块查找器"""
    
    def __init__(self, package_name="ppocr", version="1.0.0"):
        self.package_name = package_name
        self.crypto = get_secure_crypto_instance(package_name, version)
        self.package_path = None
        self._access_count = 0  # 访问计数（可用于检测异常访问）
        
    def find_spec(self, fullname, path, target=None):
        """查找模块规范"""
        # 增加访问计数
        self._access_count += 1
        
        # 检测异常访问模式（可选的安全检查）
        if self._access_count > 1000:  # 防止暴力破解
            print(f"警告: 检测到异常访问模式，访问次数: {self._access_count}")
        
        # 只处理当前包内的模块
        if not fullname.startswith(self.package_name):
            return None
            
        # 获取包路径
        if self.package_path is None:
            self.package_path = self._get_package_path()
            
        if self.package_path is None:
            return None
            
        # 构建加密文件路径
        encrypted_file = self._build_encrypted_file_path(fullname)
        
        # 检查加密文件是否存在
        if not os.path.exists(encrypted_file):
            return None
            
        # 创建模块规范
        loader = SecureEncryptedModuleLoader(fullname, encrypted_file, self.crypto)
        spec = importlib.machinery.ModuleSpec(fullname, loader, origin=encrypted_file)
        
        return spec
    
    def _build_encrypted_file_path(self, fullname):
        """构建加密文件路径"""
        module_parts = fullname.split('.')
        if len(module_parts) == 1:
            # 顶级包
            encrypted_file = os.path.join(self.package_path, '__init__.pye')
        else:
            # 子模块
            relative_parts = module_parts[1:]  # 去掉包名
            if len(relative_parts) == 1:
                # 直接子模块
                encrypted_file = os.path.join(self.package_path, f"{relative_parts[0]}.pye")
            else:
                # 嵌套子模块
                subdir = os.path.join(self.package_path, *relative_parts[:-1])
                encrypted_file = os.path.join(subdir, f"{relative_parts[-1]}.pye")
        
        return encrypted_file
    
    def _get_package_path(self):
        """获取包的安装路径"""
        try:
            # 尝试导入包以获取路径
            spec = importlib.util.find_spec(self.package_name)
            if spec and spec.origin:
                return os.path.dirname(spec.origin)
        except ImportError:
            pass
            
        # 在sys.path中搜索
        for path in sys.path:
            package_dir = os.path.join(path, self.package_name)
            if os.path.isdir(package_dir):
                return package_dir
                
        return None


def install_secure_import_hook(package_name="ppocr", version="1.0.0"):
    """
    安装安全导入钩子
    
    Args:
        package_name (str): 包名
        version (str): 版本号
    """
    # 检查是否已经安装
    for finder in sys.meta_path:
        if (isinstance(finder, SecureEncryptedModuleFinder) and 
            finder.package_name == package_name):
            return  # 已经安装
    
    # 创建并安装查找器
    finder = SecureEncryptedModuleFinder(package_name, version)
    sys.meta_path.insert(0, finder)
    
    print(f"已安装安全加密模块导入钩子: {package_name} v{version}")


def uninstall_secure_import_hook(package_name="ppocr"):
    """
    卸载安全导入钩子
    
    Args:
        package_name (str): 包名
    """
    # 查找并移除对应的查找器
    to_remove = []
    for finder in sys.meta_path:
        if (isinstance(finder, SecureEncryptedModuleFinder) and 
            finder.package_name == package_name):
            to_remove.append(finder)
    
    for finder in to_remove:
        sys.meta_path.remove(finder)
        print(f"已卸载安全加密模块导入钩子: {package_name}")


# 运行时保护措施
def _enable_runtime_protection():
    """启用运行时保护措施"""
    try:
        # 禁用某些调试功能（可选）
        import sys
        
        # 检测调试器
        if hasattr(sys, 'gettrace') and sys.gettrace() is not None:
            print("警告: 检测到调试器")
        
        # 可以添加更多反调试措施
        
    except:
        pass


# 自动安装钩子（当模块被导入时）
def auto_install_secure_hook():
    """自动安装安全导入钩子"""
    # 启用运行时保护
    _enable_runtime_protection()
    
    # 从当前模块路径推断包名
    current_file = __file__
    package_dir = os.path.dirname(current_file)
    package_name = os.path.basename(package_dir)
    
    # 安装钩子
    install_secure_import_hook(package_name, "1.0.0")


if __name__ == "__main__":
    # 测试代码
    print("测试安全加密模块导入钩子...")
    install_secure_import_hook("ppocr", "1.0.0")
    print("安全导入钩子安装完成")
    
    # 显示当前的meta_path
    print("当前meta_path:")
    for i, finder in enumerate(sys.meta_path):
        print(f"  {i}: {type(finder).__name__}")
