#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
加密功能测试脚本
测试代码加密、解密、打包和安装流程
"""

import os
import sys
import tempfile
import shutil
import subprocess
from crypto_utils import get_crypto_instance
from import_hook import install_import_hook, uninstall_import_hook


def test_crypto_basic():
    """测试基本的加密解密功能"""
    print("=" * 50)
    print("测试1: 基本加密解密功能")
    print("=" * 50)
    
    crypto = get_crypto_instance("ppocr", "1.0.0")
    
    # 测试数据
    test_code = '''#!/usr/bin/env python
# -*- coding: utf-8 -*-

def hello_world():
    """测试函数"""
    return "Hello, World from encrypted code!"

class TestClass:
    def __init__(self, name):
        self.name = name
    
    def greet(self):
        return f"Hello, {self.name}!"

if __name__ == "__main__":
    print(hello_world())
    obj = TestClass("Encrypted Module")
    print(obj.greet())
'''
    
    print("原始代码:")
    print(test_code[:100] + "...")
    
    # 加密
    encrypted_data = crypto.encrypt_data(test_code)
    print(f"\n加密数据长度: {len(encrypted_data)} 字符")
    print(f"加密数据预览: {encrypted_data[:50]}...")
    
    # 解密
    decrypted_data = crypto.decrypt_data(encrypted_data)
    decrypted_code = decrypted_data.decode('utf-8')
    
    print(f"\n解密数据长度: {len(decrypted_code)} 字符")
    print("解密验证:", "成功" if test_code == decrypted_code else "失败")
    
    return test_code == decrypted_code


def test_file_encryption():
    """测试文件加密解密功能"""
    print("\n" + "=" * 50)
    print("测试2: 文件加密解密功能")
    print("=" * 50)
    
    crypto = get_crypto_instance("ppocr", "1.0.0")
    
    # 创建临时测试文件
    test_content = '''def test_function():
    """这是一个测试函数"""
    return "文件加密测试成功!"

print(test_function())
'''
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False, encoding='utf-8') as f:
        f.write(test_content)
        test_file = f.name
    
    try:
        # 加密文件
        encrypted_file = test_file.replace('.py', '.pye')
        crypto.encrypt_file(test_file, encrypted_file)
        
        print(f"原始文件: {test_file}")
        print(f"加密文件: {encrypted_file}")
        
        # 验证加密文件存在
        if not os.path.exists(encrypted_file):
            print("错误: 加密文件未创建")
            return False
        
        # 解密文件
        decrypted_file = test_file.replace('.py', '_decrypted.py')
        crypto.decrypt_file(encrypted_file, decrypted_file)
        
        # 验证解密结果
        with open(decrypted_file, 'r', encoding='utf-8') as f:
            decrypted_content = f.read()
        
        success = test_content == decrypted_content
        print("文件加密解密验证:", "成功" if success else "失败")
        
        return success
        
    finally:
        # 清理临时文件
        for file in [test_file, encrypted_file, decrypted_file]:
            if os.path.exists(file):
                os.remove(file)


def test_import_hook():
    """测试导入钩子功能"""
    print("\n" + "=" * 50)
    print("测试3: 导入钩子功能")
    print("=" * 50)
    
    # 创建临时目录和测试模块
    temp_dir = tempfile.mkdtemp()
    package_dir = os.path.join(temp_dir, 'test_package')
    os.makedirs(package_dir)
    
    try:
        # 创建测试模块
        test_module_content = '''def encrypted_function():
    return "这是来自加密模块的消息!"

TEST_CONSTANT = "加密常量"
'''
        
        # 加密并保存测试模块
        crypto = get_crypto_instance("test_package", "1.0.0")
        encrypted_data = crypto.encrypt_data(test_module_content)
        
        encrypted_module_path = os.path.join(package_dir, 'test_module.pye')
        with open(encrypted_module_path, 'w', encoding='utf-8') as f:
            f.write(encrypted_data)
        
        # 创建包的__init__.py
        init_content = '''from .import_hook import install_import_hook
install_import_hook("test_package", "1.0.0")
'''
        init_path = os.path.join(package_dir, '__init__.py')
        with open(init_path, 'w', encoding='utf-8') as f:
            f.write(init_content)
        
        # 复制导入钩子文件
        current_dir = os.path.dirname(__file__)
        shutil.copy(os.path.join(current_dir, 'import_hook.py'), package_dir)
        shutil.copy(os.path.join(current_dir, 'crypto_utils.py'), package_dir)
        
        # 添加到sys.path
        sys.path.insert(0, temp_dir)
        
        # 安装导入钩子
        install_import_hook("test_package", "1.0.0")
        
        # 尝试导入加密模块
        try:
            import test_package.test_module as tm
            result = tm.encrypted_function()
            constant = tm.TEST_CONSTANT
            
            print(f"导入成功!")
            print(f"函数调用结果: {result}")
            print(f"常量值: {constant}")
            
            success = "加密模块" in result and "加密常量" in constant
            print("导入钩子测试:", "成功" if success else "失败")
            
            return success
            
        except Exception as e:
            print(f"导入失败: {e}")
            return False
        
    finally:
        # 清理
        uninstall_import_hook("test_package")
        if temp_dir in sys.path:
            sys.path.remove(temp_dir)
        shutil.rmtree(temp_dir, ignore_errors=True)


def test_build_process():
    """测试构建过程"""
    print("\n" + "=" * 50)
    print("测试4: 构建过程测试")
    print("=" * 50)
    
    current_dir = os.path.dirname(__file__)
    
    # 检查必要文件是否存在
    required_files = [
        'setup.py',
        'crypto_utils.py',
        'import_hook.py',
        'build_encrypted.py'
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(os.path.join(current_dir, file)):
            missing_files.append(file)
    
    if missing_files:
        print(f"缺少必要文件: {missing_files}")
        return False
    
    print("所有必要文件都存在")
    
    # 测试加密命令
    try:
        from build_encrypted import EncryptCommand
        print("成功导入EncryptCommand")
        
        # 这里可以添加更多构建测试
        return True
        
    except ImportError as e:
        print(f"导入构建模块失败: {e}")
        return False


def run_all_tests():
    """运行所有测试"""
    print("开始运行加密功能测试套件...")
    print("当前工作目录:", os.getcwd())
    print("Python版本:", sys.version)
    
    tests = [
        ("基本加密解密", test_crypto_basic),
        ("文件加密解密", test_file_encryption),
        ("导入钩子", test_import_hook),
        ("构建过程", test_build_process),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"测试 {test_name} 出现异常: {e}")
            results.append((test_name, False))
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("测试结果汇总")
    print("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "通过" if result else "失败"
        print(f"{test_name:20} : {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 个测试通过")
    
    return passed == len(results)


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
