#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Python代码加密解密工具模块
提供AES加密/解密功能，用于保护Python源代码
"""

import os
import hashlib
import base64
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.primitives import padding
from cryptography.hazmat.backends import default_backend
import platform


class CodeCrypto:
    """代码加密解密类"""
    
    def __init__(self, package_name="ppocr", version="1.0.0"):
        self.package_name = package_name
        self.version = version
        self.key = self._generate_key()
        
    def _generate_key(self):
        """
        生成确定性密钥
        基于包名、版本号和机器标识生成32字节密钥
        """
        # 获取机器标识
        machine_id = platform.node() + platform.machine()
        
        # 组合信息生成密钥
        key_material = f"{self.package_name}_{self.version}_{machine_id}_hollysys_2023"
        
        # 使用SHA256生成32字节密钥
        key = hashlib.sha256(key_material.encode('utf-8')).digest()
        return key
    
    def _pad_data(self, data):
        """PKCS7填充"""
        padder = padding.PKCS7(128).padder()
        padded_data = padder.update(data)
        padded_data += padder.finalize()
        return padded_data
    
    def _unpad_data(self, padded_data):
        """移除PKCS7填充"""
        unpadder = padding.PKCS7(128).unpadder()
        data = unpadder.update(padded_data)
        data += unpadder.finalize()
        return data
    
    def encrypt_data(self, data):
        """
        加密数据
        
        Args:
            data (bytes): 要加密的数据
            
        Returns:
            str: Base64编码的加密数据
        """
        if isinstance(data, str):
            data = data.encode('utf-8')
            
        # 生成随机IV
        iv = os.urandom(16)
        
        # 创建加密器
        cipher = Cipher(algorithms.AES(self.key), modes.CBC(iv), backend=default_backend())
        encryptor = cipher.encryptor()
        
        # 填充并加密数据
        padded_data = self._pad_data(data)
        encrypted_data = encryptor.update(padded_data) + encryptor.finalize()
        
        # 将IV和加密数据组合并Base64编码
        combined_data = iv + encrypted_data
        return base64.b64encode(combined_data).decode('utf-8')
    
    def decrypt_data(self, encrypted_data):
        """
        解密数据
        
        Args:
            encrypted_data (str): Base64编码的加密数据
            
        Returns:
            bytes: 解密后的原始数据
        """
        try:
            # Base64解码
            combined_data = base64.b64decode(encrypted_data.encode('utf-8'))
            
            # 分离IV和加密数据
            iv = combined_data[:16]
            encrypted_bytes = combined_data[16:]
            
            # 创建解密器
            cipher = Cipher(algorithms.AES(self.key), modes.CBC(iv), backend=default_backend())
            decryptor = cipher.decryptor()
            
            # 解密并移除填充
            padded_data = decryptor.update(encrypted_bytes) + decryptor.finalize()
            data = self._unpad_data(padded_data)
            
            return data
        except Exception as e:
            raise ValueError(f"解密失败: {str(e)}")
    
    def encrypt_file(self, input_file, output_file):
        """
        加密文件
        
        Args:
            input_file (str): 输入文件路径
            output_file (str): 输出文件路径
        """
        try:
            with open(input_file, 'rb') as f:
                data = f.read()
            
            encrypted_data = self.encrypt_data(data)
            
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(encrypted_data)
                
            print(f"文件加密完成: {input_file} -> {output_file}")
            
        except Exception as e:
            print(f"文件加密失败 {input_file}: {str(e)}")
            raise
    
    def decrypt_file(self, encrypted_file, output_file):
        """
        解密文件
        
        Args:
            encrypted_file (str): 加密文件路径
            output_file (str): 输出文件路径
        """
        try:
            with open(encrypted_file, 'r', encoding='utf-8') as f:
                encrypted_data = f.read()
            
            decrypted_data = self.decrypt_data(encrypted_data)
            
            with open(output_file, 'wb') as f:
                f.write(decrypted_data)
                
            print(f"文件解密完成: {encrypted_file} -> {output_file}")
            
        except Exception as e:
            print(f"文件解密失败 {encrypted_file}: {str(e)}")
            raise


def get_crypto_instance(package_name="ppocr", version="1.0.0"):
    """获取加密实例的工厂函数"""
    return CodeCrypto(package_name, version)


if __name__ == "__main__":
    # 测试代码
    crypto = CodeCrypto()
    
    # 测试数据加密解密
    test_data = "print('Hello, World!')\ndef test_function():\n    return 'This is a test'"
    print("原始数据:", test_data)
    
    encrypted = crypto.encrypt_data(test_data)
    print("加密数据:", encrypted[:50] + "...")
    
    decrypted = crypto.decrypt_data(encrypted)
    print("解密数据:", decrypted.decode('utf-8'))
    
    print("加密解密测试:", "成功" if test_data == decrypted.decode('utf-8') else "失败")
