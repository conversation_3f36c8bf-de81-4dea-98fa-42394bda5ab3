import json
import json
import multiprocessing
import os

import uvicorn
from fastapi import FastAPI, status, HTTPException, UploadFile, Form
from fastapi.exceptions import RequestValidationError
from fastapi.responses import JSONResponse

from loguru import logger
from starlette.middleware.cors import CORSMiddleware
from starlette.requests import Request

from backend_common.constants import math_constants
from backend_common.exceptions.inspection_exception import InspectionException, AlgorithmCheckException, \
    AlgorithmProcessException
from backend_common.models.response.response_body import response
from listener.listener_event import app_startup_event, app_shutdown_event
from service.maths_mgmt_service import MathABMgmt


current_count = 0
max_count = int(os.getenv('able_current_max_count', 1))

app = FastAPI()

app.add_event_handler("startup", app_startup_event(app))

app.add_event_handler("shutdown", app_shutdown_event(app))
# 配置允许域名列表、允许方法、请求头、cookie等
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


message = "-内存超限，本次任务取消,稍后再试"
# 冗余500M内存
total_memory = 16-0.5


@app.exception_handler(InspectionException)
def unicorn_exception_handler(request: Request, exc: InspectionException):
    logger.exception(exc)
    return JSONResponse(
        status_code=status.HTTP_200_OK,
        content={
            'code': exc.code,
            'msg': f'执行异常:{exc.message}',
            'data': None
        }
    )


@app.exception_handler(AlgorithmProcessException)
def unicorn_exception_handler(request: Request, exc: AlgorithmProcessException):
    logger.exception(exc)
    return JSONResponse(
        status_code=status.HTTP_200_OK,
        content={
            'code': exc.code,
            'msg': f'检测异常:{exc.message}',
            'data': None
        }
    )


@app.exception_handler(AlgorithmCheckException)
def unicorn_exception_handler(request: Request, exc: AlgorithmCheckException):
    logger.exception(exc)
    return JSONResponse(
        status_code=status.HTTP_200_OK,
        content={
            'code': exc.code,
            'msg': f'参数异常:{exc.message}',
            'data': None
        }
    )


@app.exception_handler(Exception)
def unicorn_exception_handler(request: Request, exc: Exception):
    logger.exception(exc)
    return JSONResponse(
        status_code=status.HTTP_200_OK,
        content={
            'code': -1,
            # 'msg': f'内部错误:{exc.__str__()}',
            'msg': f'内部错误: 请查看日志',
            'data': None
        }
    )


@app.exception_handler(HTTPException)
def unicorn_exception_handler(request: Request, exc: HTTPException):
    logger.exception(exc)
    return JSONResponse(
        status_code=status.HTTP_200_OK,
        content={
            'code': -1,
            'msg': f'请求失败:{exc.detail}',
            'data': None
        }
    )


@app.exception_handler(RequestValidationError)
def request_validation_exception_handler(
        request: Request, exc: RequestValidationError
) -> JSONResponse:
    logger.exception(exc)
    return JSONResponse(
        status_code=status.HTTP_200_OK,
        content={
            'code': -1,
            'msg': f'校验错误:{exc.errors()}',
            'data': None
        }
    )

# 执行算法
@app.post("/execute")
def execute(request_param: dict):
    maths_mgmt = MathABMgmt(request_param)
    data = maths_mgmt.execute()
    return response(data=data)


@app.post("/block-upload")
def upload_block(file: UploadFile, name: str = Form(...)):
    maths_mgmt = MathABMgmt({"file": file, "name": name})
    ret, msg = maths_mgmt.block_import()
    return response(data=msg) if ret else response(code=math_constants.UPLOAD_ERROR, data=msg)

@app.post("/block-remove")
def remove_block(code: str = Form(...)):
    maths_mgmt = MathABMgmt({"code": code})
    ret, msg = maths_mgmt.block_remove()
    return response(data=msg) if ret else response(code=math_constants.REMOVE_ERROR, data=msg)


@app.get("/ping")
def ping_health():
    return response(data="pong")


if __name__ == '__main__':
    # conda activate detect
    # uvicorn application_depl:app --host 0.0.0.0 --reload --port 6500
    reload = json.loads(os.getenv("UVICORN_RELOAD", 'true').lower())
    uvicorn.run(app='application_depl:app', host="0.0.0.0", port=6300)  # , reload=reload,)
    # workers=multiprocessing.cpu_count() * 2)
