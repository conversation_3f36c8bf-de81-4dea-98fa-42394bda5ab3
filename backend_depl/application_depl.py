import json
import json
import multiprocessing
import os

import uvicorn
from fastapi import FastAPI, status, HTTPException, UploadFile, Form, Body
from fastapi.exceptions import RequestValidationError
from fastapi.responses import JSONResponse

from loguru import logger
from starlette.middleware.cors import CORSMiddleware
from starlette.requests import Request

from backend_common.constants import math_constants
from backend_common.exceptions.inspection_exception import InspectionException, AlgorithmCheckException, \
    AlgorithmProcessException
from backend_common.models.response.response_body import response
from listener.listener_event import app_startup_event, app_shutdown_event
from service.maths_mgmt_service import MathABMgmt
from typing import Optional, Dict, List, Tuple
from depl_maths.realtime_stream.process import TaskConfig, scheduler, reschedule_task, tasks, redis_client, list_all_jobs
from queue import Queue, Full, Empty
import threading


app = FastAPI()
app.state.REDIS_HOST = os.getenv('REDIS_IP', '0.0.0.0')
app.add_event_handler("startup", app_startup_event(app))

app.add_event_handler("shutdown", app_shutdown_event(app))
# 配置允许域名列表、允许方法、请求头、cookie等
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


message = "-内存超限，本次任务取消,稍后再试"
# 冗余500M内存
total_memory = 16-0.5


@app.exception_handler(InspectionException)
def unicorn_exception_handler(request: Request, exc: InspectionException):
    logger.exception(exc)
    return JSONResponse(
        status_code=status.HTTP_200_OK,
        content={
            'code': exc.code,
            'msg': f'执行异常:{exc.message}',
            'data': None
        }
    )


@app.exception_handler(AlgorithmProcessException)
def unicorn_exception_handler(request: Request, exc: AlgorithmProcessException):
    logger.exception(exc)
    return JSONResponse(
        status_code=status.HTTP_200_OK,
        content={
            'code': exc.code,
            'msg': f'检测异常:{exc.message}',
            'data': None
        }
    )


@app.exception_handler(AlgorithmCheckException)
def unicorn_exception_handler(request: Request, exc: AlgorithmCheckException):
    logger.exception(exc)
    return JSONResponse(
        status_code=status.HTTP_200_OK,
        content={
            'code': exc.code,
            'msg': f'参数异常:{exc.message}',
            'data': None
        }
    )


@app.exception_handler(Exception)
def unicorn_exception_handler(request: Request, exc: Exception):
    logger.exception(exc)
    return JSONResponse(
        status_code=status.HTTP_200_OK,
        content={
            'code': -1,
            # 'msg': f'内部错误:{exc.__str__()}',
            'msg': f'内部错误: 请查看日志',
            'data': None
        }
    )


@app.exception_handler(HTTPException)
def unicorn_exception_handler(request: Request, exc: HTTPException):
    logger.exception(exc)
    return JSONResponse(
        status_code=status.HTTP_200_OK,
        content={
            'code': -1,
            'msg': f'请求失败:{exc.detail}',
            'data': None
        }
    )


@app.exception_handler(RequestValidationError)
def request_validation_exception_handler(
        request: Request, exc: RequestValidationError
) -> JSONResponse:
    logger.exception(exc)
    return JSONResponse(
        status_code=status.HTTP_200_OK,
        content={
            'code': -1,
            'msg': f'校验错误:{exc.errors()}',
            'data': None
        }
    )

# 执行算法
@app.post("/execute")
def execute(request_param: dict):
    maths_mgmt = MathABMgmt(request_param)
    data = maths_mgmt.execute()
    return response(data=data)


@app.post("/block-upload")
def upload_block(file: UploadFile, name: str = Form(...)):
    maths_mgmt = MathABMgmt({"file": file, "name": name})
    ret, msg = maths_mgmt.block_import()
    return response(data=msg) if ret else response(code=math_constants.UPLOAD_ERROR, data=msg)

@app.post("/block-remove")
def remove_block(code: str = Form(...)):
    maths_mgmt = MathABMgmt({"code": code})
    ret, msg = maths_mgmt.block_remove()
    return response(data=msg) if ret else response(code=math_constants.REMOVE_ERROR, data=msg)


@app.get("/ping")
def ping_health():
    return response(data="pong")

@app.post("/tasks/start")
async def start_single_task_api(config: TaskConfig = Body(None)):
    global tasks
    taskId = config.taskId
    logger.info(f"[{taskId}] 🕒 API 请求启动任务")
    if taskId in tasks:
        if tasks[taskId]["status"] == "running":
            logger.info(f"[{taskId}] ✅ 任务已在运行中，停止旧任务后重新启动")
            await stop_single_task({"taskId": taskId})
            tasks[taskId] = {
                "config": config.model_dump(),
                "status": "stopped",
                "thread": None,
                "stop_event": threading.Event(),
                "last_annotated_frame": None,
                "frame_queue": Queue(maxsize=5),
                "result_queue": Queue(maxsize=5)
            }
        tasks[taskId]["config"] = config.model_dump()
        reschedule_task(taskId)
        return {"taskId": taskId, "status": "updated_and_started"}

    if taskId not in tasks and config is not None:
        tasks[taskId] = {
            "config": config.model_dump(),
            "status": "stopped",
            "thread": None,
            "stop_event": threading.Event(),
            "last_annotated_frame": None,
            "frame_queue": Queue(maxsize=5),
            "result_queue": Queue(maxsize=5)
        }
        logger.info(f"✅ 动态创建新任务: {taskId}")
        # thread = threading.Thread(target=run_task, args=(taskId,), daemon=True)
        # tasks[taskId]["thread"] = thread
        # thread.start()
        reschedule_task(taskId)
        return {"taskId": taskId, "status": "created_and_started"}

    raise HTTPException(status_code=400, detail="任务不存在且未提供配置")

@app.post("/tasks/stop")
async def stop_single_task(config: dict):
    global tasks
    taskId = config['taskId']

    if scheduler:
        # 获取所有 job
        jobs = scheduler.get_jobs()
        prefix = f"task_{taskId}_"

        for job in jobs:
            if job.id.startswith(prefix):  # 匹配 task_{taskId}_start_x 或 stop_x
                scheduler.remove_job(job.id)
                logger.info(f"[{taskId}] 🗑️ 已移除调度任务: {job}")

    if taskId not in tasks:
        raise HTTPException(status_code=404, detail="任务不存在")
    if tasks[taskId]["status"] != "running":
        del tasks[taskId]
        return {"status": "not_running"}
    tasks[taskId]["status"] = "stopping"
    tasks[taskId]["stop_event"].set()
    del tasks[taskId]
    return {"taskId": taskId, "status": "stopping"}

@app.post("/tasks/start_all")
async def start_all_tasks(config: List[TaskConfig] = Body([])):
    global tasks
    started = []
    for task in config:
        # if tasks[tid]["status"] != "running":
        #     reschedule_task(tid)
        await start_single_task_api(task)
        started.append(task)
    return {"status": "all_started", "tasks": started}

@app.post("/tasks/stop_all")
async def stop_all_tasks():
    global tasks
    for tid in tasks:
        if tasks[tid]["status"] == "running":
            tasks[tid]["status"] = "stopping"
            tasks[tid]["stop_event"].set()
    if scheduler and scheduler.running:
        scheduler.remove_all_jobs(jobstore='default')  # 删除 default 存储中的所有任务
        logger.info("🗑️ 已移除所有调度任务")
    tasks.clear()
    return {"status": "all_stopped"}

@app.get("/status")
async def system_status():
    global redis_client
    running = sum(1 for t in tasks.values() if t["status"] == "running")
    mesg = {
        "total_tasks": len(tasks),
        "running_tasks": running,
        "redis_connected": bool(redis_client and redis_client.ping()),
        "scheduler_running": list_all_jobs(scheduler) #scheduler.running
    }
    return mesg

@app.post("/tasks/status")
async def single_task_status(config: dict):
    global tasks
    """
    获取单个任务状态
    """
    taskId = config['taskId']
    if taskId not in tasks:
        mesg =  {"task_id": taskId, "status": "not exist", "taskInfo": None}
        return response(data=mesg)
    task = tasks[taskId]
    mesg = {"task_id": taskId, "status": task["status"], "taskInfo": task["config"]}
    return response(data=mesg)

if __name__ == '__main__':
    # conda activate detect
    # uvicorn application_depl:app --host 0.0.0.0 --reload --port 6500
    reload = json.loads(os.getenv("UVICORN_RELOAD", 'true').lower())
    uvicorn.run(app='application_depl:app', host="0.0.0.0", port=6300)  # , reload=reload,)
    # workers=multiprocessing.cpu_count() * 2)
