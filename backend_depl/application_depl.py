import json
import json
import multiprocessing
import os

import uvicorn
from fastapi import Fast<PERSON><PERSON>, status, HTTPException, UploadFile, Form
from fastapi.exceptions import RequestValidationError
from fastapi.responses import JSONResponse
from fastapi_cache.decorator import cache
from loguru import logger
from starlette.middleware.cors import CORSMiddleware
from starlette.requests import Request

from backend_common.constants import math_constants
from backend_common.exceptions.inspection_exception import InspectionException, AlgorithmCheckException, \
    AlgorithmProcessException
from backend_common.models.response.responseBody import response
from listener.ListenerEvent import app_startup_event, app_shutdown_event
from service.mathsMgmtService import MathABMgmt


current_count = 0
max_count = int(os.getenv('able_current_max_count', 1))
print("max_count:{}, current_count:{}".format(max_count, current_count))
app = FastAPI()

app.add_event_handler("startup", app_startup_event(app))

app.add_event_handler("shutdown", app_shutdown_event(app))
# 配置允许域名列表、允许方法、请求头、cookie等
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

algorithm_memory = {"person_cross": 0, "phone": 0, "workclothes": 0, "reflective_clothes": 0, "safetybelt": 0,
                   "fire_smoke": 0, "smoke": 0, "goggles": 0, "common_det": 0, "paomaodilou": 0,
                   "position_status": 0, "ppocr": 0, "total": 0}
message = "-内存超限，本次任务取消,稍后再试"
# 荣誉500M内存
total_memory = 16-0.5


def increase_algorithm_memory(algorithm_code):
    current_memory = algorithm_memory["total"]
    if algorithm_code == "paomaodilou":
        if current_memory + 7.5 > total_memory:
            raise InspectionException("paomaodilou" + message)
        algorithm_memory["paomaodilou"] = + 7.5
        algorithm_memory["total"] = + 7.5
        return 7.5
    elif algorithm_code == "person_cross":
        if current_memory + 1 > total_memory:
            raise InspectionException("person_cross" + message)
        algorithm_memory["person_cross"] = + 1
        algorithm_memory["total"] = + 1
        return 1
    elif algorithm_code == "ppocr":
        if current_memory + 2.5 > total_memory:
            raise InspectionException("ppocr" + message)
        algorithm_memory["ppocr"] = + 2.5
        algorithm_memory["total"] = + 2.5
    elif algorithm_code == "goggles":
        if algorithm_memory['phone'] != 0.0 or algorithm_memory['smoke'] != 0.0:
            if current_memory + 0.5 > total_memory:
                raise InspectionException("goggles" + message)
            algorithm_memory["goggles"] = + 0.5
            algorithm_memory["total"] = + 0.5
            return 0.5
        else:
            if current_memory + 2.5 > total_memory:
                raise InspectionException("person_cross" + message)
            algorithm_memory["goggles"] = + 2.5
            algorithm_memory["total"] = + 2.5
            return 2.5
    elif algorithm_code == "phone":
        if algorithm_memory['goggles'] != 0.0 or algorithm_memory['smoke'] != 0.0:
            if current_memory + 0.5 > total_memory:
                raise InspectionException("phone" + message)
            algorithm_memory["phone"] = + 0.5
            algorithm_memory["total"] = + 0.5
            return 0.5
        else:
            if current_memory + 2.5 > total_memory:
                raise InspectionException("phone" + message)
            algorithm_memory["phone"] = + 2.5
            algorithm_memory["total"] = + 2.5
            return 2.5
    elif algorithm_code == "smoke":
        if algorithm_memory['goggles'] != 0.0 or algorithm_memory['phone'] != 0.0:
            if current_memory + 0.5 > total_memory:
                raise InspectionException("smoke" + message)
            algorithm_memory["smoke"] = + 0.5
            algorithm_memory["total"] = + 0.5
            return 0.5
        else:
            if current_memory + 2.5 > total_memory:
                raise InspectionException("smoke" + message)
            algorithm_memory["smoke"] = + 2.5
            algorithm_memory["total"] = + 2.5
            return 2.5
    elif algorithm_code == "reflective_clothes":
        if algorithm_memory['safetybelt'] != 0.0 or algorithm_memory['workclothes'] != 0.0:
            if current_memory + 0.5 > total_memory:
                raise InspectionException("reflective_clothes" + message)
            algorithm_memory["reflective_clothes"] = + 0.5
            algorithm_memory["total"] = + 0.5
            return 0.5
        else:
            if current_memory + 2.5 > total_memory:
                raise InspectionException("reflective_clothes" + message)
            algorithm_memory["reflective_clothes"] = + 2.5
            algorithm_memory["total"] = + 2.5
            return 2.5
    elif algorithm_code == "safetybelt":
        if algorithm_memory['reflective_clothes'] != 0.0 or algorithm_memory['workclothes'] != 0.0:
            if current_memory + 0.5 > total_memory:
                raise InspectionException("safetybelt" + message)
            algorithm_memory["safetybelt"] = + 0.5
            algorithm_memory["total"] = + 0.5
            return 0.5
        else:
            if current_memory + 2.5 > total_memory:
                raise InspectionException("safetybelt" + message)
            algorithm_memory["safetybelt"] = + 2.5
            algorithm_memory["total"] = + 2.5
            return 2.5
    elif algorithm_code == "workclothes":
        if algorithm_memory['reflective_clothes'] != 0.0 or algorithm_memory['safetybelt'] != 0.0:
            if current_memory + 0.5 > total_memory:
                raise InspectionException("workclothes" + message)
            algorithm_memory["workclothes"] = + 0.5
            algorithm_memory["total"] = + 0.5
            return 0.5
        else:
            if current_memory + 2.5 > total_memory:
                raise InspectionException("workclothes" + message)
            algorithm_memory["workclothes"] = + 2.5
            algorithm_memory["total"] = + 2.5
            return 2.5
    elif algorithm_code == "position_status":
        if current_memory + 1 > total_memory:
            raise InspectionException("position_status" + message)
        algorithm_memory["position_status"] = + 1
        algorithm_memory["total"] = + 1
        return 1
    else:
        if current_memory + 1 > total_memory:
            raise InspectionException(algorithm_code + message)
        algorithm_memory["total"] = + 1
        return 1

def decrease_algorithm_memory(algorithm_code, memory):
    algorithm_memory[algorithm_code] = - memory
    algorithm_memory["total"] = - memory
    pass


@app.exception_handler(InspectionException)
def unicorn_exception_handler(request: Request, exc: InspectionException):
    logger.exception(exc)
    return JSONResponse(
        status_code=status.HTTP_200_OK,
        content={
            'code': exc.code,
            'msg': f'执行异常:{exc.message}',
            'data': None
        }
    )


@app.exception_handler(AlgorithmProcessException)
def unicorn_exception_handler(request: Request, exc: AlgorithmProcessException):
    logger.exception(exc)
    return JSONResponse(
        status_code=status.HTTP_200_OK,
        content={
            'code': exc.code,
            'msg': f'检测异常:{exc.message}',
            'data': None
        }
    )


@app.exception_handler(AlgorithmCheckException)
def unicorn_exception_handler(request: Request, exc: AlgorithmCheckException):
    logger.exception(exc)
    return JSONResponse(
        status_code=status.HTTP_200_OK,
        content={
            'code': exc.code,
            'msg': f'参数异常:{exc.message}',
            'data': None
        }
    )


@app.exception_handler(Exception)
def unicorn_exception_handler(request: Request, exc: Exception):
    logger.exception(exc)
    return JSONResponse(
        status_code=status.HTTP_200_OK,
        content={
            'code': 500,
            # 'msg': f'内部错误:{exc.__str__()}',
            'msg': f'内部错误: 请查看日志',
            'data': None
        }
    )


@app.exception_handler(HTTPException)
def unicorn_exception_handler(request: Request, exc: HTTPException):
    logger.exception(exc)
    return JSONResponse(
        status_code=status.HTTP_200_OK,
        content={
            'code': 500,
            'msg': f'请求失败:{exc.detail}',
            'data': None
        }
    )


@app.exception_handler(RequestValidationError)
def request_validation_exception_handler(
        request: Request, exc: RequestValidationError
) -> JSONResponse:
    logger.exception(exc)
    return JSONResponse(
        status_code=status.HTTP_200_OK,
        content={
            'code': 422,
            'msg': f'校验错误:{exc.errors()}',
            'data': None
        }
    )



# 执行算法
@app.post("/execute")
def execute(request_param: dict):
    global current_count, max_count
    # print('start-----------',current_count, max_count)
    if request_param['code']=='paomaodilou':
        if current_count >= max_count:
            raise InspectionException('超过最大限制, current_count:{}, max_count:{}'.format(current_count, max_count))
        #memory = increase_algorithm_memory(request_param['code'])
        current_count = current_count+1
        # print('start--111-----------',current_count, max_count)
        try:
            maths_mgmt = MathABMgmt(request_param)
            data = maths_mgmt.execute()
        except ModuleNotFoundError as e:
            raise InspectionException(message=e.msg)
        except InspectionException as e:
            raise InspectionException(message=e.msg)
        except AlgorithmCheckException as e:
            raise AlgorithmCheckException(message=e.message)
        except AlgorithmProcessException as e:
            raise AlgorithmProcessException(message=e.message)
        except Exception as e:
            raise Exception(e.__str__())
        finally:
            current_count = current_count-1

        # print('end----------------',current_count, max_count)
    else:
        maths_mgmt = MathABMgmt(request_param)
        data = maths_mgmt.execute()
    #print()
    #decrease_algorithm_memory(request_param['code'], memory)
    return response(data=data)


@app.get("/algorithm-detail/{code}/{a_type}")
@cache(namespace="ns-alg-dtl", expire=24 * 3600)
def ab_detail(code: str, a_type: str):
    maths_mgmt = MathABMgmt({"code": code, "a_type": a_type})
    return response(data=maths_mgmt.get_detail())


@app.post("/block-upload")
def upload_block(file: UploadFile, name: str = Form(...)):
    maths_mgmt = MathABMgmt({"file": file, "name": name})
    ret, msg = maths_mgmt.block_import()
    return response(data=msg) if ret else response(code=math_constants.UPLOAD_ERROR, data=msg)


@app.get("/ping")
def ping_health():
    return response(data="pong")


@app.get("/update-cls-gallery")
def gallery_update():
    maths_mgmt = MathABMgmt({})
    ret, msg = maths_mgmt.update_cls_gallery()
    return response(data=msg) if ret else response(code=math_constants.UPLOAD_ERROR, data=msg)


if __name__ == '__main__':
    # conda activate detect
    # uvicorn application_depl:app --host 0.0.0.0 --reload --port 6500
    reload = json.loads(os.getenv("UVICORN_RELOAD", 'true').lower())
    uvicorn.run(app='application_depl:app', host="0.0.0.0", port=6300)  # , reload=reload,)
    # workers=multiprocessing.cpu_count() * 2)
