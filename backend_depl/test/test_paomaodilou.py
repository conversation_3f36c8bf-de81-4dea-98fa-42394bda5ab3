#!/usr/bin/env python

"""Tests for `IndicatorFlashReader` algorithm."""
import importlib
import os
from typing import List

import cv2
import pytest

# from backend_common.constants.alarm import AlarmRule, ExceedLimitAlarmRule
# from backend_common.constants.constant import AlgorithmClassifyEnum, AlgorithmStrategyEnum, AlgorithmOutputTypeEnum
from backend_common.constants.math_constants import ALGORITHM_DEPL_USER_DEFINE_PATH, ALGORITHM_DEPL_SYSTEM_INNER_PATH, ALGORITHM_DEPL_USER_DEFINE_BASE_YOLO
from backend_common.exceptions.inspection_exception import InspectionException
from backend_common.maths.algorithm.base.algorithm_base import AlgorithmOutput, AlgorithmBase, AlgorithmInput
from backend_common.utils.util import to_camel_case
from backend_common.utils import MinioUtil

# clazz_name_prefix = "IndicatorFlashReader"

# 深度学习算法块 MAP
DEEPLEARN_BLOCK_MAP = dict()

def do_itself():
    # settings = AlgorithmSettings(classify, strategy, alarm_rules=alarm_rules)

    # algorithm_name = "indicator_flash"
    # TODO 默认采样频率 500ms + 采样时长 5s, 即 10 张图片


	request_param = {
		"code": "paomaodilou",
		"images": [
			"./test/resources/input-img/paomaodilou/water.jpg"
		],
		"roi": {
			"dataType": "SQUARE",
			"constraints": {
				"required": False
			}
		},
		"sceneImage": "http://minio:9001/inspection/SCENE/202507221752191416.JPEG",
		"userOrSystem": "system_inner",
		"inputParam": [
			{
				"algorithmInstanceId": "c135d34e42ddd4cc49856e233a319406",
				"sceneId": "48411fe684c3446eccc37690d0eed420",
                "value": None,
				"algorithmId": "fac447d27c2a324cf07aa3e74d1ab6c3",
				"key": "input_roi",
				"label": "ROI区域",
				"dataType": "SQUARE",
				"constraints": {
					"required": False
				},
				"drawToOsd": False,
				"sortNo": 0,
				"outOrIn": "IN"
			},
			{
				"algorithmInstanceId": "c135d34e42ddd4cc49856e233a319406",
				"sceneId": "48411fe684c3446eccc37690d0eed420",
				"value": 0.5,
				"algorithmId": "fac447d27c2a324cf07aa3e74d1ab6c3",
				"key": "conf",
				"label": "框检测阈值",
				"dataType": "FLOAT",
				"defaultValue": "0.5",
				"constraints": {
					"required": True,
					"max": 1,
					"min": 0,
					"precision": 0.01
				},
				"drawToOsd": False,
				"sortNo": 1,
				"outOrIn": "IN"
			},
			{
				"algorithmInstanceId": "c135d34e42ddd4cc49856e233a319406",
				"sceneId": "48411fe684c3446eccc37690d0eed420",
				"value": 0.7,
				"algorithmId": "fac447d27c2a324cf07aa3e74d1ab6c3",
				"key": "iou",
				"label": "iou阈值",
				"dataType": "FLOAT",
				"defaultValue": "0.7",
				"constraints": {
					"required": True,
					"max": 1,
					"min": 0,
					"precision": 0.01
				},
				"drawToOsd": False,
				"sortNo": 2,
				"outOrIn": "IN"
			}
		]
	}
	inputs = AlgorithmInput()

	images = request_param['images']

	# 现在不需要Minio下载图片到本地路径
	# new_image_arr = []
	# for img_path in images:
	#     # 下载图片到本地
	#     local_path = MinioUtil.download(img_path)
	#     new_image_arr.append(local_path)
	inputs['images'] = images

	roi = request_param['roi']
	inputs['roi'] = roi

	inputParam = request_param['inputParam']
	inputs['inputParam'] = inputParam

	method_code = request_param['code']
	user_or_system = request_param['userOrSystem']

	# 输入参数校验
	# self.param_check.do_check_param(inputs)
    # # 输入参数校验
	from backend_common.maths.algorithm.base.parameter_check import ParameterCheck
	param_check = ParameterCheck()
	param_check.do_check_param(inputs)

	# yolo_model_paomaodilou = DEEPLEARN_BLOCK_MAP.get("paomaodilou").model
	# algorithm_instance = DEEPLEARN_BLOCK_MAP.get(f"{method_code}")
	# if algorithm_instance is None:
	# 	if method_code in ALGORITHM_DEPL_USER_DEFINE_BASE_YOLO.keys():
	# 		algorithm_instance = load_algorithm_super(method_code, user_or_system, inputs, reload_model=yolo_model_paomaodilou)
	# 	else:
	# 		algorithm_instance = load_algorithm_super(method_code, user_or_system, inputs)
	# 	DEEPLEARN_BLOCK_MAP[f"{method_code}"] = algorithm_instance
	# else:
	# 	algorithm_instance.init(inputs)
	algorithm_instance = load_algorithm_super(method_code, user_or_system, inputs)
	assert algorithm_instance is not None, "无法加载算法实例!!!"
	# 算法执行
	isSuccess, output, osdInfo = algorithm_instance.perform()
	print(isSuccess, output, osdInfo)
	outputs = AlgorithmOutput(isSuccess, output, osdInfo)
	print(f"outputs {str(outputs)}")
	return outputs

def load_algorithm_super(algorithm_name, a_type, inputs: AlgorithmInput, reload_model=None):
    # 算法块唯一标识名,通常需与算法块文件夹名称一致，比如 liquid_level
    module_name = f".{algorithm_name}.ab_{algorithm_name}"
    target_clazz_name = f"{to_camel_case(algorithm_name, '_')}Reader"

    base_path = ALGORITHM_DEPL_SYSTEM_INNER_PATH
    if a_type == "user_define":
        base_path = ALGORITHM_DEPL_USER_DEFINE_PATH

    try:
        print(f"algorithm_depl_base_path : {base_path}")
        print(f"module_name : {module_name}")
        print(f"target_clazz_name : {target_clazz_name}")

        M = importlib.import_module(module_name, base_path)
        target_clazz_ = getattr(M, target_clazz_name)
    except ModuleNotFoundError:
        raise InspectionException(
            f"load_algorithm_super module '{module_name}' from '{base_path}' failure, please check it manually")
    if reload_model is not None:
        algorithm_instance: AlgorithmBase = target_clazz_(inputs, reload_model)
    else:
        algorithm_instance: AlgorithmBase = target_clazz_(inputs)
    return algorithm_instance


@pytest.fixture
def primary_detect():
    return do_itself()

def test_primary(primary_detect):
    assert primary_detect.isSuccess is True

