import subprocess

import cv2
import numpy as np

SKIP_FRAMES = 1


# 推流器
class StreamPusher:

    def __init__(self, src_video_path, rtmp_url):
        # Open input video with OpenCV
        video_in = cv2.VideoCapture(src_video_path)
        frame_width = int(video_in.get(cv2.CAP_PROP_FRAME_WIDTH))
        frame_height = int(video_in.get(cv2.CAP_PROP_FRAME_HEIGHT))
        fps = video_in.get(cv2.CAP_PROP_FPS)
        # frame_count = int(video_in.get(cv2.CAP_PROP_FRAME_COUNT))
        # bitrate = bitrate * 4096 * 2160 / (frame_width * frame_height)

        # Process video in ffmpeg pipe
        # See http://zulko.github.io/blog/2013/09/27/read-and-write-video-frames-in-python-using-ffmpeg/
        ffmpeg_cmd = ['ffmpeg',
                      # '-rtmp_live', '1',
                      '-loglevel', 'error',
                      '-y',
                      # Input
                      '-f', 'rawvideo',
                      '-vcodec', 'rawvideo',
                      '-pix_fmt', 'bgr24',
                      '-s', str(frame_width) + 'x' + str(frame_height),
                      '-r', str(fps),
                      '-i', '-',
                      # Output
                      '-an',
                      '-c:v', 'libx264',
                      '-r', str(fps),
                      # '-b:v', str(bitrate) + 'M',
                      '-f', 'flv',
                      # '-pix_fmt', 'bgr24',
                      rtmp_url
                      ]
        print('ffmpeg_cmd:', ffmpeg_cmd)
        # 启动 ffmpeg
        self.ffmpeg_process = subprocess.Popen(ffmpeg_cmd, stdin=subprocess.PIPE)

    def stream_push(self, frame):
        self.ffmpeg_process.stdin.write(frame.tobytes())
        # self.ffmpeg_process.stdin.flush()

    def close(self):
        self.ffmpeg_process.stdin.close()
        self.ffmpeg_process.wait()


Tpar = [0.001021, -0.000223976568957973, 0.000198664507931246]


def tCalculate(frame, k):
    """
    温度计算
    """
    # frame[frame == 0] = 1
    frame = frame.astype(float)
    r = frame[:, :, 1] / frame[:, :, 0]
    lnr = np.log(r)
    T = 1.0 / (k[0] + k[1] * lnr + k[2] * lnr * lnr)

    return T


def test_d():
    # 输入视频流地址
    # input_stream = 'rtmp://************:1935/192_168_253_105/c84a044ad74fd6b28f5620b8b45ddd2f'
    input_stream = 'rtsp://admin:Qqwe1234@***************/Streaming/Channels/101'
    # input_stream = 'rtsp://***************/6l'

    # 输出视频流地址
    output_stream = 'rtmp://************:1935/hyperion/1'

    pusher = StreamPusher(input_stream, output_stream)

    # 打开输出视频流
    # fourcc = cv2.VideoWriter_fourcc(*'X264')  # 您可以根据需要更改编解码器
    # output_video = cv2.VideoWriter(output_stream, fourcc, 30.0, (640, 480))  # 更改分辨率和帧率

    cap = cv2.VideoCapture(input_stream)
    frame_count = 0
    ret, first_frame = cap.read()
    recent_frame = first_frame

    while True:
        # 读取帧
        # raw_frame = ffmpeg_process.stdout.read(640 * 480 * 3)  # 根据分辨率调整帧大小
        ret, raw_frame = cap.read()
        if not ret:
            break

        frame_count += 1
        if frame_count % SKIP_FRAMES == 0:
            # 在此处可以添加您的OpenCV图像处理操作
            # 例如，可以添加滤镜、文本或其他特效
            print(f"do frame process here :: {frame_count}")

            #   # 可根据需要更改)

            T = tCalculate(raw_frame, Tpar)
            gray = cv2.cvtColor(raw_frame, cv2.COLOR_BGR2GRAY)
            _, BW = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
            T[~BW.astype(bool)] = np.nan

            # frame = cv2.normalize(T, raw_frame, 0, 1, cv2.NORM_MINMAX)
            T = np.nan_to_num(T, nan=0)
            frame = cv2.applyColorMap(raw_frame, cv2.COLORMAP_JET)

            # 将处理后的帧写入输出视频流
            # output_video.write(frame)
            # cv2.imwrite(f"frame{frame_count}.jpg", frame)
            pusher.stream_push(frame)
            recent_frame = frame

        pusher.stream_push(recent_frame)

    cap.release()
    pusher.close()
    print('Done')


if __name__ == '__main__':
    test_d()
