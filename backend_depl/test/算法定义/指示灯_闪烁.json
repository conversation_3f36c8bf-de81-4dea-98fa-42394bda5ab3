{"code": "CODE_10088", "name": "指示灯闪烁识别", "url": "http://localhost:5000/identify/light/1", "roiSquare": "[{\"x\":540,\"y\":195},{\"x\":1361,\"y\":195},{\"x\":1361,\"y\":1062},{\"x\":540,\"y\":1062}]", "imgPath": "[\"'D:\\Pythoncode\\learningcv\\box\\images\\test_.jpg\"]", "des": "指示灯闪烁识别", "isCompareTemp": true, "inputParam": {"ligth1_position": {"id": 161, "algorithmId": 36, "name": "指示灯1上限报警", "type": "circle", "key": "key1", "des": "上限报警", "cruisePointId": 6, "value": "[1139,233,44]"}, "ligth2_position": {"id": 162, "algorithmId": 36, "name": "指示灯2下限报警", "type": "circle", "key": "key2", "des": "下限报警", "cruisePointId": 6, "value": "[1148,388,41]"}, "light1_gray": {"id": 165, "algorithmId": 36, "name": "指示灯1灰值", "type": "number", "key": "key5", "des": "灰度值", "cruisePointId": 6, "value": "150"}, "light2_gray": {"id": 166, "algorithmId": 36, "name": "指示灯2灰值", "type": "number", "key": "key6", "des": "灰度值", "cruisePointId": 6, "value": "150"}, "light1_status": {"id": 163, "algorithmId": 36, "name": "指示灯1状态", "type": "number", "key": "key3", "des": "指示灯1亮灭", "cruisePointId": 6, "value": "1"}, "light2_status": {"id": 164, "algorithmId": 36, "name": "指示灯2状态", "type": "number", "key": "key4", "des": "指示灯2亮灭", "cruisePointId": 6, "value": "1"}, "light1_bright": {"id": 167, "algorithmId": 36, "name": "指示灯1亮度", "type": "number", "key": "key7", "des": "亮度值", "cruisePointId": 6, "value": "141"}, "light2_bright": {"id": 168, "algorithmId": 36, "name": "指示灯2亮度", "type": "number", "key": "key8", "des": "亮度值", "cruisePointId": 6, "value": "143"}}, "constantList": [{"algorithmParamKey": "light1Cache", "name": "light1Cache", "type": "模拟量", "des": "指示灯闪烁识别", "value": "-", "time": "2022-12-21 16:59:03"}], "outputParam": {"light1Cache": {"name": "字符串", "type": "string", "value": "9263", "des": "指示灯闪灭识别"}, "audio": {"name": "字符串", "type": "audio", "value": "hello world", "des": "将发送kafka，语音播报"}}}