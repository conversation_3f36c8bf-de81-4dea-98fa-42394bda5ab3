{"name": "圆盘仪表指针读数算法", "code": "CODE_10094", "url": "http://localhost:5000/identify/appearance/1", "imgPath": "[\"'D:\\Pythoncode\\learningcv\\box\\images\\test_.jpg\"]", "des": "圆盘仪表指针读数算法", "isCompareTemp": true, "inputParam": {"roiSquare": {"name": "圆心坐标及半径", "type": "circle", "value": "[1148,423,55]", "des": "圆心坐标及半径"}, "GearUp": {"name": "正常范围上限", "type": "point", "value": "{'x':1144.0,'y':366.0}", "des": "一个point对象，点的坐标"}, "GearDown": {"name": "正常范围下限", "type": "point", "value": "{'x':1209,'y':391}", "des": "一个point对象，点的坐标"}, "zeroPoint": {"name": "零点位置", "type": "point", "value": "{'x':1076,'y':443}", "des": "一个point对象，点的坐标"}, "startPoint": {"name": "起点位置", "type": "point", "value": "{'x':1153,'y':486}", "des": "一个point对象，点的坐标"}, "endPoint": {"name": "终点位置", "type": "point", "value": "{'x':1215,'y':427}", "des": "一个point对象，点的坐标"}, "rangeUp": {"name": "量程上限", "type": "number", "value": "-20", "des": "数字类型"}, "rangeDown": {"name": "量程下限", "type": "number", "value": "50", "des": "数字类型"}}, "constantList": [{"algorithmParamKey": "appearance1<PERSON>ache", "name": "appearance1<PERSON>ache", "des": "圆盘仪表指针读数算法", "time": "2022-12-21 16:59:03"}], "outputParam": {"appearance1Cache": {"name": "整数", "type": "string", "value": "9263", "des": "圆盘仪表指针读数算法"}, "audio": {"name": "字符串", "type": "audio", "value": "hello world", "des": "将发送kafka，语音播报"}}}