{"code": "CODE_10088", "imgPath": "[\"D:\\\\\\\\project\\\\\\\\pic\\\\\\\\202301181618071.jpg\",\"D:\\\\\\\\project\\\\\\\\pic\\\\\\\\202301181618271.jpg\",\"D:\\\\\\\\project\\\\\\\\pic\\\\\\\\202301181618281.jpg\",\"D:\\\\\\\\project\\\\\\\\pic\\\\\\\\202301181618291.jpg\",\"D:\\\\\\\\project\\\\\\\\pic\\\\\\\\202301181618291.jpg\"]", "inputParam": {"light2_bright": {"algorithmId": 1, "cruisePointId": 2, "des": "亮度值", "id": 606, "key": "light2_bright", "name": "指示灯2亮度", "outOrIn": "input", "type": "number", "value": "144"}, "light1_status": {"algorithmId": 1, "cruisePointId": 2, "des": "指示灯1亮灭", "id": 603, "key": "light1_status", "name": "指示灯1状态", "outOrIn": "input", "type": "number", "value": "1"}, "light1_bright": {"algorithmId": 1, "cruisePointId": 2, "des": "亮度值", "id": 605, "key": "light1_bright", "name": "指示灯1亮度", "outOrIn": "input", "type": "number", "value": "140"}, "ligth2_position": {"algorithmId": 1, "cruisePointId": 2, "des": "下限报警", "id": 600, "key": "ligth2_position", "name": "指示灯2下限报警", "outOrIn": "input", "type": "circle", "value": "[1245,484,37]"}, "light2_gray": {"algorithmId": 1, "cruisePointId": 2, "des": "灰度值", "id": 602, "key": "light2_gray", "name": "指示灯2灰值", "outOrIn": "input", "type": "number"}, "ligth1_position": {"algorithmId": 1, "cruisePointId": 2, "des": "上限报警", "id": 599, "key": "ligth1_position", "name": "指示灯1上限报警", "outOrIn": "input", "type": "circle", "value": "[1239,345,39]"}, "light2_status": {"algorithmId": 1, "cruisePointId": 2, "des": "指示灯2亮灭", "id": 604, "key": "light2_status", "name": "指示灯2状态", "outOrIn": "input", "type": "number", "value": "0"}, "light1_gray": {"algorithmId": 1, "cruisePointId": 2, "des": "灰度值", "id": 601, "key": "light1_gray", "name": "指示灯1灰值", "outOrIn": "input", "type": "number"}}, "roiSquare": "[{\"x\":758,\"y\":155},{\"x\":1539,\"y\":155},{\"x\":1539,\"y\":1014},{\"x\":758,\"y\":1014}]"}