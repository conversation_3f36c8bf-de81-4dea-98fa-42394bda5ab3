{"code": "CODE_10094", "imgPath": "[\"D:\\\\\\\\project\\\\\\\\pic\\\\\\\\202301161741391.jpg\"]", "inputParam": {"endPoint": {"algorithmId": 3, "cruisePointId": 3, "des": "一个point对象，点的坐标", "id": 554, "key": "endPoint", "name": "终点位置", "outOrIn": "input", "type": "point", "value": "{\"x\":1298,\"y\":755}"}, "rangeUp": {"algorithmId": 3, "cruisePointId": 3, "des": "数字类型", "id": 555, "key": "rangeUp", "name": "量程上限", "outOrIn": "input", "type": "number", "value": "-30"}, "roiSquare": {"algorithmId": 3, "cruisePointId": 3, "des": "圆心坐标及半径", "id": 549, "key": "roiSquare", "name": "圆心坐标及半径", "outOrIn": "input", "type": "circle", "value": "[1113,672,198]"}, "rangeDown": {"algorithmId": 3, "cruisePointId": 3, "des": "数字类型", "id": 556, "key": "rangeDown", "name": "量程下限", "outOrIn": "input", "type": "number", "value": "50"}, "GearDown": {"algorithmId": 3, "cruisePointId": 3, "des": "一个point对象，点的坐标", "id": 551, "key": "GearDown", "name": "正常范围下限", "outOrIn": "input", "type": "point", "value": "{\"x\":1178,\"y\":489}"}, "zeroPoint": {"algorithmId": 3, "cruisePointId": 3, "des": "一个point对象，点的坐标", "id": 552, "key": "zeroPoint", "name": "零点位置", "outOrIn": "input", "type": "point", "value": "{\"x\":988,\"y\":515}"}, "startPoint": {"algorithmId": 3, "cruisePointId": 3, "des": "一个point对象，点的坐标", "id": 553, "key": "startPoint", "name": "起点位置", "outOrIn": "input", "type": "point", "value": "{\"x\":933,\"y\":747}"}, "GearUp": {"algorithmId": 3, "cruisePointId": 3, "des": "一个point对象，点的坐标", "id": 550, "key": "GearUp", "name": "正常范围上限", "outOrIn": "input", "type": "point", "value": "{\"x\":1036,\"y\":492}"}}, "roiSquare": "[{\"x\":813,\"y\":379},{\"x\":1404,\"y\":379},{\"x\":1404,\"y\":959},{\"x\":813,\"y\":959}]"}