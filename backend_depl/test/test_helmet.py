#!/usr/bin/env python

"""Tests for `HelmetReader` algorithm."""
import importlib
import os

import cv2
import pytest

from backend_common.constants.alarm import AlarmRule
from backend_common.constants.constant import AlgorithmClassifyEnum, AlgorithmStrategyEnum, AlgorithmOutputTypeEnum
from backend_common.constants.math_constants import ALGORITHM_DEPL_USER_DEFINE_PATH, ALGORITHM_DEPL_SYSTEM_INNER_PATH
from backend_common.exceptions.inspection_exception import InspectionException
from backend_common.maths.algorithm.base.algorithm_base import AlgorithmOutput, AlgorithmBase, AlgorithmSettings, AlgorithmInput
from backend_common.utils.util import to_camel_case


def do_itself(algorithm_index, classify, strategy, output_type, alarm_rule: AlarmRule = None):
    settings = AlgorithmSettings(classify, strategy, alarm_rules=[alarm_rule])
    basedir = os.path.dirname(os.path.abspath(__file__))
    algorithm_name = "helmet1"

    inputs = AlgorithmInput({"images": [
        os.path.join(basedir, "./resources/input-img/helmet/h4.jpg"),
        os.path.join(basedir, "./resources/input-img/helmet/h5.jpg"),
        # os.path.join(basedir, "./resources/pulc_demo_imgs/safety_helmet/safety_helmet_test_2.png"),
        # os.path.join(basedir, "./resources/input-img/helmet/h3.jpg")
        ]
    })

    sup: AlgorithmBase
    sup = load_algorithm_super(algorithm_name, "system_inner", inputs)
    target_instance = sup.determine_algorithm_instance(settings, algorithm_index)

    assert target_instance is not None
    ret, val, points, alarms = target_instance.perform(settings)
    print(f"结果 {ret, val, points, alarms}")

    outputs = AlgorithmOutput(ret, val, points, alarms)
    print(f"outputs {str(outputs)}")

    # 可选操作
    ret_images = target_instance.gen_result_img()
    if ret_images is not None:
        for idx, img in enumerate(ret_images):
            cv2.imwrite(os.getcwd() + f"/40_result_{idx}.jpg", img)

    return outputs


def load_algorithm_super(algorithm_name, a_type, inputs: AlgorithmInput):
    # self._name  算法块唯一标识名,通常需与算法块文件夹名称一致，比如 liquid_level
    module_name = f".{algorithm_name}.ab_{algorithm_name}"
    target_clazz_name = f"{to_camel_case(algorithm_name, '_')}Reader"

    base_path = ALGORITHM_DEPL_SYSTEM_INNER_PATH
    if a_type == "user_define":
        base_path = ALGORITHM_DEPL_USER_DEFINE_PATH

    try:
        M = importlib.import_module(module_name, base_path)
        target_clazz_ = getattr(M, target_clazz_name)
    except ModuleNotFoundError:
        raise InspectionException(
            f"Auto-load module '{module_name}' from '{base_path}' failure, please check it manually")

    algorithm_instance: AlgorithmBase = target_clazz_(inputs, -1)
    return algorithm_instance


@pytest.fixture
def primary_detect():
    algorithm_index = 0
    classify = AlgorithmClassifyEnum.Primary
    strategy = AlgorithmStrategyEnum.Main
    output_type = AlgorithmOutputTypeEnum.AlarmLevel

    return do_itself(algorithm_index, classify, strategy, output_type)


def test_primary(primary_detect):
    assert primary_detect.ret is True

    # http-request /execute json =====copy ====================================================
    # {"code": "helmet", "algorithmIndex": 0, "inputParam": {}, "images": [
    #     "E:\\workspace\\PycharmProjects\\intelligent_inspection\\py\\test\\resources\\input-img\\helmet\\h5.jpg"],
    #  "classify": "Primary", "strategy": "Main"}


def test_img_click():
    # mouse callback function
    def draw_circle(event, x, y, flags, param):
        if event == cv2.EVENT_LBUTTONDOWN:
            cv2.circle(img, (x, y), 100, (255, 0, 0), -1)
            print(f"[{x},{y}],")

    img = cv2.imread("resources/input-img/helmet/h5.jpg")

    cv2.imshow("img", img)
    cv2.setMouseCallback("img", draw_circle)
    cv2.waitKey()


def test_use_ppclas():
    import paddleclas
    # car_exists  language_classification  person_attribute  person_exists  safety_helmet  text_image_orientation
    # textline_orientation  traffic_sign  vehicle_attribute
    model = paddleclas.PaddleClas(model_name="safety_helmet")
    # result = model.predict(input_data="resources/pulc_demo_imgs/safety_helmet/safety_helmet_test_1.png")
    result = model.predict(input_data="resources/input-img/helmet/h2.jpg")
    print(next(result))
