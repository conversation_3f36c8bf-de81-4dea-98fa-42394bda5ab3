# from backend_depl.depl_maths.onnx_runner import LightGlueRunner, load_image, rgb_to_grayscale
# from depl_maths.onnx_runner import viz2d
#
#
# def test_dnn_forward_light_glue_trt():
#     """
#     分割模型 测试 :: onnx 格式
#     """
#     # onnxsim unet.onnx unet.fixed.b1.onnx --input-shape "1,3,128,128"
#     extrator_path = 'resources/models/superpoint.onnx'
#     lightglue_path = 'resources/models/superpoint_lightglue_fused_fp16.onnx'
#
#     image0_path = 'resources/input-img/sacre_coeur1.jpg'
#     image1_path = 'resources/input-img/sacre_coeur2.jpg'
#
#     image0, scales0 = load_image(image0_path, resize=512)
#     image1, scales1 = load_image(image1_path, resize=512)
#     image0 = rgb_to_grayscale(image0)  # only needed for SuperPoint
#     image1 = rgb_to_grayscale(image1)  # only needed for SuperPoint
#
#     providers = ["CUDAExecutionProvider", "CPUExecutionProvider"]
#     providers = [
#                     (
#                         "TensorrtExecutionProvider",
#                         {
#                             "trt_fp16_enable": True,
#                             "trt_engine_cache_enable": True,
#                             "trt_engine_cache_path": "resources/models/cache",
#                         },
#                     )
#                 ] + providers
#     # Create ONNXRuntime runner
#     runner = LightGlueRunner(
#         extractor_path=extrator_path,
#         lightglue_path=lightglue_path,
#         providers=providers,
#         # TensorrtExecutionProvider, OpenVINOExecutionProvider
#     )
#
#     # Run inference
#     m_kpts0, m_kpts1 = runner.run(image0, image1, scales0, scales1)
#
#     orig_image0, _ = load_image(image0_path)
#     orig_image1, _ = load_image(image1_path)
#     viz2d.plot_images(
#         [orig_image0[0].transpose(1, 2, 0), orig_image1[0].transpose(1, 2, 0)]
#     )
#     viz2d.plot_matches(m_kpts0, m_kpts1, color="lime", lw=0.2)
#     viz2d.plt.show()
#
#
# from onvif import ONVIFCamera
#
#
# def test_onvif_camera_camera():
#     mycam = ONVIFCamera('***************', 80, 'admin', 'Qqwe1234')
#     camera_media = mycam.create_media_service()
#     camera_media_profile = camera_media.GetProfiles()[0]
#     ptz = mycam.create_ptz_service()
#
#     position = ptz.GetStatus({'ProfileToken': camera_media_profile.token}).Position
#     p = position.PanTilt.x
#     t = position.PanTilt.y
#     z = position.Zoom.x
#     print({"p": p, "t": t, "z": z})


from datetime import datetime


def setup_onvif_camera(ip, port, username, password):
    """初始化ONVIF相机连接"""
    try:
        # 创建ONVIF相机对象
        mycam = ONVIFCamera(ip, port, username, password)

        # 创建媒体服务
        media_service = mycam.create_media_service()

        # 创建事件服务
        event_service = mycam.create_events_service()

        # 创建录制服务
        recording_service = mycam.create_recording_service()

        # 创建回放服务
        replay_service = mycam.create_replay_service()

        return mycam, media_service, event_service, recording_service
    except Exception as e:
        print(f"初始化相机失败: {e}")
        return None, None, None, None


def get_recording_summary(recording_service):
    """获取录制摘要"""
    try:
        summary = recording_service.GetRecordingSummary()
        print("录制摘要:")
        print(f"数据来源: {summary.DataFrom}")
        print(f"数据截止: {summary.DataUntil}")
        print(f"录制数量: {summary.NumberRecordings}")
        return summary
    except Exception as e:
        print(f"获取录制摘要失败: {e}")
        return None


def find_recordings(recording_service, start_time, end_time):
    """查找指定时间范围内的录制"""
    try:
        # 设置时间范围
        search = {
            'StartPoint': start_time,
            'EndPoint': end_time,
            'Scope': {
                'IncludedSources': True,
                'IncludedRecordings': True,
                'RecordingInformationFilter': None
            }
        }

        # 执行搜索
        result = recording_service.FindRecordings(search)

        print(f"找到 {len(result.RecordingInformation)} 条录制记录")

        for recording in result.RecordingInformation:
            print("\n录制信息:")
            print(f"录制ID: {recording.RecordingToken}")
            print(f"源ID: {recording.SourceId}")
            print(f"开始时间: {recording.EarliestRecording}")
            print(f"结束时间: {recording.LatestRecording}")
            print(f"内容描述: {recording.ContentDescription}")

        return result
    except Exception as e:
        print(f"查找录制失败: {e}")
        return None


def get_recording_metadata(recording_service, recording_token):
    """获取特定录制的元数据"""
    try:
        metadata = recording_service.GetRecordingSummary(recording_token)
        print("\n录制元数据:")
        print(f"开始时间: {metadata.DataFrom}")
        print(f"结束时间: {metadata.DataUntil}")
        print(f"录制状态: {metadata.RecordingStatus}")
        return metadata
    except Exception as e:
        print(f"获取录制元数据失败: {e}")
        return None



from onvif2 import ONVIFCamera
import datetime

# 摄像头连接信息
IP = '************'  # 替换为你的摄像头IP地址
PORT = 80  # 替换为你的摄像头ONVIF端口
USER = 'admin'  # 替换为你的摄像头用户名
PASS = 'Qqwe1234'  # 替换为你的摄像头密码

def test_onvif_get_recordings():
    try:
        # 初始化ONVIFCamera实例
        # 确保 WSDL_DIR 指向正确的 WSDL 文件目录
        mycam = ONVIFCamera(IP, PORT, USER, PASS)
        print(f"连接到 ONVIF 摄像头: {IP}:{PORT}")

        # 创建 Recording Service
        # ONVIF 协议定义了多种服务，你可以使用 mycam.create_xxx_service() 来创建
        recording_service = mycam.create_recording_service()

        # 获取录像配置 (可选，用于了解录像流信息)
        # 并非所有摄像头都支持所有 Recording Service 操作
        try:
            recording_configurations = recording_service.GetRecordingConfigurations()
            print("\n录像配置:")
            for config in recording_configurations:
                print(f"  Configuration Token: {config.ConfigurationToken}")
                print(f"  Source: {config.Source.SourceId}")
                print(f"  Content: {config.Content}")
                print(f"  Maximum Rate: {config.MaximumRate}")
                print(f"  Maximal Duration: {config.MaximalDuration}")
                # 更多配置信息可以根据 WSDL 定义访问
        except Exception as e:
            print(f"无法获取录像配置 (可能不支持此操作): {e}")

        # 查询录像记录
        # 定义时间范围，ONVIF 查询通常需要 UTC 时间
        # 这里以当前时间为例，查询过去24小时的记录
        end_time = datetime.datetime.utcnow()
        start_time = end_time - datetime.timedelta(days=1)

        # 创建 GetRecordings 请求对象
        # 使用 create_type() 是推荐的方式，因为它会验证参数的有效性 [<sup data-citation='{&quot;id&quot;:2,&quot;url&quot;:&quot;https://pypi.org/project/onvif2-zeep/&quot;,&quot;title&quot;:&quot;onvif2-zeep&quot;,&quot;content&quot;:&quot;‘ ONVIF Client Implementation in Python base wsdl ver1020, support hevch265 Dependencies zeep = 300 Install python-onvif2-zeep python-onvif-zeep ,see From Source You should clone this repository and r&quot;}'>2</sup>](https://pypi.org/project/onvif2-zeep/)
        request = recording_service.create_type('GetRecordings')

        # 尽管 GetRecordings 通常不直接接受时间范围参数，
        # 实际的录像搜索功能通常在 Search Service 中实现。
        # 这里先尝试 GetRecordings，如果需要更复杂的搜索，则需要使用 Search Service。

        print(f"\n尝试获取录像列表...")
        # 实际的 GetRecordings 操作可能不需要额外的参数
        recordings = recording_service.GetRecordings(request)

        if recordings and recordings.RecordingItem:
            print("\n找到以下录像记录:")
            for record in recordings.RecordingItem:
                print(f"  Recording Token: {record.RecordingToken}")
                print(f"  ArchiveDays: {record.ArchiveDays}")
                print(f"  Description: {record.Description}")
                print(f"  Source: {record.Source.SourceId}")
                print(f"  Content: {record.Content}")
                print(f"  Earliest: {record.Earliest}")
                print(f"  Latest: {record.Latest}")
                # 可以进一步获取回放URI，通常在 Search Service 中进行
                # 例如，使用 GetReplayUri 或 FindRecordings
        else:
            print("\n没有找到录像记录。")

        # 更高级的录像搜索通常通过 Search Service 进行
        # 如果摄像头支持 Search Service，你可以这样查询特定时间段的录像：
        print("\n尝试使用 Search Service (如果支持)...")
        try:
            search_service = mycam.create_search_service()

            # 创建 FindRecordings 请求对象
            search_request = search_service.create_type('FindRecordings')
            search_request.Scope = {'IncludedSources': None, 'IncludedContents': ['Video', 'Audio']}  # 搜索视频和音频内容
            search_request.SearchString = ''  # 可以添加搜索字符串
            search_request.MinTime = start_time
            search_request.MaxTime = end_time

            # 启动录像搜索
            search_session = search_service.FindRecordings(search_request)
            print(f"录像搜索会话创建成功，SearchToken: {search_session.SearchToken}")

            # 获取搜索结果
            # 需要一个循环来获取所有结果，因为结果可能分批返回
            pull_request = search_service.create_type('GetRecordingSearchResults')
            pull_request.SearchToken = search_session.SearchToken
            pull_request.MaxResults = 100  # 每次获取的最大结果数
            pull_request.StartPoint = 0  # 起始点

            all_search_results = []
            while True:
                results = search_service.GetRecordingSearchResults(pull_request)
                if results and results.ResultList:
                    all_search_results.extend(results.ResultList)
                    pull_request.StartPoint += len(results.ResultList)
                    # 检查是否还有更多结果
                    if not results.ResultList or len(results.ResultList) < pull_request.MaxResults:
                        break
                else:
                    break

            if all_search_results:
                print(f"\n通过 Search Service 找到 {len(all_search_results)} 条录像搜索结果:")
                for result in all_search_results:
                    print(f"  Recording Token: {result.RecordingToken}")
                    print(f"  Time: {result.Time}")
                    print(f"  Content: {result.Content}")
                    # 获取回放URI
                    # replay_uri_request = search_service.create_type('GetReplayUri')
                    # replay_uri_request.RecordingToken = result.RecordingToken
                    # replay_uri_request.StreamSetup = {'Stream': 'RTP_RTSP_TCP', 'Transport': {'Protocol': 'RTSP'}} # 示例流设置
                    # replay_uri = search_service.GetReplayUri(replay_uri_request)
                    # print(f"  Replay URI: {replay_uri.Uri}")
            else:
                print("通过 Search Service 没有找到录像记录。")

            # 停止搜索会话
            search_service.EndSearch({'SearchToken': search_session.SearchToken})
            print("搜索会话已结束。")

        except Exception as e:
            print(f"Search Service 操作失败 (可能不支持此服务或操作): {e}")

    except Exception as e:
        print(f"连接或操作 ONVIF 摄像头时发生错误: {e}")
