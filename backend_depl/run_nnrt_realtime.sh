#!/bin/bash

export ASCEND_GLOBAL_EVENT_ENABLE=0  #禁用EVENT日志
source /usr/local/Ascend/nnrt/set_env.sh
export FFMPEG_LIB_PATH=/home/<USER>/ascend/lib
export LD_LIBRARY_PATH=${FFMPEG_LIB_PATH}:$LD_LIBRARY_PATH
# 添▒~J|  FFmpeg ▒~H▒▒~N▒▒~C▒~O~X▒~G~O
export PATH=/home/<USER>

nohup /var/dmp_daemon -I -M -U 8087 >&/dev/null &
python3 application_depl.py 2>&1 |     grep -v "svm_alloc\|FreeDevMem\|dvpp free\|drvMmapHeadErase\|alloc information from base\|KERNEL\|DVPP"