FROM ***************:5000/inspection-backend-tensorrt:1.0.0

#代码添加到home文件夹
ADD config /home/<USER>
ADD constants /home/<USER>
ADD exceptions /home/<USER>
ADD listener /home/<USER>
ADD maths /home/<USER>
ADD models /home/<USER>
ADD service /home/<USER>
ADD deplutils /home/<USER>
ADD requirements.txt /home/<USER>
ADD application_depl.py /home/<USER>

# 设置home文件夹是工作目录
WORKDIR /home/

EXPOSE 6300

#RUN pip3 install --upgrade pip -i https://pypi.doubanio.com/simple
# 安装支持
#RUN pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple
#RUN pip install opencv-python install "opencv-python-headless<4.3" -i https://pypi.tuna.tsinghua.edu.cn/simple
CMD ["uvicorn", "application_depl:app", "--host", "0.0.0.0", "--port", "6300"]

# docker build -t  ***************:5000/inspection-backend-depl:3090 .
# docker build -t  ***************:5000/inspection-backend-depl:T4 .
#docker run --name inspection-backend-depl --restart=always \
#-v /home/<USER>/intelligent_inspection/file-server/images:/project/file-server/images/ \
#-v /home/<USER>/intelligent_inspection/file-server/voice/:/voice/ \
#--gpus all --shm-size=8g --ulimit memlock=-1 -p 6300:6300 -d  inspection-backend-depl:1.0.0

#docker run --name inspection-backend-depl --restart=always -v E:/project/file-server/images:/project/file-server/images/ --gpus all --shm-size=8g --ulimit memlock=-1 -p 6300:6300 -d   ***************:5000/inspection-backend-depl:1.0.0