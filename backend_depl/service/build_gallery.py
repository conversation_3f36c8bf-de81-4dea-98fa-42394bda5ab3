import copy
import os
import pickle

import cv2
import faiss
import numpy as np
import yaml
from loguru import logger

try:
    from depl_maths.algorithm.system.paomaodilou.paomaodilou_det import RecPredictor
except:
    logger.error("load RecPredictor failed, please check your paomaodilou block installation")
    from paddleclas.deploy.python.predict_rec import RecPredictor


class AttrDict(dict):
    def __getattr__(self, key):
        return self[key]

    def __setattr__(self, key, value):
        if key in self.__dict__:
            self.__dict__[key] = value
        else:
            self[key] = value

    def __deepcopy__(self, content):
        return copy.deepcopy(dict(self))


def parse_config(cfg_file):
    def create_attr_dict(yaml_config):
        from ast import literal_eval
        for key, value in yaml_config.items():
            if type(value) is dict:
                yaml_config[key] = value = AttrDict(value)
            if isinstance(value, str):
                try:
                    value = literal_eval(value)
                except BaseException:
                    pass
            if isinstance(value, AttrDict):
                create_attr_dict(yaml_config[key])
            else:
                yaml_config[key] = value

    """Load a config file into AttrDict"""
    with open(cfg_file, 'r') as fopen:
        yaml_config = AttrDict(yaml.load(fopen, Loader=yaml.SafeLoader))
    create_attr_dict(yaml_config)
    return yaml_config


class GalleryBuilder(object):
    def __init__(self, config_file, rec_predictor: RecPredictor = None):
        config = parse_config(config_file)
        self.config = config
        if rec_predictor is None:
            self.rec_predictor = RecPredictor(config["IndexProcess"]["rec_inference_model_dir"])
        else:
            self.rec_predictor = rec_predictor
        assert 'IndexProcess' in config.keys(), "Index config not found ... "
        # self.android_demo = config["Global"].get("android_demo", False)
        self.build(config['IndexProcess'])
        print('gallery build success!!!')

    def build(self, config):
        """
            build index from scratch
        """
        operation_method = config.get("index_operation", "new").lower()
        gallery_images, gallery_docs = self.get_img_lable_info(config['image_root'])
        print('start update {} images to gallery ...'.format(len(gallery_images)))
        if (len(gallery_images) <= 0 or len(gallery_docs) <= 0) and operation_method == "new":
            print(f'[WARN] no images under {config["image_root"]}, gallery build skipped, delete all vector !!!')
            vector_path = os.path.join(config["index_dir"], "vector.index")
            if os.path.isfile(vector_path):
                os.remove(vector_path)
                os.remove(os.path.join(config["index_dir"], "id_map.pkl"))
            return

        # when remove data in index, do not need extract fatures
        if operation_method != "remove":
            gallery_features = self._extract_features(gallery_images, config)
        assert operation_method in [
            "new", "remove", "append"
        ], "Only append, remove and new operation are supported"

        # if self.android_demo:
        #     self._create_index_for_android_demo(config, gallery_features, gallery_docs)
        #     return

        # vector.index: faiss index file
        # id_map.pkl: use this file to map id to image_doc
        index, ids = None, None
        if operation_method in ["remove", "append"]:
            # if remove or append, load vector.index and id_map.pkl
            index, ids = self._load_index(config)
            index_method = config.get("index_method", "HNSW32")
        else:
            index_method, index, ids = self._create_index(config, gallery_images)
        if index_method == "HNSW32":
            # logger.warning(
            print("The HNSW32 method dose not support 'remove' operation")

        if operation_method != "remove":
            # calculate id for new data
            index, ids = self._add_gallery(index, ids, gallery_features, gallery_docs, config, operation_method)
        else:
            if index_method == "HNSW32":
                raise RuntimeError(
                    "The index_method: HNSW32 dose not support 'remove' operation"
                )
            # remove ids in id_map, remove index data in faiss index
            index, ids = self._rm_id_in_galllery(index, ids, gallery_docs)

        # store faiss index file and id_map file
        self._save_gallery(config, index, ids)

    def get_img_lable_info(self, image_root):
        gallery_images = []
        gallery_docs = []
        for cls in os.listdir(image_root):
            cls_path = os.path.join(image_root, cls)
            for img in os.listdir(cls_path):
                img_path = os.path.join(cls_path, img)
                gallery_images.append(img_path)
                label_info = cls + '/' + img + ' ' + cls
                gallery_docs.append(label_info)
        return gallery_images, gallery_docs

    def _create_index_for_android_demo(self, config, gallery_features, gallery_docs):
        if not os.path.exists(config["index_dir"]):
            os.makedirs(config["index_dir"], exist_ok=True)
        # build index
        index = faiss.IndexFlatIP(config["embedding_size"])
        index.add(gallery_features)

        # calculate id for data
        ids_now = (np.arange(0, len(gallery_docs))).astype(np.int64)
        ids = {}
        for i, d in zip(list(ids_now), gallery_docs):
            ids[i] = d
        self._save_gallery(config, index, ids)

    def _extract_features(self, gallery_images, config):
        # extract gallery features
        if config["dist_type"] == "hamming":
            gallery_features = np.zeros(
                [len(gallery_images), config['embedding_size'] // 8],
                dtype=np.uint8)
        else:
            gallery_features = np.zeros(
                [len(gallery_images), config['embedding_size']],
                dtype=np.float32)

        # construct batch imgs and do inference
        batch_size = config.get("batch_size", 32)
        batch_img = []
        for i, image_file in enumerate(gallery_images):
            img = cv2.imread(image_file)
            if img is None:
                # logger.error
                print("img empty, please check {}".format(image_file))
                exit()
            img = img[:, :, ::-1]
            batch_img.append(img)

            if (i + 1) % batch_size == 0:
                rec_feat = self.rec_predictor.predict(batch_img)
                gallery_features[i - batch_size + 1:i + 1, :] = rec_feat
                batch_img = []

        if len(batch_img) > 0:
            rec_feat = self.rec_predictor.predict(batch_img)
            gallery_features[-len(batch_img):, :] = rec_feat
            batch_img = []

        return gallery_features

    def _load_index(self, config):
        assert os.path.join(
            config["index_dir"], "vector.index"
        ), "The vector.index dose not exist in {} when 'index_operation' is not None".format(
            config["index_dir"])
        assert os.path.join(
            config["index_dir"], "id_map.pkl"
        ), "The id_map.pkl dose not exist in {} when 'index_operation' is not None".format(
            config["index_dir"])
        index = faiss.read_index(
            os.path.join(config["index_dir"], "vector.index"))
        with open(os.path.join(config["index_dir"], "id_map.pkl"),
                  'rb') as fd:
            ids = pickle.load(fd)
        assert index.ntotal == len(ids.keys(
        )), "data number in index is not equal in in id_map"
        return index, ids

    def _create_index(self, config, gallery_images):
        if not os.path.exists(config["index_dir"]):
            os.makedirs(config["index_dir"], exist_ok=True)
        index_method = config.get("index_method", "HNSW32")

        # if IVF method, cal ivf number automaticlly
        if index_method == "IVF":
            index_method = index_method + str(
                min(int(len(gallery_images) // 8), 65536)) + ",Flat"

        # for binary index, add B at head of index_method
        if config["dist_type"] == "hamming":
            index_method = "B" + index_method

        # dist_type
        dist_type = faiss.METRIC_INNER_PRODUCT if config[
                                                      "dist_type"] == "IP" else faiss.METRIC_L2

        # build index
        if config["dist_type"] == "hamming":
            index = faiss.index_binary_factory(config["embedding_size"],
                                               index_method)
        else:
            index = faiss.index_factory(config["embedding_size"],
                                        index_method, dist_type)
            index = faiss.IndexIDMap2(index)
        ids = {}
        return index_method, index, ids

    def _add_gallery(self, index, ids, gallery_features, gallery_docs, config, operation_method):
        start_id = max(ids.keys()) + 1 if ids else 0
        ids_now = (
                np.arange(0, len(gallery_docs)) + start_id).astype(np.int64)

        # only train when new index file
        if operation_method == "new":
            if config["dist_type"] == "hamming":
                index.add(gallery_features)
            else:
                index.train(gallery_features)

        if not config["dist_type"] == "hamming":
            index.add_with_ids(gallery_features, ids_now)

        for i, d in zip(list(ids_now), gallery_docs):
            ids[i] = d
        return index, ids

    def _rm_id_in_galllery(self, index, ids, gallery_docs):
        remove_ids = list(
            filter(lambda k: ids.get(k) in gallery_docs, ids.keys()))
        remove_ids = np.asarray(remove_ids)
        index.remove_ids(remove_ids)
        for k in remove_ids:
            del ids[k]

        return index, ids

    def _save_gallery(self, config, index, ids):
        if config["dist_type"] == "hamming":
            faiss.write_index_binary(
                index, os.path.join(config["index_dir"], "vector.index"))
        else:
            faiss.write_index(
                index, os.path.join(config["index_dir"], "vector.index"))

        with open(os.path.join(config["index_dir"], "id_map.pkl"), 'wb') as fd:
            pickle.dump(ids, fd)


if __name__ == "__main__":
    # args = config.parse_args()
    # args.config = '/opt/tjh/paddleClas-release-2.5/paomaodilou/inference_paomaodilou.yaml'
    # config = config.get_config(args.config, overrides=args.override, show=True)
    config_file = '../depl_maths/algorithm/system/paomaodilou/paomaodilou_gallary.yaml'
    GalleryBuilder(config_file)
