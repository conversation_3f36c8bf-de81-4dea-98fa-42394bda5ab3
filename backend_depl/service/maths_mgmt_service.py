import importlib
import os
import tempfile

from loguru import logger

from backend_common.constants.math_constants import ALGORITHM_DEPL_USER_DEFINE_PATH, ALGORITHM_DEPL_SYSTEM_INNER_PATH, ALGORITHM_DEPL_USER_DEFINE_BASE_OCR
from backend_common.exceptions.inspection_exception import InspectionException, AlgorithmCheckException, AlgorithmProcessException
from backend_common.maths.algorithm.base.algorithm_base import AlgorithmInput, AlgorithmBase
from backend_common.utils.util import remove_quietly
from backend_common.maths.algorithm.base.parameter_check import ParameterCheck

from backend_common.utils import MinioUtil
import shutil
import sys

# 深度学习算法块 MAP
DEEPLEARN_BLOCK_MAP = dict()


def to_camel_case(string, spliter):
    components = string.split(spliter)
    # Capitalize the first letter of each word except the first one if index > 0 else word
    components = [word.capitalize() for index, word in enumerate(components)]
    # Join the components together
    camel_case_string = ''.join(components)
    return camel_case_string


def load_algorithm_super(algorithm_name, a_type, inputs: AlgorithmInput, reload_model=None):
    # 算法块唯一标识名,通常需与算法块文件夹名称一致，比如 liquid_level
    module_name = f".{algorithm_name}.ab_{algorithm_name}"
    target_clazz_name = f"{to_camel_case(algorithm_name, '_')}Reader"

    base_path = ALGORITHM_DEPL_SYSTEM_INNER_PATH
    if a_type == "user_define":
        base_path = ALGORITHM_DEPL_USER_DEFINE_PATH

    try:
        logger.info(f"algorithm_depl_base_path : {base_path}")
        logger.info(f"module_name : {module_name}")
        logger.info(f"target_clazz_name : {target_clazz_name}")

        M = importlib.import_module(module_name, base_path)
        target_clazz_ = getattr(M, target_clazz_name)
    except ModuleNotFoundError as e:
        logger.exception(e)
        raise InspectionException(
            f"load_algorithm_super module '{module_name}' from '{base_path}' failure, please check it manually")
    if reload_model is not None:
        algorithm_instance: AlgorithmBase = target_clazz_(inputs, reload_model)
    else:
        algorithm_instance: AlgorithmBase = target_clazz_(inputs)
    return algorithm_instance


def preload_all_deeplearning_block():
    """预加载所有深度学习模块"""
    import glob
    import os

    # 用于加载指定路径下的所有Python包
    def load_packages(path):
        # 获取指定路径下的所有文件夹
        folders = glob.glob(os.path.join(path, "*"))

        code_name_list = []
        for folder in folders:
            if os.path.isdir(folder) and os.path.exists(os.path.join(folder, "__init__.py")) \
                    and not folder.endswith("base"):
                code_name = folder.split('/')[-1]
                code_name_list.append(code_name)

        return code_name_list

    def move_to_front(lst, target):
        if target in lst:
            lst.remove(target)
            lst.insert(0, target)
        return lst

    # 加载指定路径下的所有Python包 -- 系统内置 system_inner
    packages_system = load_packages(ALGORITHM_DEPL_SYSTEM_INNER_PATH.replace('.', '/'))
    # ppocr算法首先进行初始化
    packages_system = move_to_front(packages_system, 'ppocr')
    global DEEPLEARN_BLOCK_MAP
    for idx, code in enumerate(packages_system):
        sup: AlgorithmBase
        sup = load_algorithm_super(code, "system_inner", AlgorithmInput())
        DEEPLEARN_BLOCK_MAP[f"{code}"] = sup
        logger.info(f"pre load system code: {code} model")
        assert sup is not None, f"无法预加载{code}算法实例"

    # 加载指定路径下的所有Python包 -- 用户自定 user_define
    packages_user = load_packages(ALGORITHM_DEPL_USER_DEFINE_PATH.replace('.', '/'))
    # yolo_model_paomaodilou = DEEPLEARN_BLOCK_MAP.get("paomaodilou").model
    ocr = DEEPLEARN_BLOCK_MAP.get("ppocr").text_sys
    for idx, code in enumerate(packages_user):
        sup: AlgorithmBase
        if code in ALGORITHM_DEPL_USER_DEFINE_BASE_OCR.keys():
            sup = load_algorithm_super(code, "user_define", AlgorithmInput(), reload_model=ocr)
        else:
            sup = load_algorithm_super(code, "user_define", AlgorithmInput())
        # sup = load_algorithm_super(code, "user_define", AlgorithmInput())
        DEEPLEARN_BLOCK_MAP[f"{code}"] = sup
        logger.info(f"pre load user code: {code} model")
        assert sup is not None, f"无法预加载{code}算法实例"


class MathABMgmt:
    def __init__(self, request_param: dict):
        self.request_param = request_param
        self.param_check = ParameterCheck()

    def execute(self):
        """ 算法执行 """
        try:
            inputs = AlgorithmInput()

            images = self.request_param['images']

            inputs['images'] = images

            roi = self.request_param['roi']
            inputs['roi'] = roi

            inputParam = self.request_param['inputParam']
            inputs['inputParam'] = inputParam

            method_code = self.request_param['code']
            user_or_system = self.request_param['userOrSystem']

            # 输入参数校验
            self.param_check.do_check_param(inputs)

            algorithm_instance = DEEPLEARN_BLOCK_MAP.get(f"{method_code}")
            ocr = DEEPLEARN_BLOCK_MAP.get("ppocr").text_sys
            if algorithm_instance is None:
                if method_code in ALGORITHM_DEPL_USER_DEFINE_BASE_OCR.keys():
                    algorithm_instance = load_algorithm_super(method_code, user_or_system, inputs, reload_model=ocr)
                else:
                    algorithm_instance = load_algorithm_super(method_code, user_or_system, inputs)
                DEEPLEARN_BLOCK_MAP[f"{method_code}"] = algorithm_instance
            else:
                algorithm_instance.init(inputs)

            assert algorithm_instance is not None, "无法加载算法实例!!!"
            # 算法执行
            # ret, val, points = algorithm_instance.perform()
            result = algorithm_instance.perform()
            if len(result) == 3:
                return {
                    'isSuccess': result[0],
                    'output': result[1],
                    'osdInfo': result[2],
                }
            else:
                return {
                    'isSuccess': result[0],
                    'output': result[1],
                    'osdInfo': result[2],
                    'resultImgUrl': result[3],
                }

        except ModuleNotFoundError as e:
            raise InspectionException(message=e.message)
        except InspectionException as e:
            raise InspectionException(message=e.message)
        except AlgorithmCheckException as e:
            raise AlgorithmCheckException(message=e.message)
        except AlgorithmProcessException as e:
            raise AlgorithmProcessException(message=e.message)
        except Exception as e:
            raise Exception(e.__str__())

    def delete_code_and_resource(self, block_name):
        # 构建算法包路径
        user_path = ALGORITHM_DEPL_USER_DEFINE_PATH.replace('.', '/')
        block_name_list = []
        for black in os.listdir(user_path):
            if block_name in black:
                block_name_list.append(black)

        if block_name_list == []:
            logger.error(f"算法文件 {block_name} 不存在")
        else:
            # acl模型内存释放
            algorithm_instance = DEEPLEARN_BLOCK_MAP.get(block_name)
            if algorithm_instance is not None:
                if hasattr(algorithm_instance, 'release_resource'):
                    logger.info(f"算法 {block_name} acl资源释放开始执行")
                    algorithm_instance.release_resource()
                    logger.info(f"算法 {block_name} acl资源释放完成")
            # 删除模块目录
            for block_dir in block_name_list:
                package_path = os.path.join(user_path, block_dir)
                shutil.rmtree(package_path)
            logger.info(f"算法文件 {block_name} 删除成功")

        try:
            DEEPLEARN_BLOCK_MAP.pop(block_name, None)
            logger.info(f"从DEEPLEARN_BLOCK_MAP中删除算法块 {block_name}")
            # 1. 查找所有缓存中与该模块相关的子模块
            sub_modules = [k for k in sys.modules.keys() if block_name in str(k)]
            # logger.info(f"找到与模块 {block_name} 相关的子模块: {sub_modules}")
            # 2. 删除缓存中的子模块
            for sub_module in sub_modules:
                if sub_module in sys.modules:
                    logger.info(f"Removing module: {sub_module}")
                    del sys.modules[sub_module]
            # 删除模块目录
            logger.info(f"模块 {block_name} 删除成功")
            return (True, f"模块 {block_name} 删除成功")
        except Exception as e:
            return (False, f"删除失败: {str(e)}")

    def block_import(self) -> (bool, str):
        """

        file: the uploaded wheel file
        :return:
            是否导入成功
        """
        # 先判断该算法是否已经存在，如果存在则先删除已有算法并释放资源，再重新上传
        code_name = self.request_param['name']

        user_path = ALGORITHM_DEPL_USER_DEFINE_PATH.replace('.', '/')
        code_in_path = False
        for name in os.listdir(user_path):
            if code_name == name:
                code_in_path = True

        if code_in_path:
            logger.info(f"模块重新上传{code_name} 先开始执行删除操作")
            result = self.delete_code_and_resource(code_name)
            if result[0] is False:
                return (False, "模块删除失败")

        # 算法code上传模块
        logger.info(f"开始执行模块 {code_name} 安装")
        file = self.request_param['file']
        tmp_file = f"{tempfile.gettempdir()}/{file.filename}"

        writer = open(tmp_file, 'wb')
        writer.write(file.file.read())
        # Install the package using pip
        cmd = f"pip install --no-cache --upgrade {tmp_file} -t {ALGORITHM_DEPL_USER_DEFINE_PATH.replace('.', '/')}"
        # exit_code = subprocess.call(cmd)
        exit_code = os.system(cmd)
        remove_quietly(tmp_file)
        logger.info(f"模块 {code_name} 安装步骤执行结束")

        return (True, "import success") if exit_code == 0 else (False, "import failure")

    def block_remove(self) -> (bool, str):
        """
        删除指定的用户自定义算法包
        :return:
            (bool, str): 是否删除成功 和 提示信息
        """
        # 获取算法包名称
        block_name = self.request_param.get('code')

        result = self.delete_code_and_resource(block_name)
        return result
