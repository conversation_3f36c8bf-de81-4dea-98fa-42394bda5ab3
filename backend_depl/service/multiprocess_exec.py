import importlib
import os
import sys
from multiprocessing import Pool

# add deploy path of backend_depl to sys.path
parent_path = os.path.abspath(os.path.join(__file__, *(['../..'])))
sys.path.insert(0, parent_path)

from loguru import logger

from backend_common.constants.math_constants import ALGORITHM_DEPL_USER_DEFINE_PATH
from backend_common.exceptions.inspection_exception import InspectionException
from backend_common.maths.algorithm.base.algorithm_base import AlgorithmSettings, AlgorithmBase, AlgorithmInput

instance_of_subprocess = None


def to_camel_case(string, spliter, cap_first=True):
    """
    转换为驼峰形式(默认首字大写)
    """
    components = string.split(spliter)
    # Capitalize the first letter of each word except the first one
    components = [word.lower() if index == 0 and not cap_first else word.capitalize() for index, word in
                  enumerate(components)]
    # Join the components together
    camel_case_string = ''.join(components)
    return camel_case_string


def load_algorithm_super(algorithm_name, inputs: AlgorithmInput):
    # self._name  算法块唯一标识名,通常需与算法块文件夹名称一致，比如 liquid_level
    module_name = f".{algorithm_name}.ab_{algorithm_name}"
    target_clazz_name = f"{to_camel_case(algorithm_name, '_')}Reader"

    try:
        # print("ALGORITHM_DEPL_USER_DEFINE_PATH", ALGORITHM_DEPL_USER_DEFINE_PATH)
        print("module_name", module_name)
        print("target_clazz_name", target_clazz_name)
        M = importlib.import_module(module_name, ALGORITHM_DEPL_USER_DEFINE_PATH)
        target_clazz_ = getattr(M, target_clazz_name)
    except Exception as e:
        logger.exception(str(e))
        raise InspectionException(
            f"Auto-load module '{module_name}' from '{ALGORITHM_DEPL_USER_DEFINE_PATH}' failure, please check it manually")

    algorithm_instance: AlgorithmBase = target_clazz_(inputs, -1)
    return algorithm_instance


def pool_block_init(code, i):
    """
    初始化子进程中算法块实例
        multiprocessing.set_start_method('spawn')
    """
    # add deploy path of backend_depl to sys.path
    parent_path = os.path.abspath(os.path.join(__file__, *(['../..'])))
    sys.path.insert(0, parent_path)

    # multiprocessing.set_start_method('spawn')
    global instance_of_subprocess
    settings: AlgorithmSettings
    sup: AlgorithmBase
    sup = load_algorithm_super(code, AlgorithmInput())
    instance_of_subprocess = sup.determine_algorithm_instance(index=i)
    logger.debug(f"init :: pool_block_init {instance_of_subprocess} success ...")


def init_and_predict(inputs: AlgorithmInput, settings: AlgorithmSettings):
    global instance_of_subprocess
    assert instance_of_subprocess is not None, "sub进程持有的算法实例为None"

    logger.debug(f"init :: instance_of_subprocess {instance_of_subprocess} and predict start ...")
    instance_of_subprocess.init(inputs, settings)
    return instance_of_subprocess.perform(settings)


# def callback(result):
#     print(f"callback ret == {result}")
#
#
# def errorback(result):
#     print(f"error_back ret == {result}")


def do_predictor(target_instance: Pool, inputs: AlgorithmInput, settings: AlgorithmSettings):
    #  callback=callback, error_callback=errorback
    return target_instance.apply(init_and_predict, args=(inputs, settings,),)
