import importlib
import json
import os
import tempfile

from loguru import logger

from backend_common.constants.alarm import ExceedLimitAlarmRule
from backend_common.constants.constant import AlgorithmClassifyEnum, AlgorithmStrategyEnum
from backend_common.constants.math_constants import ALGORITHM_DEPL_USER_DEFINE_PATH, ALG<PERSON>ITHM_DEPL_SYSTEM_INNER_PATH
from backend_common.exceptions.inspection_exception import InspectionException, AlgorithmCheckException, AlgorithmProcessException
from backend_common.maths.algorithm.base.algorithm_base import AlgorithmInput, AlgorithmBase, AlgorithmSettings
from backend_common.utils.util import remove_quietly
# from service.build_gallery import GalleryBuilder

# 每个进程池可用进程数
PROCESS_PER_INSTANCE = 2
# 分类库位于哪个目录，默认 位于 跑冒滴漏算法 路径下, 后续可配
GALLERY_LOCATE_IN = os.getenv("GALLERY_LOCATE_IN",
                              os.path.join(ALGORITHM_DEPL_SYSTEM_INNER_PATH.replace('.', '/') + "/",
                                           "paomaodilou/paomaodilou_data"))
# 分类库位于哪个算法下，默认 位于 跑冒滴漏算法 路径下, 后续可配
GALLERY_LOCATE_IN_BLOCK = "paomaodilou"


def to_camel_case(string, spliter):
    components = string.split(spliter)
    # Capitalize the first letter of each word except the first one if index > 0 else word
    components = [word.capitalize() for index, word in enumerate(components)]
    # Join the components together
    camel_case_string = ''.join(components)
    return camel_case_string


def load_algorithm_super(algorithm_name, a_type, inputs: AlgorithmInput):
    # self._name  算法块唯一标识名,通常需与算法块文件夹名称一致，比如 liquid_level
    module_name = f".{algorithm_name}.ab_{algorithm_name}"
    target_clazz_name = f"{to_camel_case(algorithm_name, '_')}Reader"

    base_path = ALGORITHM_DEPL_SYSTEM_INNER_PATH
    if a_type == "user_define":
        base_path = ALGORITHM_DEPL_USER_DEFINE_PATH

    try:
        print("ALGORITHM_DEPL_USER_DEFINE_PATH", base_path)
        print("module_name", module_name)
        print("target_clazz_name", target_clazz_name)
        M = importlib.import_module(module_name, base_path)
        target_clazz_ = getattr(M, target_clazz_name)
    except ModuleNotFoundError as e:
        logger.exception(e)
        raise InspectionException(
            f"load_algorithm_super module '{module_name}' from '{base_path}' failure, please check it manually")

    algorithm_instance: AlgorithmBase = target_clazz_(inputs, -1)
    return algorithm_instance


def preload_all_deeplearning_block():
    """预加载所有深度学习模块"""
    import glob
    import os

    # 定义一个函数，用于加载指定路径下的所有Python包
    def load_packages(path):
        # 获取指定路径下的所有文件夹
        folders = glob.glob(os.path.join(path, "*"))

        # 遍历所有文件夹，将其中的Python包加入到sys.path中
        target_folders = []
        for folder in folders:
            if os.path.isdir(folder) and os.path.exists(os.path.join(folder, "__init__.py")) \
                    and not folder.endswith("base"):
                schema_json = os.path.join(folder, "schema.json")
                with open(schema_json, encoding='utf-8', errors='ignore') as f:
                    schema = json.load(f)
                a_type = schema['algorithm']['type']
                _code = schema['algorithm']['code']
                if a_type == 'DeepLearning':
                    target_folders.append((_code, folder))
        return target_folders

    # 加载指定路径下的所有Python包 -- 系统内置 system_inner
    packages_system = load_packages(ALGORITHM_DEPL_SYSTEM_INNER_PATH.replace('.', '/'))
    # TODO 此处加载的算法实例其 inputs和 settings 都是空的，需要在实例调用前 self.init() 一下 然后再 predictor.run
    for idx, (code, basedir) in enumerate(packages_system):

        settings: AlgorithmSettings
        sup: AlgorithmBase
        sup = load_algorithm_super(code, "system_inner", AlgorithmInput())
        for i, sub in enumerate(sup.load_algorithm_sublist(code, "system_inner", 'depl')):
            target_instance = sup.determine_algorithm_instance(None, index=i, a_type="system_inner")

            DEEPLEARN_BLOCK_MAP[f"{code}_{i}"] = target_instance

    # 加载指定路径下的所有Python包 -- 用户自定 user_define
    packages_user = load_packages(ALGORITHM_DEPL_USER_DEFINE_PATH.replace('.', '/'))
    # TODO 此处加载的算法实例其 inputs和 settings 都是空的，需要在实例调用前 self.init() 一下 然后再 predictor.run
    for idx, (code, basedir) in enumerate(packages_user):

        settings: AlgorithmSettings
        sup: AlgorithmBase
        sup = load_algorithm_super(code, "user_define", AlgorithmInput())
        for i, sub in enumerate(sup.load_algorithm_sublist(code, "user_define", 'depl')):
            target_instance = sup.determine_algorithm_instance(None, index=i, a_type="user_define")

            DEEPLEARN_BLOCK_MAP[f"{code}_{i}"] = target_instance


DEEPLEARN_BLOCK_MAP = dict()  # 深度学习算法块 MAP


class MathABMgmt:
    def __init__(self, request_param: dict):
        self.request_param = request_param

    def execute(self):
        """ 算法执行 """
        try:
            input_param = self.request_param['inputParam']
            inputs = AlgorithmInput(input_param)

            roi = self.request_param['roi']
            inputs['roi'] = roi

            images = self.request_param['images']
            inputs['images'] = images

            scene_image = self.request_param['sceneImage']
            inputs['targetSceneImage'] = {'uri': scene_image}

            classify = AlgorithmClassifyEnum.from_str(self.request_param['classify'])
            strategy = AlgorithmStrategyEnum.from_str(self.request_param['strategy'])

            alarm_rules = []
            alarm_rules_info = self.request_param['alarmRulesInfo']
            if alarm_rules_info is not None:
                # alarm_rules_info = eval(alarm_rules_info)
                for alarm_rule_info in alarm_rules_info:
                    hh_info = alarm_rule_info["HH"]
                    h_info = alarm_rule_info["H"]
                    l_info = alarm_rule_info["L"]
                    ll_info = alarm_rule_info["LL"]
                    alarm_rule = ExceedLimitAlarmRule(
                        hh_info["val"], h_info["val"], l_info["val"], ll_info["val"],
                        hh_info["desc"], h_info["desc"], l_info["desc"], ll_info["desc"],
                        hh_info["isBind"], h_info["isBind"], l_info["isBind"], ll_info["isBind"]
                    )
                    alarm_rules.append(alarm_rule)

            settings = AlgorithmSettings(classify, strategy, alarm_rules)
            sup: AlgorithmBase
            method_code = self.request_param['code']
            a_type = self.request_param['userOrSystem']
            sup = load_algorithm_super(method_code, a_type, inputs)
            algorithm_index = self.request_param['algorithmIndex']

            target_instance = DEEPLEARN_BLOCK_MAP.get(F"{method_code}_{algorithm_index}")
            if target_instance is not None:
                target_instance.init(inputs, settings)
            else:
                target_instance = sup.determine_algorithm_instance(settings, algorithm_index, a_type)
                # reload 后回填, 也即算法完成了更新
                DEEPLEARN_BLOCK_MAP[F"{method_code}_{algorithm_index}"] = target_instance

                target_instance.init(inputs, settings)

            assert target_instance is not None, "无法加载算法实例!!!"
            # 算法执行
            ret, val, points, alarms = target_instance.perform(settings)
            # outputs = AlgorithmOutput(ret, val, points, alarms)
            # print(f"outputs {str(outputs)}")

            return {
                'isSuccess': ret,
                'outputValue': val,
                'outputValuePosition': points,
                'alarms': alarms
            }
        # except ModuleNotFoundError as e:
        #     raise InspectionException(message=e.msg)
        # except Exception as e:
        #     raise InspectionException(e.__str__())
        
        except ModuleNotFoundError as e:
            raise InspectionException(message=e.msg)
        except InspectionException as e:
            raise InspectionException(message=e.msg)
        except AlgorithmCheckException as e:
            raise AlgorithmCheckException(message=e.message)
        except AlgorithmProcessException as e:
            raise AlgorithmProcessException(message=e.message)
        except Exception as e:
            raise Exception(e.__str__())

    def get_detail(self):
        """ """
        code = self.request_param['code']
        a_type = self.request_param['a_type']
        sup: AlgorithmBase
        sup = load_algorithm_super(code, a_type, AlgorithmInput())
        algorithm_detail = sup.get_algorithm_detail(a_type)

        ret = json.dumps(algorithm_detail, default=lambda o: o.__dict__, sort_keys=True, ensure_ascii=False)
        # logger.debug(f"algorithm block detail of {code} :: \r\n {ret}")

        return json.loads(ret)

    def block_import(self) -> (bool, str):
        """

        file: the uploaded wheel file
        :return:
            是否导入成功
        """
        file = self.request_param['file']
        # block_name = self.request_param['name']
        tmp_file = f"{tempfile.gettempdir()}/{file.filename}"

        # async with aiofiles.open(tmp_file, "wb") as buffer:
        #     while content := await file.read(1024):
        #         await buffer.write(content)
        writer = open(tmp_file, 'wb')
        writer.write(file.file.read())
        # Install the package using pip
        cmd = f"pip install --no-cache --upgrade {tmp_file} -t {ALGORITHM_DEPL_USER_DEFINE_PATH.replace('.', '/')}"
        # exit_code = subprocess.call(cmd)
        exit_code = os.system(cmd)
        remove_quietly(tmp_file)

        return (True, "import success") if exit_code == 0 else (False, "import failure")

    def update_cls_gallery(self):
        """
            {{root}}/depl_maths/algorithm/system/paomaodilou/paomaodilou_data
               |--paomaodilou_gallary.yaml
               |--gallery
                   |--xxx
                       |--*.png
                       |--*.jpg
               |--index
                   |-- id_map.pkl
                   |-- vector.index
               |--*
        """
        logger.info(f"GALLERY_LOCATE_IN: {GALLERY_LOCATE_IN}")
        config_file = os.path.join(GALLERY_LOCATE_IN, '../paomaodilou_gallary.yaml')

        builder, rec_predictor = None, None
        sup: AlgorithmBase
        sup = load_algorithm_super(GALLERY_LOCATE_IN_BLOCK, "system_inner", AlgorithmInput())
        for i, sub in enumerate(sup.load_algorithm_sublist(GALLERY_LOCATE_IN_BLOCK, "system_inner", 'depl')):
            # 从缓存中加载后赋值
            if DEEPLEARN_BLOCK_MAP.get(f"{GALLERY_LOCATE_IN_BLOCK}_{i}", None) is not None:
                rec_predictor = DEEPLEARN_BLOCK_MAP[f"{GALLERY_LOCATE_IN_BLOCK}_{i}"].paomaodilou_det.rec_predictor

            builder = GalleryBuilder(config_file, rec_predictor)
            # 从缓存中移除，促使下次执行时 reload 该算法
            #DEEPLEARN_BLOCK_MAP.pop(f"{GALLERY_LOCATE_IN_BLOCK}_{i}", None)

        return (True, "build success") if builder is not None else (False, "build failure")
