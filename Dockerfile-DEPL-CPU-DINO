FROM ***************:5000/inspection-backend-base:ground-cpu

#代码添加到home文件夹
ADD backend_depl /home/<USER>
ADD backend_common /home/<USER>/backend_common

# 设置home文件夹是工作目录
WORKDIR /home/<USER>

EXPOSE 6300

# $WEB_CONCURRENCY eq --workers
#CMD ["uvicorn", "application_depl:app", "--host", "0.0.0.0", "--port", "6300"]
# CMD ["python3", "application_depl.py"]
ENTRYPOINT ["python3","application_depl.py"]

# docker build -f Dockerfile-DEPL-CPU-DINO -t  ***************:5000/inspection-backend-depl:ground-cpu .
