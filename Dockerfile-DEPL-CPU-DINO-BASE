FROM ***************:5000/inspection-backend-base:cpu

COPY ground_dino_install /home/<USER>
COPY ground_dino_install/GroundingDINO /home/<USER>

WORKDIR /home

RUN pip uninstall -y torch torchvision \
    && pip install ./ground_dino_install/torch-1.12.1+cpu-cp38-cp38-linux_x86_64.whl \
    && pip install ./ground_dino_install/torchvision-0.13.1+cpu-cp38-cp38-linux_x86_64.whl \
    && rm -rf /home/<USER>

WORKDIR /home/<USER>

RUN  pip install -e .

# docker build -f Dockerfile-DEPL-CPU-DINO-BASE -t  ***************:5000/inspection-backend-base:ground-cpu .
