# 算法编码约定

## 命名约定

* 算法基类命名 以 Reader 结尾，例如 Meter**Reader**, 遵循首字符大写的驼峰命名风格
* 算法类名称 必须以基类名称为前缀, 以 Sub{N} 为后缀, N为从零开始的编号，例
  MeterReader**Sub0**
* 算法基类：  __clazz__._name 属性名必须与 算法包文件夹名称相同, Sub{N}子类需与父类保持一致

```python
class IndicatorFlashReader(AlgorithmBase):
    """
    指示灯闪烁识别
    """

    schema_path = os.path.join(os.path.dirname(__file__), "schema.json")

    _name = "indicator_flash"  # 此名称同算法文件夹名

    ...
```

* 算法类所属python文件名： ab(Algorithm Block) 开头, 以小写的 sub{n} 结尾
  例 **ab_{__clazz__._name}_sub0** or **ab_{__clazz__._name}_sub1**

## 编程约定

* 每个算法块新增子sub后，其 __init__ 的 __all__ 需要 import 新加入的算法块
* 算法type=openCV的算法因为不涉及加载模型, 因此没有预加载算法（随用随销毁), 目前看加载&计算速度尚可
* 算法type=deeplearn的算法涉及加载模型, 因此需要提前预加载算法，以提高算法调用速度

# 算法基本信息

- alarmSupport: 声明算法是否支持报警 (无论是 默认或可配 的报警规则)
- isOverrideAlarm: 声明算法是否支持自定义(界面可配的)报警规则
- batchSupport:  声明算法是否支持在单张图上批量识别处理, 目前 batchSupport=true 等效于存在type=array的 input
- resultShowType：声明结果显示的类型，RESULT：根据算法结果显示，ALARM：根据报警结果显示

# 算法场景基准图

```json
"targetSceneImage": {
    "in_use": false,  // 是否启用，为true 后端需要处理并传入匹配场景图
    "uri": "",   // 匹配场景基准图 uri
    "name": "场景基准图"  // 描述
  },
```

# 算法入参

## 算法入参 **type**

| group | type         | 描述                   | val_range 取值定义示例                   | value返回值示例                                                        | 返回值说明            | 对应前端控件                           |
| ----- | ------------ | -------------------- | ---------------------------------- | ----------------------------------------------------------------- | ---------------- | -------------------------------- |
| 基本    | string       | 字符串                  | -                                  | "readable text"                                                   | string           | <input type="text">              |
|       | number       | 数值                   | [0,12.5,0.5,1]    #[最小,最大,精度, 默认]  | 12.5                                                              | number           | <input type="number" step="0.5"> |
| 复合    | point        | 坐标点,item为int,len=2   | -                                  | [12,34]                                                           | [x坐标,y坐标]        | 点画笔                              |
|       | color_picker | 取色笔                  | -                                  | [255, 255, 255]                                                   | 从图像中提取到的颜色值(RGB) | <input type="color" step="0.5">  |
|       | line         | 直线,item见示例           | -                                  | {"start":[12,34],"end:[56,78]}                                    | 起点、终点坐标          | 线性画笔                             |
|       | circle       | 圆,item见示例            | -                                  | {"center":[100,200],"radius":20}                                  | 圆心、半径            | 圆形画笔                             |
|       | square       | 矩形,item为4个point      | -                                  | [[100,200],[150,230],[320,200],[450,460]]                         | 左上、左下、右下、右上四点    | 矩形画笔                             |
|       | ellipse      | 椭圆,item见示例           | -                                  | {"center":[100,200],"axes":[20,40],"angle":90.0}                  | 圆心、轴、角度          | 椭圆画笔                             |
|       | polygon      | 多边形,item为多(>4)个point | -                                  | [[100,200],[150,230],[320,200],[450,460]...]                      | 多个point的数组       | 多边形画笔                            |
|       | select       | 下拉,item见示例           | {"Red":"红","Blue":"蓝"}             | Red                                                               | JSON的某一个key      | <select value="1"></select>      |
|       | multi_select | 下拉,item见示例           | {"Red":"红","Blue":"蓝"}             | ["Red", "Blue"]                                                   | JSON的某一个或者多个key  | <select value="1"></select>      |
|       | range        | 量程,item为number       | [-30.0,100.0,0.5]  #[下限默认,上限默认,精度] | [-30.5,50.5]                                                      | 下限,上限            | <input type="range" step="1">    |
|       | array        | 数组,item可为各种type      | [... ]                             | [{"start":[12,34],"end:[56,78]},{"center":[100,200],"radius":20}] | 多type的组合         | 各item逐个解析后构成多个type控件的组合          |

## 算法入参 **for_sub**

**for_sub** ：仅对子实例生效, 列表取值表示对哪些sub算法生效

* 例 "for_sub"=[0,1] 表示对 sub0, sub1 有效
* 例 "for_sub"=[] 表示该算法公共参数（子算法无关)

## 算法入参 **nullable**

**nullable** 参数是否可以为空

* 为false时 传参为空会报错

## 算法入参 **showable**

**showable** 参数是否需要绘制显示

* 为false时 此参数将不会被绘制显示

# 算法出参

## 输出如何统一格式??

 **分析**
   支持值类别： 数值(模拟量、开关量) 及其描述 、文本、BBOX、准确率
   支持批量： 单数、多数
 **结论**
   数值及其描述： 返回数值即可，对应的描述需在schema.json中给出，不再单独return, 由使用者按需取用
   文本：可读类文本直接返回即可
   bbox及其准确率:  作为points 返回
   扩展信息: 比如 仪表的指针、中心原点, 旋钮的指示、 作为points 的 扩展

  单数/批量返回形式：统一使用数组，结果与入参顺序保持一致

## 算法执行结果共 分 四部分

* ret 检测是否成功,同时作为算法结果质量位  **True 执行成功/质量OK   False 执行失败/质量Bad**
* val 检测结果(如果有,若没有留空), 每一张图支持多个结果
* points 检测到的关键结果点集(渲染时需要)。 **[bbox四个点,置信度,扩展点]**
* alarms 根据报警规则触发的报警, 每张图支持多条报警

## 示例, **points 不包含 扩展点部分**

```json
ret: True,  // 质量OK
val: [[], ['helmet', 'no_helmet', 'no_helmet', 'no_helmet']],  // 第一张图未检测出结果,第二张图有四个检测结果
points: [[], [[[[590, 79], [652, 79], [652, 145], [590, 145]], 0.80225670337677], //  [[bbox四个点],置信度,] and 无扩展点
              [[[422, 40], [451, 40], [451, 75], [422, 75]], 0.7042343616485596], //  同上
              [[[283, 25], [311, 25], [311, 58], [283, 58]], 0.6804124116897583],//  同上
              [[[357, 38], [380, 38], [380, 72], [357, 72]], 0.5978320837020874]]], //总体, 第一张图未有检测结果图形,第二张图有四个检测结果
alarms: [[(False, None, None)], [(True, 'H', 'Someone is not wearing a helmet!')]] //第一张图没有报警, 第二张图有未带安全帽告警
```

## 示例, **points 包含扩展点部分**

- 无扩展点时, 留空(不传或传空数组) 
  
  - 举例 [[bbox四个点],置信度 **,** ] or [[bbox四个点],置信度 **,[]** ]

- 有扩展点时, 传非空数组, 数组元素为需要绘制的图像类型及其相关点

- 对于数组中的元素
  
  - index==0, 为图形type,具体参考上文  "算法入参 **type**" group==复合
  
  - index==1, 为绘制对应图形需要的value取值 （与入参value返回值示例保持一致）
    
    - point: 一个point类型的点 [x,y]
    - square:  四个point类型的顶点
    - line: 两个point类型的起止点
    - circle: point类型圆心, number类型半径
    - ellipse: 圆心、轴、角度
  
  - 举例 对于旋钮档位算法，需要额外返回的扩展点是代表旋钮指示的一个矩形,
    即 [[bbox四个点],置信度, **[['square',[[100,200],[150,230],[320,200],[450,460]]]]** ]
  
  - 举例 对于指针仪表算法，假设需要额外返回的绘制点是代表仪表指针的一条线段和圆心点, 
    即 [[bbox四个点],置信度, **[['line',{"start":[12,34],"end:[56,78]}],['point',[100,200]]]**]

## 算法出参 **schema.json outputDefine. type** 定义说明

| type     | 描述  | value返回值示例       | 返回值说明    | 报警配置方式                                                         |
| -------- | --- | ---------------- | -------- | -------------------------------------------------------------- |
| string   | 字符串 | "智能化成就卓越"        | 可读string | 1. 根据alarmSupport判断是否支持配置报警 2. 若支持配置报警则从val_enum取对应数值作为报警配置界限值 |
| analogue | 模拟量 | 12.5212121212565 | float    | 1. 根据alarmSupport判断是否可以配置报警 2，直接取对应数值作为界限值                     |
| switch   | 开关量 | 1 or 0           | 只有0或1    | 同上                                                             |
| digital  | 数字量 | 12               | int      | 同上                                                             |

# TODO

- [x] 根据算法唯一标识返回算法详情接口 , 优先级1
- [x] 算法包上传接口, 优先级2
- [ ] 其他算法实现( 闪烁等 ) , 优先级2
  - [x] 液位读值 
  - [x] 指示灯亮灭(~~闪烁~~)
  - [x] 仪表读数
  - [x] 旋钮挡位
  - [x] 二维码、条形码识别
  - [ ] ~~透视变换~~
  - [x] 安全帽识别 【pp-infer】
  - [x] OCR文字识别 【pp-ocr】