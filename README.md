# 算法编码约定

## 命名约定

* 算法基类命名 以 Reader 结尾，例如 Meter**Reader**, 遵循首字符大写的驼峰命名风格
* 算法基类：  __clazz__._name 属性名必须与 算法包文件夹名称相同

```python
class IndicatorFlashReader(AlgorithmBase):
    """
    指示灯闪烁识别
    """

    schema_path = os.path.join(os.path.dirname(__file__), "schema.json")

    _name = "indicator_flash"  # 此名称同算法文件夹名

    ...
```

* 算法类所属python文件名： ab(Algorithm Block) 
  例 **ab_{__clazz__._name}** 

## 编程约定

* 算法type=openCV的算法因为不涉及加载模型, 因此没有预加载算法（随用随销毁), 目前看加载&计算速度尚可
* 算法type=deeplearn的算法涉及加载模型, 因此需要提前预加载算法，以提高算法调用速度

# 算法基本信息

- "name":  算法展示名称
- "code":  算法代码标识符
- "version": 版本控制
- "description": "人物、物体识别算法.V1",
- "type": [openCV、traditionalIR|deeplearn] 类型影响预加载策略
- "isBatchProcessing": 是否需要批量计算, ps:所有算法都应支持
- "roiDrawType": ROI绘制类型, [SQUARE|CIRCLE|POLYGON]
- "roiRequired": ROI是否必填
- "modelCode": （新增）算法关联的模型文件标志符，该字段的内容需与模型文件中标识符一致，该字段针对用户上传算法且该算法需要模型的条件下使用。
- "classification": （新增）算法的分类，例如设备仪表（DEVICE_METER）、环境安全（ENVIRONMENTAL_SAFETY）、人员行为（PERSONNEL_BEHAVIOR）、其他（OTHERS）等

# 算法模型基本信息
- "code": 算法模型文件标志符（新增）
-  注意容器启动前挂载模型上传路径 "${OUT_PROJECT_HOME}/workspace/algorithm-model:/home/<USER>"

# 算法场景基准图

```json
"targetSceneImage": {
    "uri": "",   // 匹配场景基准图 uri
    "name": "场景基准图"  // 描述
  },
```

# 算法参数

| group | type     | 描述                   | constraints 取值定义示例                                                                                                 | value返回值示例                                                                         | 返回值说明            | 对应前端控件                           |
| ----- | -------- | -------------------- | ------------------------------------------------------------------------------------------------------------------ | ---------------------------------------------------------------------------------- | ---------------- | -------------------------------- |
| 基本    | STRING   | 文本参数                 | {"maxLength": 255, "regexPattern": "^[A-Za-z0-9_]+$", "required": true}#[最大长度, 支持格式, 是否必填]                         | "readable text"                                                                    | string           | <input type="text">              |
|       | FLOAT    | 浮点参数                 | {"min": 0.0, "max": 100.0, "precision": 0.1, "required": true} #[最小,最大,精度, 是否必填]                                   | 12.5                                                                               | float            | <input type="number" step="0.5"> |
|       | INTEGER  | 整形参数                 | {"min": 0, "max": 100, "precision": 1, "required": true}   #[最小,最大,精度, 是否必填]                                       | 12                                                                                 | int              | <input type="number" step="0.5"> |
|       | BOOLEAN  | 布尔参数                 | {"required": true} 是否必填                                                                                            | false                                                                              | boolean          | <input type="number" step="0.5"> |
| 复合    | POINT    | 坐标点,item为int         | {"required": true} 是否必填                                                                                            | {"x": 100, "y": 100}                                                               | x坐标,y坐标          | 点画笔                              |
|       | CIRCLE   | 圆,item见示例            | {"required": true} 是否必填                                                                                            | {"center":{'x': 100,'y': 200},"radius":20}                                                   | 圆心、半径            | 圆形画笔                             |
|       | ELLIPSE  | 椭圆,item见示例           | {"required": true} 是否必填                                                                                            | {"center":{'x': 100,'y': 200},"axes":[20,40],"angle":90.0}                                   | 圆心、轴、角度          | 椭圆画笔                             |
|       | SQUARE   | 矩形,item为4个point      | {"required": true} 是否必填                                                                                            | [{"x": 100, "y": 100},{"x": 100,"y": 200},{"x": 200,"y": 200},{"x": 200,"y": 100}] | 左上、左下、右下、右上四点    | 矩形画笔                             |
|       | POLYGON  | 多边形,item为多(>4)个point | {"required": true} 是否必填                                                                                            | [{"x": 0,"y": 0},...]                                                              | 多个point的数组       | 多边形画笔                            |
|       | LINE     | 直线,item见示例           | {"required": true}  是否必填                                                                                           | {"start":["x":12,"y":34],"end":["x":56,"y":78]}                     | 起点、终点坐标    | 线性画笔                             |
|       | LINE_ARROW  | 带箭头的直线,item见示例       | {"required": true}  是否必填                                                                                           | {"start":["x":12,"y":34],"end":["x":56,"y":78]}                     | 起点、终点坐标,箭头在终点坐标    | 线性画笔                             |
| 增强    | RGB      | 取色笔                  | {"required": true,"minLength": 3,  "maxlength": 3}                                                                 | [255, 255, 255]                                                                    | 从图像中提取到的颜色值(RGB) | <input type="color" step="0.5">  |
|       | SELECTOR | 下拉,item见示例           | {"required": true, "maxlength": 3,"options": [{"key":"cat", "label":猫}, {"key":"dog", "label":狗}]} maxLength为1时为单选 | ["cat", "dog"]                                                                     | JSON的某一个或者多个key  | <select value="1"></select>      |
|       | RANGE    | 量程,item为int          | {"required": true}  #[下限,上限]                                                                                       | {"start": -30,"end": 50}                                                           | 下限,上限            | <input type="range" step="1">    |

## 算法入参 **required**

**required** 参数是否必填

* 为true时 传参为空会报错
* 为false时 传参类型和指定类型不一致会报错

## 算法入参 **drawToOsd**

**drawToOsd** 参数是否需要绘制显示

* 为false时 此参数将不会被绘制显示

# 算法出参

## 输出如何统一格式??

 **分析**
   支持值类别： int,float,string,bool
   支持批量： 单数、多数
 **结论**
   数值及其描述： 返回数值即可，对应的描述需在schema.json中给出，不再单独return, 由使用者按需取用
   文本：可读类文本直接返回即可

  单数/批量返回形式：统一使用数组，结果与入参顺序保持一致

## 算法执行结果共 分 三部分

* isSuccess 检测是否成功,同时作为算法结果质量位  **True 执行成功/质量OK   False 执行失败/质量Bad**
* val 检测结果(如果有,若没有留空), 每一张图支持多个结果
* osdInfo 需要绘制到osd上面的结果，包括线，圆，矩形（可带文字），多边形

## 示例, **points 不包含 扩展点部分**

```json
{
  "isSuccess": true,
  "output": {
    "luquidLevel":  {
		"value": 2.5,
		"unit": "ml",
		"resultDes": "结果描述"
	}
  },
  "osdInfo":[
    {
      "dataType": "SQUARE",
      "text": 2.5,
      "score": 0.95,
	  "unit": "ml",
      "coords": [
        {
          "x": 100,
          "y": 100
        },
        {
          "x": 100,
          "y": 200
        },
        {
          "x": 200,
          "y": 200
        },
        {
          "x": 200,
          "y": 100
        }
      ]
    },
    {
      "dataType": "SQUARE",
      "coords": [
        {
          "x": 100,
          "y": 100
        },
        {
          "x": 100,
          "y": 200
        },
        {
          "x": 200,
          "y": 200
        },
        {
          "x": 200,
          "y": 100
        }
      ]
    }
  ]
}


```

## 算法出参 **schema.json outputDefine. type** 定义说明

| type    | 描述  | value返回值示例       | 返回值说明    | 报警配置方式                                                         |
| ------- | --- | ---------------- | -------- | -------------------------------------------------------------- |
| STRING  | 字符串 | "智能化成就卓越"        | 可读string | 1. 根据alarmSupport判断是否支持配置报警 2. 若支持配置报警则从val_enum取对应数值作为报警配置界限值 |
| FLOAT   | 浮点型 | 12.5212121212565 | float    | 1. 根据alarmSupport判断是否可以配置报警 2，直接取对应数值作为界限值                     |
| BOOLEAN | 布尔型 | 1 or 0           | 只有0或1    | 同上                                                             |
| INTEGER | 整型  | 12               | int      | 同上                                                             |
