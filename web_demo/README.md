# 海康威视NVR录像回放演示程序

这是一个基于ONVIF协议的海康威视NVR录像回放功能演示程序，支持远程连接、录像查询和回放播放。

## 功能特性

- ✅ 远程连接海康威视NVR设备
- ✅ 按日期查询历史录像文件
- ✅ 获取指定时间段的录像列表
- ✅ 支持录像的远程回放播放
- ✅ 现代化的Web界面设计
- ✅ 实时状态显示和错误处理

## 系统架构

```
┌─────────────────┐    HTTP API    ┌─────────────────┐    ONVIF    ┌─────────────────┐
│   Web前端界面   │ ──────────────► │  Python后端服务 │ ──────────► │  海康威视NVR    │
│   (HTML/JS)     │                │  (FastAPI)      │             │   设备          │
└─────────────────┘                └─────────────────┘             └─────────────────┘
```

## 技术栈

### 后端
- **Python 3.8+**
- **FastAPI** - Web框架
- **onvif-zeep** - ONVIF协议支持
- **loguru** - 日志记录
- **pydantic** - 数据验证

### 前端
- **HTML5** - 页面结构
- **CSS3** - 样式设计
- **JavaScript (ES6+)** - 交互逻辑
- **Font Awesome** - 图标库

## 安装和配置

### 1. 环境要求

确保您的系统已安装：
- Python 3.8 或更高版本
- pip 包管理器

### 2. 安装依赖

项目已包含所需的Python依赖，主要包括：

```bash
# 核心依赖
fastapi==0.95.1
onvif-zeep==0.2.12
loguru==0.7.0
pydantic==1.10.7
uvicorn==0.21.1
cachetools==5.3.0

# 其他依赖
requests==2.28.2
opencv-python==********
```

### 3. 启动后端服务

```bash
# 进入项目根目录
cd E:/workspace/PycharmProjects/intelligent_inspection_backend

# 启动后端服务
cd backend_opt
python application_opt.py
```

服务将在 `http://localhost:6400` 启动。

### 4. 访问Web界面

打开浏览器，访问：
```
file:///E:/workspace/PycharmProjects/intelligent_inspection_backend/web_demo/index.html
```

或者使用本地HTTP服务器：
```bash
cd web_demo
python -m http.server 8080
# 然后访问 http://localhost:8080
```

## 使用说明

### 1. 设备连接

1. 在"设备配置"面板中输入NVR设备信息：
   - **设备IP地址**: ***************
   - **用户名**: admin
   - **密码**: Qqwe1234
   - **通道号**: 选择要查询的通道（1-4）

2. 点击"连接设备"按钮建立连接

3. 连接成功后，状态栏会显示"已连接"

### 2. 查询录像

1. 选择要查询的日期
2. 设置开始时间和结束时间（可选，默认查询全天）
3. 点击"查询录像"按钮
4. 系统会显示找到的录像文件列表

### 3. 播放录像

1. 在录像列表中点击要播放的录像文件
2. 系统会自动获取RTSP播放流地址
3. 使用播放控制按钮控制播放：
   - ▶️ 播放
   - ⏸️ 暂停  
   - ⏹️ 停止
4. 可以通过进度条跳转到指定位置

### 4. 设备信息

点击"设备信息"按钮可以查看NVR设备的详细信息，包括：
- 制造商
- 设备型号
- 固件版本
- 序列号
- 硬件ID

## API接口说明

### 录像查询接口

```http
GET /nvr/recordings/query-by-date
参数:
- ip: 设备IP地址
- user: 用户名
- pwd: 密码
- channel: 通道号
- date: 查询日期 (YYYY-MM-DD)
```

### 获取播放流接口

```http
POST /nvr/playback/stream
请求体:
{
    "ip": "***************",
    "user": "admin", 
    "pwd": "Qqwe1234",
    "channel": 1,
    "start_time": "2024-01-01 00:00:00",
    "end_time": "2024-01-01 23:59:59"
}
```

### 播放控制接口

```http
POST /nvr/playback/control
请求体:
{
    "ip": "***************",
    "user": "admin",
    "pwd": "Qqwe1234", 
    "channel": 1,
    "action": "play|pause|stop",
    "position": 0
}
```

### 设备信息接口

```http
GET /nvr/device/info
参数:
- ip: 设备IP地址
- user: 用户名
- pwd: 密码
```

## 故障排除

### 常见问题

1. **连接超时**
   - 检查网络连接
   - 确认设备IP地址正确
   - 检查防火墙设置

2. **认证失败**
   - 确认用户名和密码正确
   - 检查设备是否启用ONVIF服务

3. **无法播放录像**
   - 确认设备支持RTSP协议
   - 检查浏览器是否支持视频播放
   - 尝试使用专业的RTSP播放器

4. **查询不到录像**
   - 确认选择的日期有录像文件
   - 检查通道号是否正确
   - 确认设备存储正常

### 日志查看

后端服务会输出详细的日志信息，包括：
- 设备连接状态
- API请求响应
- 错误信息和异常

## 开发说明

### 项目结构

```
web_demo/
├── index.html              # 主页面
├── static/
│   ├── css/
│   │   └── style.css       # 样式文件
│   └── js/
│       └── app.js          # 前端逻辑
└── README.md               # 说明文档

backend_opt/
├── service/
│   ├── nvr_playback_service.py     # NVR回放服务
│   └── onvif_operate_service.py    # ONVIF操作服务
├── application_opt.py              # 主应用程序
└── backend_common/
    └── models/
        └── request/
            └── request_body.py     # 请求模型
```

### 扩展功能

可以考虑添加的功能：
- 多通道同时播放
- 录像下载功能
- 实时预览
- 录像搜索和过滤
- 播放速度控制
- 全屏播放模式

## 许可证

本项目仅用于演示和学习目的。

## 联系方式

如有问题或建议，请联系开发团队。
