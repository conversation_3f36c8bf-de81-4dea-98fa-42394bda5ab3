<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>海康威视NVR录像回放演示</title>
    <link rel="stylesheet" href="static/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <header class="header">
            <h1><i class="fas fa-video"></i> 海康威视NVR录像回放演示</h1>
            <p>基于ONVIF协议的NVR录像查询与回放功能</p>
        </header>

        <div class="main-content">
            <!-- 设备连接配置 -->
            <div class="config-panel">
                <h2><i class="fas fa-cog"></i> 设备配置</h2>
                <div class="config-form">
                    <div class="form-group">
                        <label for="deviceIp">设备IP地址:</label>
                        <input type="text" id="deviceIp" value="***************" placeholder="请输入NVR设备IP">
                    </div>
                    <div class="form-group">
                        <label for="username">用户名:</label>
                        <input type="text" id="username" value="admin" placeholder="请输入用户名">
                    </div>
                    <div class="form-group">
                        <label for="password">密码:</label>
                        <input type="password" id="password" value="Qqwe1234" placeholder="请输入密码">
                    </div>
                    <div class="form-group">
                        <label for="channel">通道号:</label>
                        <select id="channel">
                            <option value="1">通道1</option>
                            <option value="2">通道2</option>
                            <option value="3">通道3</option>
                            <option value="4">通道4</option>
                        </select>
                    </div>
                    <button id="connectBtn" class="btn btn-primary">
                        <i class="fas fa-plug"></i> 连接设备
                    </button>
                    <button id="deviceInfoBtn" class="btn btn-info">
                        <i class="fas fa-info-circle"></i> 设备信息
                    </button>
                </div>
            </div>

            <!-- 录像查询面板 -->
            <div class="query-panel">
                <h2><i class="fas fa-search"></i> 录像查询</h2>
                <div class="query-form">
                    <div class="form-group">
                        <label for="queryDate">查询日期:</label>
                        <input type="date" id="queryDate">
                    </div>
                    <div class="form-group">
                        <label for="startTime">开始时间:</label>
                        <input type="time" id="startTime" value="00:00">
                    </div>
                    <div class="form-group">
                        <label for="endTime">结束时间:</label>
                        <input type="time" id="endTime" value="23:59">
                    </div>
                    <button id="queryBtn" class="btn btn-success">
                        <i class="fas fa-search"></i> 查询录像
                    </button>
                </div>
            </div>

            <!-- 录像列表 -->
            <div class="recordings-panel">
                <h2><i class="fas fa-list"></i> 录像列表</h2>
                <div id="recordingsList" class="recordings-list">
                    <div class="no-data">
                        <i class="fas fa-folder-open"></i>
                        <p>请先查询录像文件</p>
                    </div>
                </div>
            </div>

            <!-- 录像回放面板 -->
            <div class="playback-panel">
                <h2><i class="fas fa-play-circle"></i> 录像回放</h2>
                <div class="video-container">
                    <div id="videoPlayer" class="video-player">
                        <div class="video-placeholder">
                            <i class="fas fa-video"></i>
                            <p>请选择录像文件进行回放</p>
                        </div>
                    </div>
                    
                    <!-- 播放控制栏 -->
                    <div class="playback-controls">
                        <div class="control-buttons">
                            <button id="playBtn" class="control-btn" disabled>
                                <i class="fas fa-play"></i>
                            </button>
                            <button id="pauseBtn" class="control-btn" disabled>
                                <i class="fas fa-pause"></i>
                            </button>
                            <button id="stopBtn" class="control-btn" disabled>
                                <i class="fas fa-stop"></i>
                            </button>
                        </div>
                        
                        <div class="progress-container">
                            <div class="time-display">
                                <span id="currentTime">00:00:00</span>
                            </div>
                            <div class="progress-bar">
                                <input type="range" id="progressSlider" min="0" max="100" value="0" disabled>
                            </div>
                            <div class="time-display">
                                <span id="totalTime">00:00:00</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 状态栏 -->
        <div class="status-bar">
            <div id="connectionStatus" class="status-item">
                <i class="fas fa-circle status-disconnected"></i>
                <span>未连接</span>
            </div>
            <div id="playbackStatus" class="status-item">
                <i class="fas fa-circle status-stopped"></i>
                <span>停止</span>
            </div>
        </div>
    </div>

    <!-- 消息提示 -->
    <div id="messageContainer" class="message-container"></div>

    <!-- 设备信息模态框 -->
    <div id="deviceInfoModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>设备信息</h3>
                <span class="close">&times;</span>
            </div>
            <div class="modal-body">
                <div id="deviceInfoContent">
                    <p>正在获取设备信息...</p>
                </div>
            </div>
        </div>
    </div>

    <script src="static/js/app.js"></script>
</body>
</html>
