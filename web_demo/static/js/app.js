// 全局变量
let isConnected = false;
let currentRecordings = [];
let selectedRecording = null;
let currentStreamUrl = null;
let videoElement = null;

// API基础URL
const API_BASE_URL = 'http://localhost:6400';

// DOM元素
const elements = {
    deviceIp: document.getElementById('deviceIp'),
    username: document.getElementById('username'),
    password: document.getElementById('password'),
    channel: document.getElementById('channel'),
    connectBtn: document.getElementById('connectBtn'),
    deviceInfoBtn: document.getElementById('deviceInfoBtn'),
    queryDate: document.getElementById('queryDate'),
    startTime: document.getElementById('startTime'),
    endTime: document.getElementById('endTime'),
    queryBtn: document.getElementById('queryBtn'),
    recordingsList: document.getElementById('recordingsList'),
    videoPlayer: document.getElementById('videoPlayer'),
    playBtn: document.getElementById('playBtn'),
    pauseBtn: document.getElementById('pauseBtn'),
    stopBtn: document.getElementById('stopBtn'),
    progressSlider: document.getElementById('progressSlider'),
    currentTime: document.getElementById('currentTime'),
    totalTime: document.getElementById('totalTime'),
    connectionStatus: document.getElementById('connectionStatus'),
    playbackStatus: document.getElementById('playbackStatus'),
    messageContainer: document.getElementById('messageContainer'),
    deviceInfoModal: document.getElementById('deviceInfoModal'),
    deviceInfoContent: document.getElementById('deviceInfoContent')
};

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
    bindEvents();
    setDefaultDate();
});

function initializeApp() {
    console.log('海康威视NVR录像回放演示系统初始化');
    updateConnectionStatus(false);
    updatePlaybackStatus('stopped');
}

function bindEvents() {
    // 连接设备
    elements.connectBtn.addEventListener('click', connectToDevice);
    
    // 获取设备信息
    elements.deviceInfoBtn.addEventListener('click', getDeviceInfo);
    
    // 查询录像
    elements.queryBtn.addEventListener('click', queryRecordings);
    
    // 播放控制
    elements.playBtn.addEventListener('click', () => controlPlayback('play'));
    elements.pauseBtn.addEventListener('click', () => controlPlayback('pause'));
    elements.stopBtn.addEventListener('click', () => controlPlayback('stop'));
    
    // 进度条控制
    elements.progressSlider.addEventListener('input', seekToPosition);
    
    // 模态框关闭
    document.querySelector('.close').addEventListener('click', closeModal);
    window.addEventListener('click', function(event) {
        if (event.target === elements.deviceInfoModal) {
            closeModal();
        }
    });
}

function setDefaultDate() {
    const today = new Date();
    const dateString = today.toISOString().split('T')[0];
    elements.queryDate.value = dateString;
}

// 连接设备
async function connectToDevice() {
    const ip = elements.deviceIp.value.trim();
    const user = elements.username.value.trim();
    const pwd = elements.password.value.trim();
    
    if (!ip || !user || !pwd) {
        showMessage('请填写完整的设备信息', 'error');
        return;
    }
    
    elements.connectBtn.disabled = true;
    elements.connectBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 连接中...';
    
    try {
        const response = await fetch(`${API_BASE_URL}/nvr/device/info?ip=${ip}&user=${user}&pwd=${pwd}`);
        const result = await response.json();
        
        if (result.code === 0) {
            isConnected = true;
            updateConnectionStatus(true);
            showMessage('设备连接成功', 'success');
        } else {
            throw new Error(result.msg || '连接失败');
        }
    } catch (error) {
        console.error('连接设备失败:', error);
        showMessage(`连接失败: ${error.message}`, 'error');
        updateConnectionStatus(false);
    } finally {
        elements.connectBtn.disabled = false;
        elements.connectBtn.innerHTML = '<i class="fas fa-plug"></i> 连接设备';
    }
}

// 获取设备信息
async function getDeviceInfo() {
    if (!isConnected) {
        showMessage('请先连接设备', 'error');
        return;
    }
    
    const ip = elements.deviceIp.value.trim();
    const user = elements.username.value.trim();
    const pwd = elements.password.value.trim();
    
    try {
        const response = await fetch(`${API_BASE_URL}/nvr/device/info?ip=${ip}&user=${user}&pwd=${pwd}`);
        const result = await response.json();
        
        if (result.code === 0) {
            displayDeviceInfo(result.data);
            elements.deviceInfoModal.style.display = 'block';
        } else {
            throw new Error(result.msg || '获取设备信息失败');
        }
    } catch (error) {
        console.error('获取设备信息失败:', error);
        showMessage(`获取设备信息失败: ${error.message}`, 'error');
    }
}

// 显示设备信息
function displayDeviceInfo(deviceInfo) {
    const html = `
        <div class="device-info">
            <p><strong>制造商:</strong> ${deviceInfo.manufacturer || 'N/A'}</p>
            <p><strong>型号:</strong> ${deviceInfo.model || 'N/A'}</p>
            <p><strong>固件版本:</strong> ${deviceInfo.firmware_version || 'N/A'}</p>
            <p><strong>序列号:</strong> ${deviceInfo.serial_number || 'N/A'}</p>
            <p><strong>硬件ID:</strong> ${deviceInfo.hardware_id || 'N/A'}</p>
        </div>
    `;
    elements.deviceInfoContent.innerHTML = html;
}

// 查询录像
async function queryRecordings() {
    if (!isConnected) {
        showMessage('请先连接设备', 'error');
        return;
    }
    
    const ip = elements.deviceIp.value.trim();
    const user = elements.username.value.trim();
    const pwd = elements.password.value.trim();
    const channel = parseInt(elements.channel.value);
    const date = elements.queryDate.value;
    
    if (!date) {
        showMessage('请选择查询日期', 'error');
        return;
    }
    
    elements.queryBtn.disabled = true;
    elements.queryBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 查询中...';
    
    try {
        const response = await fetch(
            `${API_BASE_URL}/nvr/recordings/query-by-date?ip=${ip}&user=${user}&pwd=${pwd}&channel=${channel}&date=${date}`
        );
        const result = await response.json();
        
        if (result.code === 0) {
            currentRecordings = result.data || [];
            displayRecordings(currentRecordings);
            showMessage(`查询到 ${currentRecordings.length} 个录像文件`, 'success');
        } else {
            throw new Error(result.msg || '查询录像失败');
        }
    } catch (error) {
        console.error('查询录像失败:', error);
        showMessage(`查询录像失败: ${error.message}`, 'error');
        displayRecordings([]);
    } finally {
        elements.queryBtn.disabled = false;
        elements.queryBtn.innerHTML = '<i class="fas fa-search"></i> 查询录像';
    }
}

// 显示录像列表
function displayRecordings(recordings) {
    if (recordings.length === 0) {
        elements.recordingsList.innerHTML = `
            <div class="no-data">
                <i class="fas fa-folder-open"></i>
                <p>未找到录像文件</p>
            </div>
        `;
        return;
    }
    
    const html = recordings.map(recording => `
        <div class="recording-item" data-recording-id="${recording.id}">
            <div class="recording-info">
                <h4>录像文件 - 通道${recording.channel}</h4>
                <p>时间: ${recording.start_time} - ${recording.end_time}</p>
                <p>时长: ${formatDuration(recording.duration)} | 大小: ${recording.file_size}</p>
            </div>
            <div class="recording-actions">
                <button class="btn btn-primary btn-sm" onclick="selectRecording('${recording.id}')">
                    <i class="fas fa-play"></i> 播放
                </button>
            </div>
        </div>
    `).join('');
    
    elements.recordingsList.innerHTML = html;
}

// 选择录像
function selectRecording(recordingId) {
    selectedRecording = currentRecordings.find(r => r.id === recordingId);
    if (!selectedRecording) return;
    
    // 更新UI选中状态
    document.querySelectorAll('.recording-item').forEach(item => {
        item.classList.remove('selected');
    });
    document.querySelector(`[data-recording-id="${recordingId}"]`).classList.add('selected');
    
    // 获取播放流
    getPlaybackStream(selectedRecording);
}

// 获取播放流
async function getPlaybackStream(recording) {
    const ip = elements.deviceIp.value.trim();
    const user = elements.username.value.trim();
    const pwd = elements.password.value.trim();
    
    try {
        const response = await fetch(`${API_BASE_URL}/nvr/playback/stream`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                ip: ip,
                user: user,
                pwd: pwd,
                channel: recording.channel,
                start_time: recording.start_time,
                end_time: recording.end_time
            })
        });
        
        const result = await response.json();
        
        if (result.code === 0) {
            currentStreamUrl = result.data.stream_url;
            setupVideoPlayer(currentStreamUrl);
            enablePlaybackControls(true);
            showMessage('录像流获取成功，可以开始播放', 'success');
        } else {
            throw new Error(result.msg || '获取播放流失败');
        }
    } catch (error) {
        console.error('获取播放流失败:', error);
        showMessage(`获取播放流失败: ${error.message}`, 'error');
    }
}

// 设置视频播放器
function setupVideoPlayer(streamUrl) {
    elements.videoPlayer.innerHTML = `
        <video class="video-element" controls>
            <source src="${streamUrl}" type="application/x-rtsp">
            您的浏览器不支持视频播放
        </video>
        <p style="color: white; text-align: center; margin-top: 10px;">
            RTSP流地址: ${streamUrl}
        </p>
    `;
    
    videoElement = elements.videoPlayer.querySelector('video');
    if (videoElement) {
        videoElement.addEventListener('loadedmetadata', function() {
            elements.totalTime.textContent = formatTime(videoElement.duration);
        });
        
        videoElement.addEventListener('timeupdate', function() {
            elements.currentTime.textContent = formatTime(videoElement.currentTime);
            if (videoElement.duration) {
                const progress = (videoElement.currentTime / videoElement.duration) * 100;
                elements.progressSlider.value = progress;
            }
        });
    }
}

// 控制播放
async function controlPlayback(action) {
    if (!selectedRecording) {
        showMessage('请先选择录像文件', 'error');
        return;
    }
    
    const ip = elements.deviceIp.value.trim();
    const user = elements.username.value.trim();
    const pwd = elements.password.value.trim();
    
    try {
        const response = await fetch(`${API_BASE_URL}/nvr/playback/control`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                ip: ip,
                user: user,
                pwd: pwd,
                channel: selectedRecording.channel,
                action: action,
                position: 0
            })
        });
        
        const result = await response.json();
        
        if (result.code === 0) {
            updatePlaybackStatus(action);
            
            // 控制HTML5视频元素
            if (videoElement) {
                switch(action) {
                    case 'play':
                        videoElement.play();
                        break;
                    case 'pause':
                        videoElement.pause();
                        break;
                    case 'stop':
                        videoElement.pause();
                        videoElement.currentTime = 0;
                        break;
                }
            }
            
            showMessage(result.data.message, 'success');
        } else {
            throw new Error(result.msg || '控制播放失败');
        }
    } catch (error) {
        console.error('控制播放失败:', error);
        showMessage(`控制播放失败: ${error.message}`, 'error');
    }
}

// 跳转到指定位置
function seekToPosition() {
    if (videoElement && videoElement.duration) {
        const position = (elements.progressSlider.value / 100) * videoElement.duration;
        videoElement.currentTime = position;
    }
}

// 启用/禁用播放控制
function enablePlaybackControls(enabled) {
    elements.playBtn.disabled = !enabled;
    elements.pauseBtn.disabled = !enabled;
    elements.stopBtn.disabled = !enabled;
    elements.progressSlider.disabled = !enabled;
}

// 更新连接状态
function updateConnectionStatus(connected) {
    const statusIcon = elements.connectionStatus.querySelector('i');
    const statusText = elements.connectionStatus.querySelector('span');
    
    if (connected) {
        statusIcon.className = 'fas fa-circle status-connected';
        statusText.textContent = '已连接';
    } else {
        statusIcon.className = 'fas fa-circle status-disconnected';
        statusText.textContent = '未连接';
    }
}

// 更新播放状态
function updatePlaybackStatus(status) {
    const statusIcon = elements.playbackStatus.querySelector('i');
    const statusText = elements.playbackStatus.querySelector('span');
    
    switch(status) {
        case 'play':
            statusIcon.className = 'fas fa-circle status-playing';
            statusText.textContent = '播放中';
            break;
        case 'pause':
            statusIcon.className = 'fas fa-circle status-paused';
            statusText.textContent = '暂停';
            break;
        case 'stop':
        default:
            statusIcon.className = 'fas fa-circle status-stopped';
            statusText.textContent = '停止';
            break;
    }
}

// 显示消息
function showMessage(message, type = 'info') {
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${type}`;
    messageDiv.textContent = message;
    
    elements.messageContainer.appendChild(messageDiv);
    
    setTimeout(() => {
        messageDiv.remove();
    }, 5000);
}

// 关闭模态框
function closeModal() {
    elements.deviceInfoModal.style.display = 'none';
}

// 格式化时间
function formatTime(seconds) {
    if (isNaN(seconds)) return '00:00:00';
    
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);
    
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
}

// 格式化时长
function formatDuration(seconds) {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    if (hours > 0) {
        return `${hours}小时${minutes}分钟`;
    } else {
        return `${minutes}分钟`;
    }
}
