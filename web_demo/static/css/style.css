/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', <PERSON><PERSON>, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

/* 头部样式 */
.header {
    text-align: center;
    margin-bottom: 30px;
    color: white;
}

.header h1 {
    font-size: 2.5em;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.header p {
    font-size: 1.2em;
    opacity: 0.9;
}

/* 主要内容区域 */
.main-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: auto auto 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

/* 面板通用样式 */
.config-panel, .query-panel, .recordings-panel, .playback-panel {
    background: white;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    backdrop-filter: blur(10px);
}

.config-panel h2, .query-panel h2, .recordings-panel h2, .playback-panel h2 {
    color: #4a5568;
    margin-bottom: 20px;
    font-size: 1.4em;
    border-bottom: 2px solid #e2e8f0;
    padding-bottom: 10px;
}

/* 表单样式 */
.config-form, .query-form {
    display: grid;
    gap: 15px;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    font-weight: 600;
    margin-bottom: 5px;
    color: #4a5568;
}

.form-group input, .form-group select {
    padding: 12px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.form-group input:focus, .form-group select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* 按钮样式 */
.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-success {
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
    color: white;
}

.btn-info {
    background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
    color: white;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* 录像列表样式 */
.recordings-panel {
    grid-column: 1 / -1;
}

.recordings-list {
    max-height: 300px;
    overflow-y: auto;
}

.recording-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    margin-bottom: 10px;
    transition: all 0.3s ease;
    cursor: pointer;
}

.recording-item:hover {
    background: #f7fafc;
    border-color: #667eea;
    transform: translateX(5px);
}

.recording-item.selected {
    background: #ebf8ff;
    border-color: #4299e1;
}

.recording-info h4 {
    color: #2d3748;
    margin-bottom: 5px;
}

.recording-info p {
    color: #718096;
    font-size: 0.9em;
}

.recording-actions {
    display: flex;
    gap: 10px;
}

.no-data {
    text-align: center;
    padding: 40px;
    color: #a0aec0;
}

.no-data i {
    font-size: 3em;
    margin-bottom: 15px;
}

/* 回放面板样式 */
.playback-panel {
    grid-column: 1 / -1;
}

.video-container {
    width: 100%;
}

.video-player {
    width: 100%;
    height: 400px;
    background: #000;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 15px;
    position: relative;
    overflow: hidden;
}

.video-placeholder {
    text-align: center;
    color: #a0aec0;
}

.video-placeholder i {
    font-size: 4em;
    margin-bottom: 15px;
}

.video-element {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

/* 播放控制栏 */
.playback-controls {
    display: flex;
    align-items: center;
    gap: 20px;
    padding: 15px;
    background: #f7fafc;
    border-radius: 8px;
}

.control-buttons {
    display: flex;
    gap: 10px;
}

.control-btn {
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 50%;
    background: #667eea;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.control-btn:hover:not(:disabled) {
    background: #5a67d8;
    transform: scale(1.1);
}

.control-btn:disabled {
    background: #cbd5e0;
    cursor: not-allowed;
}

.progress-container {
    flex: 1;
    display: flex;
    align-items: center;
    gap: 15px;
}

.time-display {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    color: #4a5568;
    min-width: 80px;
}

.progress-bar {
    flex: 1;
}

.progress-bar input[type="range"] {
    width: 100%;
    height: 6px;
    border-radius: 3px;
    background: #e2e8f0;
    outline: none;
    -webkit-appearance: none;
}

.progress-bar input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: #667eea;
    cursor: pointer;
}

/* 状态栏 */
.status-bar {
    display: flex;
    justify-content: space-between;
    background: white;
    padding: 15px 25px;
    border-radius: 12px;
    box-shadow: 0 4px 16px rgba(0,0,0,0.1);
}

.status-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
}

.status-disconnected {
    color: #e53e3e;
}

.status-connected {
    color: #38a169;
}

.status-stopped {
    color: #a0aec0;
}

.status-playing {
    color: #38a169;
}

.status-paused {
    color: #ed8936;
}

/* 消息提示 */
.message-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
}

.message {
    padding: 15px 20px;
    margin-bottom: 10px;
    border-radius: 8px;
    color: white;
    font-weight: 600;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    animation: slideIn 0.3s ease;
}

.message.success {
    background: #38a169;
}

.message.error {
    background: #e53e3e;
}

.message.info {
    background: #4299e1;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.modal-content {
    background-color: white;
    margin: 10% auto;
    padding: 0;
    border-radius: 12px;
    width: 80%;
    max-width: 600px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.3);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    border-bottom: 1px solid #e2e8f0;
}

.modal-header h3 {
    color: #2d3748;
}

.close {
    color: #a0aec0;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover {
    color: #4a5568;
}

.modal-body {
    padding: 25px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .main-content {
        grid-template-columns: 1fr;
    }
    
    .container {
        padding: 10px;
    }
    
    .header h1 {
        font-size: 2em;
    }
    
    .playback-controls {
        flex-direction: column;
        gap: 15px;
    }
    
    .progress-container {
        width: 100%;
    }
}
