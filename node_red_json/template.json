[{"id": "f3da09c6e655e303", "type": "tab", "label": "调度模板", "disabled": false, "info": "", "env": []}, {"id": "134f8fb544f95e50", "type": "group", "z": "f3da09c6e655e303", "name": "算法1", "style": {"label": true}, "nodes": ["87883c2d62fc0386", "63e6b8714f763cbd", "b5691911ec0c86fb", "0c626a9c2f73e87e", "4308d1a28124586f", "2e59bceb62e235b1", "69efb2b2e4b2df6f", "0b477bcb0b23d38e", "9e55a5b7b23901ac"], "x": 294, "y": 259, "w": 2072, "h": 202}, {"id": "6f86eb7d35a25727", "type": "inject", "z": "f3da09c6e655e303", "name": "调度启动入口", "props": [{"p": "payload"}, {"p": "topic", "vt": "str"}], "repeat": "", "crontab": "", "once": false, "onceDelay": 0.1, "topic": "", "payload": "", "payloadType": "date", "x": 170, "y": 340, "wires": [["87883c2d62fc0386"]]}, {"id": "87883c2d62fc0386", "type": "function", "z": "f3da09c6e655e303", "g": "134f8fb544f95e50", "name": "算法请求（需配置参数）", "func": "// 工程树中复制预置点信息\n// {\"id\":\"eca659d962254b8ea05c422dbf68d490\",\n// \"name\":\"55555\",\n// \"path\":\"AutoTest/视频分析/102\"\n// }\n\n// 下面两个参数需要修改\n// presetId 和 isEnableAlarm\n\nmsg.payload = {\n    \"presetId\": \"c2fd8730e8d7427299bdee305d4880a4\",\n    \"isEnableAlarm\": true\n}\nreturn msg;", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 430, "y": 340, "wires": [["63e6b8714f763cbd"]]}, {"id": "63e6b8714f763cbd", "type": "http request", "z": "f3da09c6e655e303", "g": "134f8fb544f95e50", "name": "算法调度执行(需修改URL地址)", "method": "POST", "ret": "txt", "paytoqs": "body", "url": "http://***************:9090/api-for-dcs/execute-preset", "tls": "", "persist": false, "proxy": "", "insecureHTTPParser": false, "authType": "", "senderr": false, "headers": [{"keyType": "Content-Type", "keyValue": "", "valueType": "application/json", "valueValue": ""}], "x": 710, "y": 340, "wires": [["0c626a9c2f73e87e"]]}, {"id": "b5691911ec0c86fb", "type": "switch", "z": "f3da09c6e655e303", "g": "134f8fb544f95e50", "name": "", "property": "payload", "propertyType": "msg", "rules": [{"t": "eq", "v": "0", "vt": "str"}, {"t": "else"}], "checkall": "true", "repair": false, "outputs": 2, "x": 1150, "y": 340, "wires": [["9e55a5b7b23901ac"], ["4308d1a28124586f"]]}, {"id": "0c626a9c2f73e87e", "type": "function", "z": "f3da09c6e655e303", "g": "134f8fb544f95e50", "name": "处理请求结果", "func": "// 算法请求返回\n// {\"code\":0,\"data\":\"ok\",\"msg\":\"执行成功\"}\n\n\nconst data = msg.payload\nconst data1 = JSON.parse(data)\nmsg.payload = data1.code\nreturn msg;", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 980, "y": 340, "wires": [["b5691911ec0c86fb"]]}, {"id": "4308d1a28124586f", "type": "debug", "z": "f3da09c6e655e303", "g": "134f8fb544f95e50", "name": "算法1请求失败", "active": true, "tosidebar": true, "console": false, "tostatus": false, "complete": "payload", "targetType": "msg", "statusVal": "", "statusType": "auto", "x": 1360, "y": 400, "wires": []}, {"id": "2e59bceb62e235b1", "type": "switch", "z": "f3da09c6e655e303", "g": "134f8fb544f95e50", "name": "结果判断（需配置算法返回结果）", "property": "payload", "propertyType": "msg", "rules": [{"t": "eq", "v": "闯入", "vt": "str"}, {"t": "else"}], "checkall": "true", "repair": false, "outputs": 2, "x": 1780, "y": 360, "wires": [["69efb2b2e4b2df6f"], ["0b477bcb0b23d38e"]]}, {"id": "69efb2b2e4b2df6f", "type": "debug", "z": "f3da09c6e655e303", "g": "134f8fb544f95e50", "name": "算法1：触发后续算法", "active": true, "tosidebar": true, "console": false, "tostatus": false, "complete": "payload", "targetType": "msg", "statusVal": "", "statusType": "auto", "x": 2160, "y": 320, "wires": []}, {"id": "0b477bcb0b23d38e", "type": "debug", "z": "f3da09c6e655e303", "g": "134f8fb544f95e50", "name": "算法1不满足条件，后续算法不执行", "active": true, "tosidebar": true, "console": false, "tostatus": false, "complete": "payload", "targetType": "msg", "statusVal": "", "statusType": "auto", "x": 2180, "y": 420, "wires": []}, {"id": "9e55a5b7b23901ac", "type": "redis-command", "z": "f3da09c6e655e303", "g": "134f8fb544f95e50", "server": "ae88dd122c533dbf", "command": "GET", "name": "算法1返回结果（需修改server和key）", "topic": "global-variable:cross_108", "params": "[]", "paramsType": "json", "payloadType": "json", "block": false, "x": 1430, "y": 300, "wires": [["2e59bceb62e235b1"]]}, {"id": "ae88dd122c533dbf", "type": "redis-config", "name": "***************:6379", "options": "{\"host\":\"***************\",\"port\":\"6379\",\"password\":\"654321\"}", "cluster": false, "optionsType": "json"}]