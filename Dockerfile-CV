FROM ***************:5000/backend-cv-base:1.0.1

#代码添加到home文件夹
ADD backend_cv /home/<USER>
ADD backend_common /home/<USER>/backend_common
RUN apt-get install -y vim
# 设置home文件夹是工作目录
WORKDIR /home/<USER>

EXPOSE 6200
# $WEB_CONCURRENCY eq --workers
CMD ["uvicorn", "application_cv:app", "--host", "0.0.0.0", "--port", "6200"]
#CMD ["python3", "application_cv:py"]
# docker build -f Dockerfile-CV -t ***************:5000/inspection-backend-cv:1.0.0 .
# docker build -f Dockerfile-CV -t ***************:5000/inspection-backend-cv:1.0.0-test .
# docker run --name inspection-backend-cv --restart=always \
#-v /home/<USER>/intelligent_inspection/file-server/images:/project/file-server/images/ \
#-p 6200:6200 -d  inspection-backend-cv:1.0.0

# docker run --name inspection-backend-cv --restart=always  -v E:/project/file-server/images/:/project/file-server/images/ -p 6200:6200 -d  ***************:5000/inspection-backend-cv:1.0.0
