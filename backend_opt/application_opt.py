import json
import os
from typing import Optional

import uvicorn
from cachetools import <PERSON>F<PERSON>ache
from fastapi import FastAP<PERSON>, status, HTTPException
from fastapi.exceptions import RequestValidationError
from fastapi.responses import JSONResponse
from loguru import logger
from starlette.middleware.cors import CORSMiddleware
from starlette.requests import Request

from backend_common.exceptions.inspection_exception import InspectionException, AlgorithmCheckException, \
    AlgorithmProcessException
from listener.ListenerEvent import app_startup_event, app_shutdown_event
from backend_common.models.request.ConfigPointBody import ConfigPointBody
from backend_common.models.request.CropPicReqBody import CropPicReqBody
from backend_common.models.request.DrawShapeBody import <PERSON>ShapeBody
from backend_common.models.request.MatchingPositionReqBody import MatchingPositionReqBody

from backend_common.models.request.TransformCircleReqBody import TransformCircleReqBody
from backend_common.models.request.TransformPicReqBody import TransformPicReqBody
from backend_common.models.request.TransformPointsReqBody import TransformPointsReqBody
from backend_common.models.request.ZeroPositionReqBody import ZeroPositionReqBody
from backend_common.models.response.responseBody import response, response_data_none
from service import transformCircleService, drawShapeService
from service.OnvifOperateService import OnvifOperateService
from service.cropPicService import CropPicService
from service.drawShapeService import get_scale_for_img
from service.matchingService import MatchingService
from service.transformPicService import TransformPicService
from service.transformPointsService import TransformPointsService

app = FastAPI()
app.add_event_handler("startup", app_startup_event(app))
app.add_event_handler("shutdown", app_shutdown_event(app))
cam_cache = LFUCache(maxsize=300)
cam_scale_cache = LFUCache(maxsize=300)
rtsp_scale_cache = LFUCache(maxsize=300)
# 配置允许域名列表、允许方法、请求头、cookie等
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.exception_handler(InspectionException)
def unicorn_exception_handler(request: Request, exc: InspectionException):
    logger.exception(exc)
    return JSONResponse(
        status_code=status.HTTP_200_OK,
        content={
            'code': exc.code,
            'msg': f'执行异常:{exc.message}',
            'data': None
        }
    )


@app.exception_handler(AlgorithmProcessException)
def unicorn_exception_handler(request: Request, exc: AlgorithmProcessException):
    logger.exception(exc)
    return JSONResponse(
        status_code=status.HTTP_200_OK,
        content={
            'code': exc.code,
            'msg': f'检测异常:{exc.message}',
            'data': None
        }
    )


@app.exception_handler(AlgorithmCheckException)
def unicorn_exception_handler(request: Request, exc: AlgorithmCheckException):
    logger.exception(exc)
    return JSONResponse(
        status_code=status.HTTP_200_OK,
        content={
            'code': exc.code,
            'msg': f'参数异常:{exc.message}',
            'data': None
        }
    )


@app.exception_handler(Exception)
def unicorn_exception_handler(request: Request, exc: Exception):
    logger.exception(exc)
    return JSONResponse(
        status_code=status.HTTP_200_OK,
        content={
            'code': 500,
            'msg': f'内部错误:{exc.__str__()}',
            'data': None
        }
    )


@app.exception_handler(HTTPException)
def unicorn_exception_handler(request: Request, exc: HTTPException):
    logger.exception(exc)
    return JSONResponse(
        status_code=status.HTTP_200_OK,
        content={
            'code': 500,
            'msg': f'请求失败:{exc.detail}',
            'data': None
        }
    )


@app.exception_handler(RequestValidationError)
def request_validation_exception_handler(
        request: Request, exc: RequestValidationError
) -> JSONResponse:
    logger.exception(exc)
    return JSONResponse(
        status_code=status.HTTP_200_OK,
        content={
            'code': 422,
            'msg': f'校验错误:{exc.errors()}',
            'data': None
        }
    )


# 模板与原图定位位置
@app.post("/position")
def template_position(matching_position_body: MatchingPositionReqBody):
    matching_service = MatchingService(matching_position_body.img_path, matching_position_body.tmp_paths,
                                       "min_match")
    value, result = matching_service.matching()
    if value == status.HTTP_200_OK:
        return response(data=result)
    else:
        return response(code=value, data=None, message=result)


# 校验零点P像素
@app.post("/zero/calibration")
def zero_calibration(zero_position: ZeroPositionReqBody):
    pic = [zero_position.tmp_path]
    matching_service = MatchingService(zero_position.img_path, pic, None)
    return response(data=matching_service.zero_calibration_result())


# 透视变换图片（梯形校正图片）
@app.post("/transform-pic")
def transform_pic(transform_pic_req_body: TransformPicReqBody):
    transform_pic_service = TransformPicService(transform_pic_req_body)
    return response(data=transform_pic_service.correct_pic())


# 透视变换点列表（梯形校正点列表）
@app.post("/transform-points")
def transform_points(transform_points_req_body: TransformPointsReqBody):
    transform_points_service = TransformPointsService(transform_points_req_body)
    return response(data=transform_points_service.correct_points())


# 透视变换圆形到椭圆
@app.post("/transform-circle")
def transform_circle(transform_circle_req: TransformCircleReqBody):
    return response(data=transformCircleService.execute(transform_circle_req))


# 裁剪图片
@app.post("/crop")
def crop(crop_pic_body: CropPicReqBody):
    crop_pic_service = CropPicService(crop_pic_body)
    return response(data=crop_pic_service.crop_pic())


# 绘制图形到图片中
@app.post("/draw-shape")
def draw_shape(draw_shape_body: DrawShapeBody):
    return response(data=drawShapeService.draw_shape(draw_shape_body))


# 云台移动
@app.get("/channel/relative-move")
def relative_move(ip: str, user: str, pwd: str, action: int, p_step: Optional[float] = 0.01,
                  t_step: Optional[float] = 0.01,
                  z_step: Optional[float] = 0.0625):
    onvifService = _cache_onvif_client(ip, user, pwd)
    onvifService.relative_move(action, p_step, t_step, z_step)
    return response_data_none()


# 云台移动
@app.get("/channel/start-continuous-move")
def start_continuous_move(ip: str, user: str, pwd: str, action: int):
    onvifService = _cache_onvif_client(ip, user, pwd)
    onvifService.start_continuous_move(action)
    return response_data_none()


@app.get("/channel/stop-continuous-move")
def stop_continuous_move(ip: str, user: str, pwd: str):
    onvifService = _cache_onvif_client(ip, user, pwd)
    onvifService.stop_continuous_move()
    return response_data_none()


# 截取当前图片
@app.get("/channel/pic")
def channel_current_pic(ip: str, user: str, pwd: str, point: bool = False):
    onvifService = _cache_onvif_client(ip, user, pwd)
    data = onvifService.channel_curr_pic(ip, point)
    return response(data=data)


def _cache_onvif_client(ip, user, pwd):
    onvifService = cam_cache.get(ip + '_' + user + '_' + pwd)
    if onvifService is None:
        onvifService = OnvifOperateService(ip, user, pwd)
        onvifService.connect()
        cam_cache[ip + '_' + user + '_' + pwd] = onvifService
    return onvifService


def _cache_onvif_scale(ip, user, pwd):
    onvifService = cam_scale_cache.get(ip + '_' + user + '_' + pwd)
    if onvifService is None:
        onvifService = OnvifOperateService(ip, user, pwd)
        onvifService.is_channel_available()
        cam_scale_cache[ip + '_' + user + '_' + pwd] = onvifService
    return onvifService


# 获取推流地址
@app.get("/channel/streams")
def channel_current_pic(ip: str, user: str, pwd: str):
    onvifService = _cache_onvif_client(ip, user, pwd)
    path = onvifService.all_streams()
    return response(data=path)


# 设置预置点
@app.post("/channel/set-point")
def config_point(config_point_body: ConfigPointBody):
    onvifService = _cache_onvif_client(config_point_body.ip, config_point_body.user, config_point_body.pwd)
    onvifService.set_ptz(config_point_body.p, config_point_body.t, config_point_body.z)
    return response_data_none()


# 获取坐标
@app.get("/channel/get-point")
def get_point(ip: str, user: str, pwd: str):
    onvifService = _cache_onvif_client(ip, user, pwd)
    return response(data=onvifService.get_point())


@app.get("/channel/is-available")
def is_channel_available(ip: str, user: str, pwd: str):
    onvifService = _cache_onvif_client(ip, user, pwd)
    onvifService.is_channel_available()
    return response(data=onvifService.get_point())


# 校验分辨率
@app.get("/valid-resolution-ratio")
def resolution_ratio(img_path: str, ip: str, user: str, pwd: str, rtsp: str):
    try:
        onvifService = _cache_onvif_client(ip, user, pwd)
    except Exception:
        if rtsp is not None:
            return response(data=OnvifOperateService.resolution_ration_rtsp(img_path, channel_scale_rtsp(rtsp)))
        raise InspectionException("获取分辨率失败，请检查")
    return response(data=onvifService.resolution_ration(img_path))


@app.get("/channel/scale/ip")
def channel_scale_ip(ip: str, user: str, pwd: str):
    onvifService = _cache_onvif_scale(ip, user, pwd)
    return response(data=onvifService.get_scale())


@app.get("/channel/scale/rtsp")
def channel_scale_rtsp(rtsp: str):
    rtsp_scale = rtsp_scale_cache.get(rtsp)
    if rtsp_scale is None:
        rtsp_scale = OnvifOperateService.get_scale_rtsp(rtsp)
        rtsp_scale_cache[rtsp] = rtsp_scale
    return response(data=rtsp_scale)


@app.get("/channel/scale/img")
def channel_scale_rtsp(img: str):
    return response(data=get_scale_for_img(img))


@app.get("/point/set")
def channel_scale_rtsp(ip: str, user: str, pwd: str, number: str, name: str):
    onvifService = _cache_onvif_client(ip, user, pwd)
    return response(data=onvifService.set_point_position(number, name))


@app.get("/point/goto")
def channel_scale_rtsp(ip: str, user: str, pwd: str, number: str):
    onvifService = _cache_onvif_client(ip, user, pwd)
    return response(data=onvifService.go_to_point_position(number))


@app.get("/point/remove")
def channel_scale_rtsp(ip: str, user: str, pwd: str, number: str):
    onvifService = _cache_onvif_client(ip, user, pwd)
    return response(data=onvifService.remove_point_position(number))


@app.get("/ping")
def ping_health():
    return response(data="pong")


if __name__ == '__main__':
    # conda activate detect
    # uvicorn application:app --host 0.0.0.0 --reload --port 5000
    reload = json.loads(os.getenv("UVICORN_RELOAD", 'true').lower())
    uvicorn.run(app='application_opt:app', host="0.0.0.0", port=6400, reload=reload)
