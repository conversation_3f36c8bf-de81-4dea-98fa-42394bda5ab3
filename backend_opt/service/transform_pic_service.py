import cv2
import numpy as np

from backend_common.models.request.request_body import TransformPicReqBody
from backend_common.utils import Minio<PERSON>til


def point_objs_to_arr(point_objs):
    point_arr = []
    for pos in point_objs:
        point_arr.append([pos['x'], pos['y']])
    return point_arr


# 区域矫正
def rectification(img, formPointPos, toPointPos):
    # 原图中的四个角点(左上、左下、右下、右上),与变换后矩阵位置
    form_point_arr = point_objs_to_arr(formPointPos)
    to_point_arr = point_objs_to_arr(toPointPos)
    pts1 = np.float32(form_point_arr)
    pts2 = np.float32(to_point_arr)

    # 生成透视变换矩阵；进行透视变换
    M = cv2.getPerspectiveTransform(pts1, pts2)
    new_width = to_point_arr[2][0]
    new_height = to_point_arr[2][1]
    dst = cv2.warpPerspective(img, M, (new_width, new_height))
    return dst


def execute(transform_pic_req_body: TransformPicReqBody):
    form_point_pos = transform_pic_req_body.form_square
    to_point_pos = transform_pic_req_body.to_square
    img = cv2.imread(transform_pic_req_body.img_path)
    result_img = rectification(img, form_point_pos, to_point_pos)

    result_path = MinioUtil.name_add_suffix(transform_pic_req_body.img_path, "transform")
    cv2.imwrite(result_path, result_img)
    return MinioUtil.upload(result_path)


class TransformPicService:
    def __init__(self, transform_pic_req_body: TransformPicReqBody):
        # 图片转化为本地地址
        transform_pic_req_body.img_path = MinioUtil.download(transform_pic_req_body.img_path)
        self.transform_pic_req_body = transform_pic_req_body

    def correct_pic(self):
        return execute(self.transform_pic_req_body)
