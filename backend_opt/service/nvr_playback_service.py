import json
import time
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional

import requests
from loguru import logger
from onvif import ONVIFCamera

from backend_common.exceptions.inspection_exception import InspectionException


class NVRPlaybackService:
    """海康威视NVR录像回放服务类"""
    
    def __init__(self, ip: str, user: str, password: str, port: int = 80):
        """
        初始化NVR录像回放服务
        
        Args:
            ip: NVR设备IP地址
            user: 用户名
            password: 密码
            port: ONVIF端口，默认80
        """
        self.ip = ip
        self.user = user
        self.password = password
        self.port = port
        self.mycam = None
        self.media_service = None
        self.recording_service = None
        self.replay_service = None
        
    def connect(self):
        """连接到NVR设备"""
        try:
            self.mycam = ONVIFCamera(self.ip, self.port, self.user, self.password)
            self.media_service = self.mycam.create_media_service()
            
            # 尝试创建录像相关服务
            try:
                self.recording_service = self.mycam.create_recording_service()
            except Exception as e:
                logger.warning(f"无法创建录像服务: {e}")
                
            try:
                self.replay_service = self.mycam.create_replay_service()
            except Exception as e:
                logger.warning(f"无法创建回放服务: {e}")
                
            logger.info(f"成功连接到NVR设备: {self.ip}")
            
        except Exception as e:
            logger.error(f"连接NVR设备失败: {e}")
            if 'timed out' in str(e):
                raise InspectionException("连接超时，请检查地址以及端口是否正确")
            elif 'HTTPConnectionPool' in str(e):
                raise InspectionException("连接失败，请检查地址以及端口是否正确")
            elif 'Unknown fault occured' in str(e):
                raise InspectionException("暂不支持ONVIF协议")
            else:
                raise InspectionException("请检查账号密码是否正确")
    
    def query_recordings_by_date(self, channel: int, date: str) -> List[Dict[str, Any]]:
        """
        按日期查询录像文件
        
        Args:
            channel: 通道号
            date: 日期，格式：YYYY-MM-DD
            
        Returns:
            录像文件列表
        """
        try:
            # 解析日期
            target_date = datetime.strptime(date, "%Y-%m-%d")
            start_time = target_date.replace(hour=0, minute=0, second=0, microsecond=0)
            end_time = target_date.replace(hour=23, minute=59, second=59, microsecond=999999)
            
            return self.query_recordings(channel, start_time, end_time)
            
        except ValueError as e:
            raise InspectionException(f"日期格式错误，请使用YYYY-MM-DD格式: {e}")
        except Exception as e:
            logger.error(f"查询录像失败: {e}")
            raise InspectionException(f"查询录像失败: {e}")
    
    def query_recordings(self, channel: int, start_time: datetime, end_time: datetime) -> List[Dict[str, Any]]:
        """
        查询指定时间段的录像

        Args:
            channel: 通道号
            start_time: 开始时间
            end_time: 结束时间

        Returns:
            录像文件列表
        """
        try:
            if not self.mycam:
                self.connect()

            logger.info(f"查询录像: 通道{channel}, 时间范围: {start_time} - {end_time}")

            # 调用真正的ONVIF录像查询
            recordings = self._query_recordings_via_onvif(channel, start_time, end_time)

            logger.info(f"查询到 {len(recordings)} 个录像文件")
            return recordings

        except Exception as e:
            logger.error(f"查询录像失败: {e}")
            raise InspectionException(f"查询录像失败: {e}")
    
    def _query_recordings_via_onvif(self, channel: int, start_time: datetime, end_time: datetime) -> List[Dict[str, Any]]:
        """
        通过ONVIF协议查询录像文件

        ONVIF录像查询的标准流程：
        1. 获取Recording Search服务
        2. 获取录像源信息
        3. 创建搜索范围和过滤条件
        4. 执行录像搜索
        5. 获取搜索结果
        6. 解析录像信息
        """
        recordings = []

        try:
            # 方法1: 使用Recording Search服务（推荐）
            recordings = self._search_recordings_with_search_service(channel, start_time, end_time)

            # 如果Recording Search服务不可用，尝试方法2
            if not recordings:
                logger.info("Recording Search服务不可用，尝试使用Replay服务")
                recordings = self._search_recordings_with_replay_service(channel, start_time, end_time)

            # 如果都不可用，使用方法3（海康威视特有）
            if not recordings:
                logger.info("标准ONVIF服务不可用，尝试海康威视私有接口")
                recordings = self._search_recordings_hikvision_specific(channel, start_time, end_time)

        except Exception as e:
            logger.error(f"ONVIF录像查询失败: {e}")
            logger.info("回退到模拟数据")
            return self._simulate_recording_query(channel, start_time, end_time)

        return recordings if recordings else self._simulate_recording_query(channel, start_time, end_time)

    def _search_recordings_with_search_service(self, channel: int, start_time: datetime, end_time: datetime) -> List[Dict[str, Any]]:
        """
        使用ONVIF Recording Search服务查询录像
        """
        recordings = []

        try:
            # 1. 创建Recording Search服务
            search_service = self.mycam.create_search_service()

            # 2. 获取录像源信息
            recording_sources = search_service.GetRecordingSources()
            logger.info(f"找到 {len(recording_sources)} 个录像源")

            # 3. 查找指定通道的录像源
            target_source = None
            for source in recording_sources:
                source_id = getattr(source, 'SourceId', '')
                logger.debug(f"录像源: {source_id}")

                # 匹配通道号（多种匹配方式）
                if (str(channel) in source_id or
                    f"Channel{channel}" in source_id or
                    f"ch{channel}" in source_id.lower()):
                    target_source = source
                    logger.info(f"找到匹配的录像源: {source_id}")
                    break

            if not target_source:
                logger.warning(f"未找到通道 {channel} 的录像源")
                return []

            # 4. 创建搜索范围
            search_scope = {
                'IncludedSources': [target_source.SourceId],
                'IncludedRecordings': [],
                'RecordingInformationFilter': {
                    'EarliestRecording': start_time.isoformat() + 'Z',
                    'LatestRecording': end_time.isoformat() + 'Z'
                }
            }

            # 5. 执行录像搜索
            search_result = search_service.FindRecordings(
                Scope=search_scope,
                MaxMatches=100,
                KeepAliveTime='PT30S'
            )

            if not search_result or not hasattr(search_result, 'SearchToken'):
                logger.warning("录像搜索失败")
                return []

            # 6. 获取搜索结果
            search_token = search_result.SearchToken
            logger.info(f"搜索令牌: {search_token}")

            # 7. 获取录像结果
            get_result = search_service.GetRecordingSearchResults(
                SearchToken=search_token,
                MinResults=0,
                MaxResults=100,
                WaitTime='PT10S'
            )

            # 8. 处理搜索结果
            if hasattr(get_result, 'RecordingInformation'):
                for recording_info in get_result.RecordingInformation:
                    recording = self._parse_recording_info(recording_info, channel)
                    if recording:
                        recordings.append(recording)

            # 9. 结束搜索
            try:
                search_service.EndSearch(SearchToken=search_token)
            except Exception as e:
                logger.warning(f"结束搜索失败: {e}")

            logger.info(f"通过Recording Search查询到 {len(recordings)} 个录像文件")

        except Exception as e:
            logger.error(f"Recording Search查询失败: {e}")

        return recordings

    def _search_recordings_with_replay_service(self, channel: int, start_time: datetime, end_time: datetime) -> List[Dict[str, Any]]:
        """
        使用ONVIF Replay服务查询录像（备用方法）
        """
        recordings = []

        try:
            # 1. 创建Replay服务
            replay_service = self.mycam.create_replay_service()

            # 2. 获取Replay配置
            replay_config = replay_service.GetReplayConfiguration()
            logger.info(f"Replay配置: {replay_config}")

            # 3. 获取媒体配置文件
            profiles = self.media_service.GetProfiles()
            if not profiles:
                logger.warning("未找到媒体配置文件")
                return []

            # 4. 查找指定通道的配置文件
            target_profile = None
            for profile in profiles:
                # 根据通道号匹配配置文件
                profile_name = getattr(profile, 'Name', '')
                if str(channel) in profile_name:
                    target_profile = profile
                    break

            if not target_profile:
                target_profile = profiles[0]  # 使用第一个配置文件

            # 5. 获取录像URI
            stream_setup = {
                'Stream': 'RTP-Unicast',
                'Transport': {
                    'Protocol': 'RTSP'
                }
            }

            # 注意：这里需要根据实际的ONVIF实现调整
            # 不同厂商的实现可能有所不同

            logger.info("Replay服务查询录像功能需要根据具体设备实现")

        except Exception as e:
            logger.error(f"Replay服务查询失败: {e}")

        return recordings

    def _search_recordings_hikvision_specific(self, channel: int, start_time: datetime, end_time: datetime) -> List[Dict[str, Any]]:
        """
        海康威视特有的录像查询方法
        """
        recordings = []

        try:
            # 海康威视可能有自己的私有ONVIF扩展
            # 这里需要根据海康威视的具体实现来调整

            # 1. 尝试获取设备特有的服务
            device_service = self.mycam.create_devicemgmt_service()

            # 2. 获取设备信息
            device_info = device_service.GetDeviceInformation()
            manufacturer = getattr(device_info, 'Manufacturer', '')

            if 'Hikvision' in manufacturer:
                logger.info("检测到海康威视设备，尝试使用特有接口")

                # 3. 海康威视可能支持的特殊查询方式
                # 注意：这需要根据海康威视的实际ONVIF实现来调整

                # 方法1：通过Media2服务查询
                try:
                    media2_service = self.mycam.create_media2_service()
                    profiles = media2_service.GetProfiles()
                    logger.info(f"Media2服务找到 {len(profiles)} 个配置文件")
                except:
                    logger.info("Media2服务不可用")

                # 方法2：通过Analytics服务查询
                try:
                    analytics_service = self.mycam.create_analytics_service()
                    # 这里可以尝试获取录像相关的分析数据
                    logger.info("尝试通过Analytics服务查询")
                except:
                    logger.info("Analytics服务不可用")

                # 方法3：直接构造海康威视的RTSP URL
                # 海康威视的录像回放URL格式通常为：
                # rtsp://username:password@ip:port/Streaming/tracks/trackID?starttime=yyyymmddThhmmssZ&endtime=yyyymmddThhmmssZ

                rtsp_url = self._generate_hikvision_playback_url(channel, start_time, end_time)
                if rtsp_url:
                    # 创建一个模拟的录像记录
                    recording = {
                        "id": f"hik_rec_{channel}_{start_time.strftime('%Y%m%d_%H%M%S')}",
                        "channel": channel,
                        "start_time": start_time.strftime("%Y-%m-%d %H:%M:%S"),
                        "end_time": end_time.strftime("%Y-%m-%d %H:%M:%S"),
                        "duration": int((end_time - start_time).total_seconds()),
                        "file_size": "未知",
                        "recording_token": f"hik_token_{channel}",
                        "source_id": f"hikvision_ch{channel}",
                        "file_path": rtsp_url,
                        "playback_url": rtsp_url
                    }
                    recordings.append(recording)
                    logger.info("生成海康威视录像回放URL")

        except Exception as e:
            logger.error(f"海康威视特有查询失败: {e}")

        return recordings

    def _generate_hikvision_playback_url(self, channel: int, start_time: datetime, end_time: datetime) -> str:
        """
        生成海康威视录像回放的RTSP URL
        """
        try:
            # 海康威视录像回放URL格式
            # rtsp://username:password@ip:port/Streaming/tracks/trackID?starttime=yyyymmddThhmmssZ&endtime=yyyymmddThhmmssZ

            # 格式化时间为海康威视要求的格式
            start_str = start_time.strftime("%Y%m%dT%H%M%SZ")
            end_str = end_time.strftime("%Y%m%dT%H%M%SZ")

            # 构造URL
            # trackID通常为 channel*100 + 1 (主码流) 或 channel*100 + 2 (子码流)
            track_id = channel * 100 + 1  # 主码流

            playback_url = (f"rtsp://{self.username}:{self.password}@{self.ip}:554/"
                          f"Streaming/tracks/{track_id}?"
                          f"starttime={start_str}&endtime={end_str}")

            logger.info(f"生成海康威视回放URL: {playback_url}")
            return playback_url

        except Exception as e:
            logger.error(f"生成海康威视回放URL失败: {e}")
            return ""

    def _parse_recording_info(self, recording_info, channel: int) -> Dict[str, Any]:
        """
        解析ONVIF录像信息
        """
        try:
            # 获取录像基本信息
            recording_token = getattr(recording_info, 'RecordingToken', '')

            # 获取时间信息
            earliest_recording = getattr(recording_info, 'EarliestRecording', None)
            latest_recording = getattr(recording_info, 'LatestRecording', None)

            # 转换时间格式
            if earliest_recording:
                start_time = earliest_recording.isoformat() if hasattr(earliest_recording, 'isoformat') else str(earliest_recording)
                start_time = start_time.replace('T', ' ').split('.')[0]  # 格式化为 YYYY-MM-DD HH:MM:SS
            else:
                start_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            if latest_recording:
                end_time = latest_recording.isoformat() if hasattr(latest_recording, 'isoformat') else str(latest_recording)
                end_time = end_time.replace('T', ' ').split('.')[0]
            else:
                end_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            # 计算时长
            try:
                start_dt = datetime.strptime(start_time, "%Y-%m-%d %H:%M:%S")
                end_dt = datetime.strptime(end_time, "%Y-%m-%d %H:%M:%S")
                duration = int((end_dt - start_dt).total_seconds())
            except:
                duration = 3600  # 默认1小时

            # 获取录像配置信息
            recording_config = getattr(recording_info, 'Configuration', None)
            source_id = ''
            if recording_config and hasattr(recording_config, 'Source'):
                source_id = getattr(recording_config.Source, 'SourceId', '')

            recording = {
                "id": recording_token or f"rec_{channel}_{start_time.replace(' ', '_').replace(':', '')}",
                "channel": channel,
                "start_time": start_time,
                "end_time": end_time,
                "duration": duration,
                "file_size": "未知",  # ONVIF通常不提供文件大小
                "recording_token": recording_token,
                "source_id": source_id,
                "file_path": f"onvif://recording/{recording_token}"
            }

            return recording

        except Exception as e:
            logger.error(f"解析录像信息失败: {e}")
            # 返回默认录像信息
            return {
                "id": f"rec_{channel}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                "channel": channel,
                "start_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "end_time": (datetime.now() + timedelta(hours=1)).strftime("%Y-%m-%d %H:%M:%S"),
                "duration": 3600,
                "file_size": "未知",
                "recording_token": "",
                "source_id": "",
                "file_path": ""
            }

    def _simulate_recording_query(self, channel: int, start_time: datetime, end_time: datetime) -> List[Dict[str, Any]]:
        """
        模拟录像查询（当ONVIF查询失败时使用）
        """
        recordings = []
        current_time = start_time

        # 模拟每小时有一个录像文件
        while current_time < end_time:
            next_hour = current_time + timedelta(hours=1)
            if next_hour > end_time:
                next_hour = end_time

            recording = {
                "id": f"rec_{channel}_{current_time.strftime('%Y%m%d_%H%M%S')}",
                "channel": channel,
                "start_time": current_time.strftime("%Y-%m-%d %H:%M:%S"),
                "end_time": next_hour.strftime("%Y-%m-%d %H:%M:%S"),
                "duration": int((next_hour - current_time).total_seconds()),
                "file_size": "256MB",  # 模拟文件大小
                "file_path": f"/recordings/ch{channel}/{current_time.strftime('%Y%m%d')}/{current_time.strftime('%H%M%S')}.mp4",
                "recording_token": f"sim_token_{current_time.strftime('%Y%m%d_%H%M%S')}",
                "source_id": f"source_{channel}"
            }
            recordings.append(recording)
            current_time = next_hour

        return recordings
    
    def get_playback_stream_url(self, channel: int, start_time: str, end_time: str) -> str:
        """
        获取录像回放流地址
        
        Args:
            channel: 通道号
            start_time: 开始时间，格式：YYYY-MM-DD HH:MM:SS
            end_time: 结束时间，格式：YYYY-MM-DD HH:MM:SS
            
        Returns:
            RTSP流地址
        """
        try:
            if not self.mycam:
                self.connect()
            
            # 解析时间
            start_dt = datetime.strptime(start_time, "%Y-%m-%d %H:%M:%S")
            end_dt = datetime.strptime(end_time, "%Y-%m-%d %H:%M:%S")
            
            # 构建海康威视录像回放RTSP URL
            # 格式：rtsp://username:password@ip:554/Streaming/tracks/channelid?starttime=yyyymmddthhmmssz&endtime=yyyymmddthhmmssz
            start_str = start_dt.strftime("%Y%m%dT%H%M%SZ")
            end_str = end_dt.strftime("%Y%m%dT%H%M%SZ")
            
            rtsp_url = f"rtsp://{self.user}:{self.password}@{self.ip}:554/Streaming/tracks/{channel}01?starttime={start_str}&endtime={end_str}"
            
            logger.info(f"生成录像回放流地址: {rtsp_url}")
            return rtsp_url
            
        except ValueError as e:
            raise InspectionException(f"时间格式错误，请使用YYYY-MM-DD HH:MM:SS格式: {e}")
        except Exception as e:
            logger.error(f"获取回放流地址失败: {e}")
            raise InspectionException(f"获取回放流地址失败: {e}")
    
    def control_playback(self, action: str, position: int = 0) -> Dict[str, Any]:
        """
        控制录像回放
        
        Args:
            action: 操作类型（play, pause, stop, seek）
            position: 播放位置（秒）
            
        Returns:
            操作结果
        """
        try:
            if not self.mycam:
                self.connect()
            
            result = {"action": action, "status": "success", "message": "操作成功"}
            
            if action == "play":
                result["message"] = "开始播放"
            elif action == "pause":
                result["message"] = "暂停播放"
            elif action == "stop":
                result["message"] = "停止播放"
            elif action == "seek":
                result["message"] = f"跳转到位置: {position}秒"
                result["position"] = position
            else:
                raise InspectionException(f"不支持的操作类型: {action}")
            
            logger.info(f"录像回放控制: {action}, 位置: {position}")
            return result
            
        except Exception as e:
            logger.error(f"控制录像回放失败: {e}")
            raise InspectionException(f"控制录像回放失败: {e}")
    
    def get_device_info(self) -> Dict[str, Any]:
        """获取设备信息"""
        try:
            if not self.mycam:
                self.connect()
            
            device_mgmt = self.mycam.create_devicemgmt_service()
            device_info = device_mgmt.GetDeviceInformation()
            
            return {
                "manufacturer": device_info.Manufacturer,
                "model": device_info.Model,
                "firmware_version": device_info.FirmwareVersion,
                "serial_number": device_info.SerialNumber,
                "hardware_id": device_info.HardwareId
            }
            
        except Exception as e:
            logger.error(f"获取设备信息失败: {e}")
            raise InspectionException(f"获取设备信息失败: {e}")
