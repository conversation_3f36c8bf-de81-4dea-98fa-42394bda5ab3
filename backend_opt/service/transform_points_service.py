import cv2
import numpy as np

from backend_common.models.request.request_body import TransformPointsReqBody


# 区域矫正
def rectification(input_points, form_points, to_points):
    # 计算透视逆变换矩阵
    pts1 = np.float32(form_points)
    pts2 = np.float32(to_points)
    M = cv2.getPerspectiveTransform(pts1, pts2)
    _M = np.linalg.inv(M)

    # 通过透视逆变换矩阵计算变换后的图形坐标点集合
    input_points = np.array(input_points, dtype=np.float32).reshape(-1, 1, 2)
    transform_points = cv2.perspectiveTransform(input_points, _M)
    output_points_np = np.array(transform_points).astype(int)
    output_points = []
    for pos in output_points_np:
        point = pos[0].tolist()
        output_points.append({"x": point[0], "y": point[1]})
    return output_points


def execute(transform_points_req_body: TransformPointsReqBody):
    form_point_pos = transform_points_req_body.form_square
    to_point_pos = transform_points_req_body.to_square
    input_points = transform_points_req_body.input_points

    output_points = rectification(input_points, form_point_pos, to_point_pos)
    return output_points


class TransformPointsService:
    def __init__(self, transform_points_req_body: TransformPointsReqBody):
        self.transform_points_req_body = transform_points_req_body

    def correct_points(self):
        return execute(self.transform_points_req_body)
