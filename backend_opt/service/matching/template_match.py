import cv2

from loguru import logger
from backend_common.exceptions.inspection_exception import InspectionException


def image_crop(img_path, tlbr, add_pixels=50):
    img_gray = cv2.imread(img_path, cv2.IMREAD_GRAYSCALE)

    x = tlbr[0]
    y = tlbr[1]
    w = tlbr[2] - tlbr[0]
    h = tlbr[3] - tlbr[1]

    x = x - add_pixels
    y = y - add_pixels
    w = w + (add_pixels * 2)
    h = h + (add_pixels * 2)

    if x < 0:
        x = 0
    if x + w > img_gray.shape[1]:
        w = img_gray.shape[1] - x
    if y < 0:
        y = 0
    if y + h > img_gray.shape[0]:
        h = img_gray.shape[0] - y

    img_gray_crop = img_gray[y:y + h, x:x + w]
    crop_coor = [x, y]

    return img_gray_crop, crop_coor


class TemplateMatching:
    def __init__(self, origin_img: str, tmp_img: str, match_method: str):
        # none is returned after image read failure
        self.origin_img = cv2.imread(origin_img, cv2.IMREAD_GRAYSCALE)
        self.tmp_img = cv2.imread(tmp_img, cv2.IMREAD_GRAYSCALE)

        if self.origin_img is None:
            logger.info("测试图片读取失败，请检查文件路径{}".format(origin_img))
            raise InspectionException(message="测试图片读取失败，请检查文件路径{}".format(origin_img))
        if self.tmp_img is None:
            logger.info("模版图片读取失败，请检查文件路径{}".format(tmp_img))
            raise InspectionException(message="模版图片读取失败，请检查文件路径{}".format(tmp_img))

        self.tmp_h = self.tmp_img.shape[0]
        self.tmp_w = self.tmp_img.shape[1]

        self.match_method = match_method

    def match(self):
        if self.match_method == "max_match":
            res = cv2.matchTemplate(self.origin_img, self.tmp_img, cv2.TM_CCORR_NORMED)
        if self.match_method == "min_match":
            res = cv2.matchTemplate(self.origin_img, self.tmp_img, cv2.TM_SQDIFF_NORMED)

        min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(res)
        if self.match_method == "max_match":
            top_left = max_loc
        else:
            top_left = min_loc

        bottom_right = (top_left[0] + self.tmp_w, top_left[1] + self.tmp_h)
        coor = (top_left[0], top_left[1], bottom_right[0], bottom_right[1])

        if self.match_method == "max_match":
            return max_val, coor
        else:
            return min_val, coor
