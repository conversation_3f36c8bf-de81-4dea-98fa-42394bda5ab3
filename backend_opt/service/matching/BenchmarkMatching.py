import cv2
import numpy as np


class BenchmarkMathing:
    def __init__(self, origin_img, bench_mark_path):
        self.origin_img = origin_img
        self.bench_mark_path = bench_mark_path

    def mse(self):
        # 静态方法，一步到位
        # 质量结果图quality_map就是检测图像和基准图像各个像素点差值结果
        result_static, quality_map = cv2.quality.QualityMSE_compute(cv2.imread(self.origin_img), cv2.imread(self.bench_mark_path))
        # 计算均值
        score = np.mean([i for i in result_static if (i != 0 and not np.isinf(i))])
        return 0 if np.isnan(score) else score
