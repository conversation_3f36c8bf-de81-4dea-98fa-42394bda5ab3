import cv2 as cv
import numpy as np


class BrightnessMatching:
    def __init__(self, img_path):
        self.img_path = img_path

    def brightness_matching(self):
        # 计算图片在灰度图上的均值和方差，当存在亮度异常时，均值会偏离均值点（可以假设为128），方差也会偏小；通过计算灰度图的均值和方差，就可评估图像是否存在过曝光或曝光不足
        img = cv.imread(self.img_path)
        # 把图片转换为单通道的灰度图
        gray_img = cv.cvtColor(img, cv.COLOR_BGR2GRAY)
        # 获取形状以及长宽
        img_shape = gray_img.shape
        height, width = img_shape[0], img_shape[1]
        size = gray_img.size
        # 灰度图的直方图
        hist = cv.calcHist([gray_img], [0], None, [256], [0, 256])
        # 计算灰度图像素点偏离均值(128)程序
        a = 0
        ma = 0
        # np.full 构造一个数组，用指定值填充其元素
        reduce_matrix = np.full((height, width), 128)
        shift_value = gray_img - reduce_matrix
        shift_sum = np.sum(shift_value)
        da = shift_sum / size
        # 计算偏离128的平均偏差
        for i in range(256):
            ma += (abs(i - 128 - da) * hist[i])
        m = abs(ma / size)
        # 亮度系数
        k = abs(da) / m
        if k[0] > 1:
            # 过亮
            if da > 0:
                # print("过亮")
                return 1
            else:
                # print("过暗")
                return 2
        else:
            # print("正常")
            return 0
