import cv2 as cv
import numpy as np
from loguru import logger

from backend_common.exceptions.inspection_exception import InspectionException
from backend_common.models.response.response_body import response_data_point


def feature_recognition(file_img, temp_img) -> object:
    min_match_count = 10
    # Initiate SIFT detector
    sift = cv.SIFT_create()
    # find the keypoints and descriptors with SIFT
    kp1, des1 = sift.detectAndCompute(temp_img, None)
    kp2, des2 = sift.detectAndCompute(file_img, None)

    flann_index_kdtree = 1
    index_params = dict(algorithm=flann_index_kdtree, trees=5)
    search_params = dict(checks=50)
    flann = cv.FlannBasedMatcher(index_params, search_params)
    try:
        matches = flann.knnMatch(des1, des2, k=2)
    except Exception:
        logger.error("第二阶段特征匹配，knnMatch匹配失败")
        raise InspectionException(message="第二阶段特征匹配，knnMatch匹配失败")

    # store all the good matches as per Lowe's ratio test.
    good = []
    for m, n in matches:
        if m.distance < 0.7 * n.distance:
            good.append(m)
    if len(good) > min_match_count:
        src_pts = np.float32([kp1[m.queryIdx].pt for m in good]).reshape(-1, 1, 2)
        dst_pts = np.float32([kp2[m.trainIdx].pt for m in good]).reshape(-1, 1, 2)
        M, mask = cv.findHomography(src_pts, dst_pts, cv.RANSAC, 5.0)

        if M is None:
            raise InspectionException(message="映射变换矩阵计算失败返回None:file_path={0},temp_pic={1}".format(file_img, temp_img))

        h, w = temp_img.shape
        pts = np.float32([[0, 0], [0, h - 1], [w - 1, h - 1], [w - 1, 0]]).reshape(-1, 1, 2)
        return cv.perspectiveTransform(pts, M)
    else:
        logger.info("第二阶段特征匹配，特征点匹配个数未达阈值，匹配失败")
        raise InspectionException(message="巡航无目标比对file_path={0},temp_pic={1}".format(file_img, temp_img))


class FeatureMatching:
    def __init__(self, origin_img: str, tmp_img: str):
        self.origin_img = origin_img
        self.tmp_img = tmp_img

    def cv_imread_obj(self):
        file_img = cv.imread(self.origin_img, cv.IMREAD_GRAYSCALE)
        temp_img = cv.imread(self.tmp_img, cv.IMREAD_GRAYSCALE)
        return file_img, temp_img

    def point_w_h(self):
        dst, h, hei_left, hei_right, w, width_bottom, width_top = self.point_tmp_w_h(self.origin_img, self.tmp_img)
        return dst, h, hei_left, hei_right, w, width_bottom, width_top

    @staticmethod
    def point_tmp_w_h(file_img, temp_img):
        dst = feature_recognition(file_img, temp_img)
        r_t_x = np.int32(dst)[3][0][0]
        l_t_x = np.int32(dst)[0][0][0]
        r_b_x = np.int32(dst)[2][0][0]
        l_b_x = np.int32(dst)[1][0][0]
        l_b_y = np.int32(dst)[1][0][1]
        l_t_y = np.int32(dst)[0][0][1]
        r_b_y = np.int32(dst)[2][0][1]
        r_t_y = np.int32(dst)[3][0][1]
        if r_t_x < 0 or l_t_x < 0 or r_b_x < 0 or l_b_x < 0 or l_b_y < 0 or l_t_y < 0 or r_b_y < 0 or r_t_y < 0:
            raise InspectionException(message="图像匹配坐标为负数，请校准或重新选取模板")
        width_top = r_t_x - l_t_x + 1
        width_bottom = r_b_x - l_b_x + 1
        hei_left = l_b_y - l_t_y + 1
        hei_right = r_b_y - r_t_y + 1

        h, w = temp_img.shape
        return dst, h, hei_left, hei_right, w, width_bottom, width_top

    def image_comparison(self):
        dst, h, hei_left, hei_right, w, width_bottom, width_top = self.point_w_h()

        logger.info(f'第二阶段匹配：模版图高：{h}, 模板图宽：{w}, 上宽误差={width_top-w},下宽误差={width_bottom - w}, 左高误差={hei_left - h}, 右高误差={hei_right - h}')
        if (abs(width_top-w) > 50 or abs(width_bottom - w) > 50 and abs(hei_left - h) > 50 or abs(hei_right - h) > 50):
            logger.info('第二阶段匹配：宽高误差超过阈值，匹配失败')
            raise InspectionException(message="宽高误差超过阈值，匹配失败")

        return response_data_point(np.int32(dst).tolist()[0][0][0], np.int32(dst).tolist()[0][0][1],
                                   np.int32(dst).tolist()[2][0][0], np.int32(dst).tolist()[2][0][1],
                                   np.int32(dst).tolist()[1][0][0], np.int32(dst).tolist()[1][0][1],
                                   np.int32(dst).tolist()[3][0][0], np.int32(dst).tolist()[3][0][1])

    def zero_calibration_result(self):
        file_img, temp_img = self.cv_imread_obj()
        dst, h, hei_left, hei_right, w, width_bottom, width_top = self.point_tmp_w_h(file_img, temp_img)
        img_h, img_w = file_img.shape
        pic_center = (float(img_w / 2), float(img_h / 2))
        # logger.info('上宽误差={},下宽误差={},左高误差={},右高误差={}', width_top - w, width_bottom - w, hei_left - h,
        #             hei_right - h)
        if (w + 10 >= width_top >= w - 10 or w + 10 >= width_bottom >= w - 10) \
                and (h + 10 >= hei_left >= h - 10 or h + 10 >= hei_right >= h - 10):
            if abs(width_top - w) < abs(width_bottom - w):
                center_width = width_top / 2
            else:
                center_width = width_bottom / 2
            x = np.int32(dst)[0][0][0]
            center_detect = x + center_width
            x_p = center_detect - pic_center[0]
            logger.info("x_p={}", x_p)
            if x_p > 0:
                p = x_p / 16.605 / 360
            else:
                p = x_p / 18.465 / 360
            logger.info("p={}", p)
            return p
        else:
            logger.error("图像对比结果超过模板值，未比对成功={},{}", self.origin_img, self.tmp_img)
            raise InspectionException(message="图像对比结果超过模板值，未比对成功")
