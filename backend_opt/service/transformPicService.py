import cv2
import numpy as np

from backend_common.models.request.TransformPicReqBody import TransformPicReqBody


# 区域矫正
def rectification(img, formPointPos, toPointPos):
    # 原图中的四个角点(左上、右上、右下、左下),与变换后矩阵位置
    pts1 = np.float32(formPointPos)
    pts2 = np.float32(toPointPos)

    # 生成透视变换矩阵；进行透视变换
    M = cv2.getPerspectiveTransform(pts1, pts2)
    new_width = toPointPos[2][0]
    new_height = toPointPos[2][1]
    dst = cv2.warpPerspective(img, M, (new_width, new_height))
    return dst


def execute(transform_pic_req_body: TransformPicReqBody):
    form_point_pos = transform_pic_req_body.form_square
    to_point_pos = transform_pic_req_body.to_square
    img = cv2.imread(transform_pic_req_body.img_path)
    result_img = rectification(img, form_point_pos, to_point_pos)
    result_path = transform_pic_req_body.out_path
    cv2.imwrite(result_path, result_img)
    return result_path


class TransformPicService:
    def __init__(self, transform_pic_req_body: TransformPicReqBody):
        self.transform_pic_req_body = transform_pic_req_body

    def correct_pic(self):
        return execute(self.transform_pic_req_body)
