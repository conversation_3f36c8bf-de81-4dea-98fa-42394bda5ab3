import os
import random

import cv2 as cv
from loguru import logger

from backend_common.exceptions.inspection_exception import InspectionException
from backend_common.models.request.CropPicReqBody import CropPicReqBody


class CropPicService:
    def __init__(self, crop_pic_body: CropPicReqBody):
        self.crop_pic_body = crop_pic_body

    def crop_pic(self):
        file_path = self.crop_pic_body.filePath
        top_left_y = self.crop_pic_body.topLeftY
        bottom_left_y = self.crop_pic_body.bottomLeftY
        top_left_x = self.crop_pic_body.topLeftX
        bottom_right_x = self.crop_pic_body.topRightX
        out_path = self.crop_pic_body.outPath
        try:
            img = cv.imread(file_path)
            small_img = img[int(top_left_y):int(bottom_left_y), int(top_left_x): int(bottom_right_x)]
            name = os.path.basename(file_path)
            path = out_path + str(random.randint(100, 1000)) + "_" + name
            cv.imwrite(path, small_img)
            return path
        except Exception as e:
            logger.error("裁剪图片失败,pic ={0},msg={1}".format(file_path, e.args))
            raise InspectionException("裁剪图片失败")
