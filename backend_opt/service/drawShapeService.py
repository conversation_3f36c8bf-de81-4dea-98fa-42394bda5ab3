import time
import traceback
from typing import Any, List

import cv2

from backend_common.models.request.DrawShapeBody import DrawShapeBody
from backend_common.utils.util import render_text_on_position


def draw_shape(draw_shape_body: DrawShapeBody):
    img_path = draw_shape_body.imgPath
    img = cv2.imread(img_path)
    out_path = draw_shape_body.outPath
    shape_list = draw_shape_body.osdItemList

    for shape in shape_list:
        try:
            img = do_draw(shape, img)
        except Exception as e:
            print('绘制形状错误，shape = ', str(shape), e)
            traceback.print_exc()
            continue
    cv2.imwrite(out_path, img)
    return out_path


def double_arr_to_int_arr(double_arr: List):
    return list(map(int, double_arr))


def do_draw(shape: Any, img):
    color = shape['color']
    thickness = shape['thickness']
    shape_type = shape['type']
    coordinate = shape['coordinate']
    coordinate = eval(coordinate)

    if shape_type == 'point':
        cv2.circle(img, double_arr_to_int_arr(coordinate), 5, color, -1)
    elif shape_type == 'line':
        start = coordinate['start']
        stop = coordinate['end']
        cv2.arrowedLine(img, start, stop, color, thickness)
    elif shape_type == 'circle':
        center = double_arr_to_int_arr(coordinate['center'])
        radius = coordinate['radius']
        cv2.circle(img, center, radius, color, thickness)
    elif shape_type == 'ellipse':
        center = double_arr_to_int_arr(coordinate['center'])
        axes = [coordinate['axes'][0] * 2, coordinate['axes'][1] * 2]
        angle = coordinate['angle']
        cv2.ellipse(img, (center, axes, angle), color, thickness)
    elif shape_type == 'square':
        square = coordinate
        point0 = double_arr_to_int_arr(square[0])
        point1 = double_arr_to_int_arr(square[1])
        point2 = double_arr_to_int_arr(square[2])
        point3 = double_arr_to_int_arr(square[3])
        cv2.line(img, point0, point1, color, thickness)
        cv2.line(img, point1, point2, color, thickness)
        cv2.line(img, point2, point3, color, thickness)
        cv2.line(img, point3, point0, color, thickness)
    elif shape_type == 'polygon':
        coordinate_len = len(coordinate)
        if coordinate_len < 3:
            print({'多边形点坐标个数必须大于等于3，coordinate=', coordinate})
            return
        for i in range(coordinate_len - 1):
            point0 = coordinate[i]
            point1 = coordinate[i + 1]
            cv2.line(img, point0, point1, color, thickness)
        cv2.line(img, coordinate[0], coordinate[coordinate_len - 1], color, thickness)
    elif shape_type == 'string':
        content = shape['content']
        font_size = shape['fontSize']
        if shape.__contains__('bgColor'):
            bg_color = shape['bgColor']
        else:
            bg_color = None
        # FIXME FAKE_PERCENT ??  font_scale = font_size/{BASE_FONT_SIZE}
        # text = f'{content} { "%.2f" % FAKE_PERCENT }'
        text = content
        # 颜色转化   BGR转RGB
        if bg_color is None:
            img = render_text_on_position(img, (coordinate[0], coordinate[1]), text, (color[2], color[1], color[0]),
                                          None, font_size, thickness)
        else:
            img = render_text_on_position(img, (coordinate[0], coordinate[1]), text, (color[2], color[1], color[0]),
                                          (bg_color[2], bg_color[1], bg_color[0]), font_size, thickness)
    else:
        print('shape_type 错误', shape_type)
    return img


def get_scale_for_img(img: str):
    img = cv2.imread(img, 0)
    return img.shape

# {
#     "imgPath": "E:\\temp\\202211211541031.jpg",
#     "outPath": "E:\\temp\\202211211541031_1.jpg",
#     "osdItemList": [
#         {
#             "color": [
#                 255,
#                 0,
#                 0
#             ],
#             "coordinate": "[100,200]",
#             "fontSize": 16,
#             "thickness": 1,
#             "type": "point"
#         },
#         {
#             "color": [
#                 255,
#                 255,
#                 0
#             ],
#             "coordinate": "{\"start\":[100,200],\"stop\":[300,400]}",
#             "fontSize": 16,
#             "thickness": 1,
#             "type": "line"
#         },
#         {
#             "color": [
#                 255,
#                 0,
#                 0
#             ],
#             "coordinate": "{\"angle\":45.0,\"axes\":[200,500],\"center\":[300,400]}",
#             "fontSize": 16,
#             "thickness": 1,
#             "type": "ellipse"
#         },
#         {
#             "color": [
#                 255,
#                 0,
#                 0
#             ],
#             "coordinate": "{\"leftBottom\":[100,600],\"leftTop\":[100,200],\"rightBottom\":[400,600],\"rightTop\":[400,210]}",
#             "fontSize": 16,
#             "thickness": 1,
#             "type": "square"
#         },
#         {
#             "color": [
#                 255,
#                 0,
#                 0
#             ],
#             "coordinate": "[[200,300],[400,600],[300,210]]",
#             "fontSize": 16,
#             "thickness": 3,
#             "type": "polygon"
#         },
#         {
#             "color": [
#                 255,
#                 0,
#                 0
#             ],
#             "content": "hello world, 我是后端",
#             "coordinate": "[100,200]",
#             "fontSize": 2,
#             "thickness": 1,
#             "type": "string"
#         }
#     ]
# }
