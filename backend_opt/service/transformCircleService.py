import cv2
import numpy as np

from backend_common.models.request.TransformCircleReqBody import TransformCircleReqBody
from backend_common.models.request.TransformPointsReqBody import TransformPointsReqBody
from service.transformPointsService import TransformPointsService


def get_circle_pts(center, radius):
    ret = []
    err = 0
    dx = radius
    dy = 0
    plus = 1
    minus = (radius << 1) - 1
    while dx >= dy:
        y11 = center[1] - dy
        y12 = center[1] + dy
        y21 = center[1] - dx
        y22 = center[1] + dx
        x11 = center[0] - dx
        x12 = center[0] + dx
        x21 = center[0] - dy
        x22 = center[0] + dy

        ret.append([x11, y11])
        ret.append([x11, y12])
        ret.append([x12, y11])
        ret.append([x12, y12])
        ret.append([x21, y21])
        ret.append([x21, y22])
        ret.append([x22, y21])
        ret.append([x22, y22])

        dy += 1
        err += plus
        plus += 2
        mask = (err <= 0) - 1
        err -= minus & mask
        dx += mask
        minus -= mask & 2
    return ret


def execute(transform_circle_req: TransformCircleReqBody):
    transform_points_req_body = TransformPointsReqBody()

    transform_points_req_body.form_square = transform_circle_req.form_square
    transform_points_req_body.to_square = transform_circle_req.to_square

    # 获取原图的圆形的坐标点集合
    center = transform_circle_req.center
    radius = transform_circle_req.radius
    circle_points = get_circle_pts(center, radius)
    transform_points_req_body.input_points = circle_points

    transform_points_service = TransformPointsService(transform_points_req_body)
    ellipse_points = transform_points_service.correct_points()

    img_copy = np.ones((1080, 1920, 3), dtype="uint8")
    img_copy = img_copy * 255
    for point in ellipse_points:
        cv2.circle(img_copy, [point[0], point[1]], 1, [0, 0, 0], 2, 8)

    # 进行椭圆拟合，得出椭圆参数
    img_copy = cv2.cvtColor(img_copy, cv2.COLOR_BGR2GRAY)
    _, img_copy = cv2.threshold(img_copy, 200, 255, 0)
    contours, hierarchy = cv2.findContours(img_copy, mode=cv2.RETR_LIST, method=cv2.CHAIN_APPROX_SIMPLE)
    global ellipse
    for contour in contours:
        if len(contour) >= 5:
            # 用轮廓数据来拟合椭圆
            ellipse = cv2.fitEllipse(contour)
            break

    # 绘制变换后的图形坐标点集合到空白图片上
    # img_path = "D://googleDownload//202308011612589156.JPEG"
    # img = cv2.imread(img_path)
    # cv2.ellipse(img, ellipse, (255, 0, 255), 2)
    # cv2.imwrite('D:\\temp\\202211211541031_1.jpg', img)
    #
    # print(ellipse)

    a = ellipse[1]
    ellipse = ellipse[0], (a[0] / 2, a[1] / 2), ellipse[2]
    print("ellipse", ellipse)
    return ellipse

# {
#     "imgPath": "D:\\project\\pic\\202301311453001.jpg",
#     "center": [
#         204,
#         239
#     ],
#     "radius": 204,
#     "formSquare": {
#         "leftTop": [
#             1022,
#             209
#         ],
#         "rightBottom": [
#             1268,
#             691
#         ],
#         "leftBottom": [
#             996,
#             665
#         ],
#         "rightTop": [
#             1251,
#             214
#         ]
#     },
#     "toSquare": {
#         "leftTop": [
#             0,
#             0
#         ],
#         "rightBottom": [
#             409,
#             477
#         ],
#         "leftBottom": [
#             0,
#             477
#         ],
#         "rightTop": [
#             409,
#             0
#         ]
#     }
# }
