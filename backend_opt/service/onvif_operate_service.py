import json
import time

import cv2
from cachetools import LF<PERSON>ache
from loguru import logger
from onvif import ONVIFCamera

from backend_common.constants.math_constants import NOT_SUPPORT_YUM_PLATFORM_OPERATE, NOT_SUPPORT_ONVIF
from backend_common.exceptions.inspection_exception import InspectionException, AlgorithmCheckException

root_file_path = '/project/file-server/images/'
snap_url_cache = LFUCache(maxsize=200)


# stream_url_cache = LFUCache(maxsize=200)


def goto_preset_and_monitor(ptz_service, profile_token, preset_token,
                            speed=0.7, position_tolerance=0.05,
                            timeout=20, poll_interval=0.5):
    """
    跳转到预置点并监控到达状态
    :return: 是否成功到达
    """
    # 执行跳转
    try:
        goto = ptz_service.create_type('GotoPreset')
        goto.ProfileToken = profile_token
        goto.PresetToken = preset_token
        goto.Speed = {
            'PanTilt': {'x': speed, 'y': speed},
            'Zoom': {'x': speed}
        }
        ptz_service.GotoPreset(goto)
    except Exception as e:
        logger.error(f"跳转到预置点失败: {e}")
        return False

    # 监控状态
    start_time = time.time()
    last_position = None
    stable_count = 0
    required_stable = 3  # 需要连续3次检测稳定才认为到达

    while time.time() - start_time < timeout:
        # 获取当前状态
        status = ptz_service.GetStatus({'ProfileToken': profile_token})
        current_pos = {
            'pan': status.Position.PanTilt.x,
            'tilt': status.Position.PanTilt.y,
            'zoom': status.Position.Zoom.x
        }

        # 检查是否停止移动
        is_moving = (status.MoveStatus.PanTilt != 'IDLE' or
                     status.MoveStatus.Zoom != 'IDLE')

        # 检查位置变化
        if last_position:
            position_changed = (
                    abs(current_pos['pan'] - last_position['pan']) > position_tolerance or
                    abs(current_pos['tilt'] - last_position['tilt']) > position_tolerance or
                    abs(current_pos['zoom'] - last_position['zoom']) > position_tolerance
            )
        else:
            position_changed = True

        # 更新状态
        if not is_moving and not position_changed:
            stable_count += 1
            if stable_count >= required_stable:
                return True
        else:
            stable_count = 0

        last_position = current_pos
        time.sleep(poll_interval)

    return False


def get_channel_num(profile_name):
    # 海康 Profile_101 Profile_201  Profile_1
    if profile_name.startswith("Profile_"):
        parts = profile_name.split('_')
        # 取第二部分，判断第二部分长度为1时，认为此摄像机为单通道，固定返回1
        parts_2 = parts[1]
        if len(parts_2) <= 1:
            return 1
        # 获取第一个字符
        first_digit = parts_2[0] if len(parts) > 1 and parts_2 else None
        return int(first_digit)

    # 宇视 media_profile1
    if profile_name.startswith("media_profile"):
        return 1

    # 大华 MediaProfile_Channel1_MainStream
    if profile_name.startswith("MediaProfile_Channel"):
        parts = profile_name.split('_')
        channel_part = parts[1]  # 'Channel1'
        channel_number = int(''.join(filter(str.isdigit, channel_part)))
        return channel_number - 1


class OnvifOperateService:
    def __init__(self, ip, user, password):
        """Constructor.

        Args:
            ip (str): Ip of the camera.
            user (str): Onvif login.
            password (str): Onvif password.
        """
        self.requestr = None
        self.request = None
        self.ptz = None
        self.mycam = None
        self.camera_media = None
        self.camera_media_profile = None
        self.cam_ip = ip
        self.cam_user = user
        self.cam_password = password
        # variables
        self.default = False
        self.defaultx = None
        self.defaulty = None
        self.defaultzoom = None
        self.PanTiltSpeedMax = None
        self.ZoomSpeedMax = None

    def zeep_pythonvalue(self, xmlvalue):  # 额外加的
        return xmlvalue

    def is_channel_available(self):
        try:
            self.mycam = ONVIFCamera(self.cam_ip, 80, self.cam_user, self.cam_password)
        except Exception as e:
            if 'timed out' in str(e):
                raise InspectionException("连接超时，请检查地址以及端口是否正确")
            elif 'HTTPConnectionPool' in str(e):
                raise InspectionException("连接失败，请检查地址以及端口是否正确。")
            elif 'Unknown fault occured' in str(e):
                raise InspectionException(message="暂不支持ONVIF协议。", code=NOT_SUPPORT_ONVIF)
            else:
                raise InspectionException("请检查账号密码是否正确")

    def connect(self):
        try:
            self.mycam = ONVIFCamera(self.cam_ip, 80, self.cam_user, self.cam_password)
        except Exception as e:
            if 'timed out' in str(e):
                raise InspectionException("连接超时，请检查地址以及端口是否正确")
            elif 'HTTPConnectionPool' in str(e):
                raise InspectionException("连接失败，请检查地址以及端口是否正确。")
            elif 'Unknown fault occured' in str(e):
                raise InspectionException(message="暂不支持ONVIF协议。", code=NOT_SUPPORT_ONVIF)
            else:
                raise InspectionException("请检查账号密码是否正确")
        self.camera_media = self.mycam.create_media_service()
        self.camera_media_profile = self.camera_media.GetProfiles()[0]

        self.ptz = self.mycam.create_ptz_service() if bool(self.mycam.devicemgmt.GetCapabilities().PTZ) else None
        self.ptzNode = self.ptz.GetNodes()[0] if self.ptz is not None else None
        self.PanTiltSpeedMax = self.ptzNode.SupportedPTZSpaces.ContinuousPanTiltVelocitySpace[0].XRange.Max \
            if self.ptzNode is not None and len(
            self.ptzNode.SupportedPTZSpaces.ContinuousPanTiltVelocitySpace) > 0 else None
        # PTZ缩放速度峰值
        self.ZoomSpeedMax = self.ptzNode.SupportedPTZSpaces.ContinuousZoomVelocitySpace[0].XRange.Max \
            if self.ptzNode is not None and len(
            self.ptzNode.SupportedPTZSpaces.ContinuousZoomVelocitySpace) > 0 else None
        # zeep.xsd.simple.AnySimpleType.pythonvalue = self.zeep_pythonvalue  # 额外加的
        # config = self.ptz.create_type('GetConfigurationOptions')
        # config.ConfigurationToken = self.camera_media_profile.PTZConfiguration.token
        # ptz_configuration_options = self.ptz.GetConfigurationOptions(config)

        # RelativeMove
        # self.requestr = self.ptz.create_type('RelativeMove')
        # self.requestr.ProfileToken = self.camera_media_profile.token
        # if self.requestr.Translation is None:
        #     self.requestr.Translation = self.ptz.GetStatus(
        #         {'ProfileToken': self.camera_media_profile.token}).Position
        #     self.requestr.Translation.PanTilt.space = \
        #         ptz_configuration_options.Spaces.RelativePanTiltTranslationSpace[
        #             0].URI
        #     self.requestr.Translation.Zoom.space = ptz_configuration_options.Spaces.RelativeZoomTranslationSpace[
        #         0].URI
        try:
            self.request = self.ptz.create_type('ContinuousMove')
            self.request.ProfileToken = self.camera_media_profile.token
        except Exception:
            logger.info("")
        # if self.request.Velocity is None:
        #     self.request.Velocity = self.ptz.GetStatus({'ProfileToken': self.camera_media_profile.token}).Position
        #     self.request.Velocity.PanTilt.space = \
        #     ptz_configuration_options.Spaces.ContinuousPanTiltVelocitySpace[
        #         0].URI
        #     self.request.Velocity.Zoom.space = ptz_configuration_options.Spaces.ContinuousZoomVelocitySpace[
        #         0].URI
        # XMAX = ptz_configuration_options.Spaces.ContinuousPanTiltVelocitySpace[0].XRange.Max / 2
        # XMIN = ptz_configuration_options.Spaces.ContinuousPanTiltVelocitySpace[0].XRange.Min / 2
        # YMAX = ptz_configuration_options.Spaces.ContinuousPanTiltVelocitySpace[0].YRange.Max / 2
        # YMIN = ptz_configuration_options.Spaces.ContinuousPanTiltVelocitySpace[0].YRange.Min / 2
        # Z_MAX = ptz_configuration_options.Spaces.ContinuousZoomVelocitySpace[0].XRange.Max / 2
        # Z_MIN = ptz_configuration_options.Spaces.ContinuousZoomVelocitySpace[0].XRange.Min / 2
        # PTZ云台移动速度峰值

    def channel_curr_pic(self, ip: str):
        snap_url = snap_url_cache.get(ip)
        if snap_url is None:
            obj = self.camera_media.create_type('GetSnapshotUri')
            obj.ProfileToken = self.camera_media_profile.token
            snap_url = self.camera_media.GetSnapshotUri(obj)['Uri']
            snap_url_cache[ip] = snap_url
        # auth = base64.b64encode(str(self.cam_user + ":" + self.cam_password).encode("utf-8"))
        # s = requests.session()
        # s.keep_alive = False
        # resp = s.get(snap_url, headers={"Authorization": auth})
        # time_name = time.strftime("%Y%m%d%H%M%S")
        # file_path = root_file_path + time_name + str(random.randint(100, 900)) + ".JPEG"
        # if not os.path.exists(root_file_path):
        #     os.makedirs(root_file_path)
        # with open(file_path, 'wb') as f:
        #     f.write(resp.content)
        return snap_url

    # 通过profile_name获取设备通道号
    def all_streams(self):
        obj = self.camera_media.create_type('GetStreamUri')
        obj.StreamSetup = {'Stream': 'RTP-Unicast', 'Transport': {'Protocol': 'RTSP'}}
        profiles = self.camera_media.GetProfiles()
        streams = []
        for camera_media_profile in profiles:
            if not camera_media_profile.fixed:
                continue
            # 获取通道号（待完善）
            channel_num = get_channel_num(camera_media_profile.token)
            # 获取通道RTSP地址
            obj.ProfileToken = camera_media_profile.token
            stream_url = self.camera_media.GetStreamUri(obj)['Uri']
            streams.append({"channelNum": channel_num, "rtspUrl": stream_url})

        # if stream_url is None:
        #     raise InspectionException("获取推流地址异常、稍后再试")
        return streams

    def set_ptz(self, p: float, t: float, z: float):
        if self.ptz is None:
            raise InspectionException("当前设备不支持PTZ控制", code=NOT_SUPPORT_YUM_PLATFORM_OPERATE)
        if self.ZoomSpeedMax is None and self.PanTiltSpeedMax is None:
            raise InspectionException("当前设备不支持PTZ控制", code=NOT_SUPPORT_YUM_PLATFORM_OPERATE)
        request = self.ptz.create_type('AbsoluteMove')
        request.ProfileToken = self.camera_media_profile.token
        if request.Position is None:
            request.Position = self.ptz.GetStatus({'ProfileToken': self.camera_media_profile.token}).Position
            if request.Position is None:
                raise InspectionException("当前设备不支持坐标获取", code=NOT_SUPPORT_YUM_PLATFORM_OPERATE)
        # 偏差补偿，补偿一度
        # if p < 0:
        #     p += 0.005611
        if self.PanTiltSpeedMax is not None:
            request.Position.PanTilt.x = p
            request.Position.PanTilt.y = t
        if self.ZoomSpeedMax is not None:
            request.Position.Zoom.x = z
        self.ptz.AbsoluteMove(request)

    def get_point(self):
        try:
            if self.ptz is None:
                raise InspectionException("当前设备不支持PTZ控制", code=NOT_SUPPORT_YUM_PLATFORM_OPERATE)
            if self.ZoomSpeedMax is None and self.PanTiltSpeedMax is None:
                raise InspectionException("当前设备不支持PTZ控制", code=NOT_SUPPORT_YUM_PLATFORM_OPERATE)
            p = 0
            t = 0
            z = 0
            ptz = self.mycam.create_ptz_service()
            position = ptz.GetStatus({'ProfileToken': self.camera_media_profile.token}).Position
            if position is None:
                raise InspectionException("当前设备不支持坐标获取", code=NOT_SUPPORT_YUM_PLATFORM_OPERATE)
            if self.PanTiltSpeedMax is not None:
                p = position.PanTilt.x
                t = position.PanTilt.y
            if self.ZoomSpeedMax is not None:
                z = position.Zoom.x
            return {"p": p, "t": t, "z": z}
        except Exception as e:
            logger.error(f"当前设备坐标获取失败 {e}")
            raise InspectionException("当前设备坐标获取失败")

    def preform_relative_move(self, xshift, yshift, zoom):
        # record origin position
        if self.default is False:
            # set default
            cx, cy, cz = self.getCurrentStatus()
            self.default = True
            self.defaultx = cx
            self.defaulty = cy
            self.defaultzoom = cz
        # RelativeMove
        self.requestr.Translation.PanTilt.x = xshift
        self.requestr.Translation.PanTilt.y = yshift
        self.requestr.Translation.Zoom = zoom
        self.ptz.RelativeMove(self.requestr)
        logger.info('finish relative_move')

    def getCurrentStatus(self):
        # 获取当前位置信息
        params = self.ptz.create_type('GetStatus')
        params.ProfileToken = self.camera_media_profile.token
        pantilt = self.ptz.GetStatus(params)['Position']['PanTilt']
        zoom = self.ptz.GetStatus(params)['Position']['Zoom']

        cx = pantilt['x']
        cy = pantilt['y']
        cz = zoom['x']

        return (cx, cy, cz)

    def relative_move(self, action: int, p_step: float = 0.1, t_step: float = 0.1, z_step: float = 0.0625):
        # Get PTZ configuration options for getting continuous move range
        if p_step > 1.0 or p_step < -1.0 or t_step > 1.0 or t_step < -1.0 or z_step < 0.0 or z_step > 1.0:
            raise AlgorithmCheckException("参数超过限制，请检查")
        if action == 1:
            # 左转
            self.preform_relative_move(-p_step, 0.0, 0.0)
        elif action == 2:
            # 右转
            self.preform_relative_move(p_step, 0.0, 0.0)
        elif action == 3:
            # 上抬
            self.preform_relative_move(0.0, t_step, 0.0)
        elif action == 4:
            # 下抬
            self.preform_relative_move(0.0, -t_step, 0.0)
        elif action == 5:
            # 放大倍数
            self.preform_relative_move(0.0, 0.0, z_step)
        elif action == 6:
            self.preform_relative_move(0.0, 0.0, -z_step)
        else:
            raise InspectionException("位置操作")

    def start_continuous_move(self, action: int):
        if self.ptz is None:
            raise InspectionException("当前设备不支持PTZ控制", code=NOT_SUPPORT_YUM_PLATFORM_OPERATE)
        if action == 1 or action == 2 or action == 3 or action == 4:
            if self.PanTiltSpeedMax is None:
                raise InspectionException("当前设备不支持云台控制", code=NOT_SUPPORT_YUM_PLATFORM_OPERATE)
        if action == 5 or action == 6:
            if self.ZoomSpeedMax is None:
                raise InspectionException("当前设备不支持缩放控制", code=NOT_SUPPORT_YUM_PLATFORM_OPERATE)
        # Get PTZ configuration options for getting continuous move range
        Velocity = {'PanTilt': {
            'x': 0,
            'y': 0
        }, 'Zoom': 0}
        if action == 1:
            Velocity["PanTilt"]["x"] = -self.PanTiltSpeedMax * 50 / 100
            # 左转
            self._move_left(Velocity)
        elif action == 2:
            Velocity["PanTilt"]["x"] = self.PanTiltSpeedMax * 50 / 100
            # 右转
            self._move_right(Velocity)
        elif action == 3:
            # 上抬
            Velocity["PanTilt"]["y"] = self.PanTiltSpeedMax * 50 / 100
            self._move_up(Velocity)
        elif action == 4:
            # 下抬
            Velocity["PanTilt"]["y"] = -self.PanTiltSpeedMax * 50 / 100
            self._move_down(Velocity)
        elif action == 5:
            # 放大倍数
            Velocity["Zoom"] = self.ZoomSpeedMax * 50 / 100
            self._z_up(Velocity)
        elif action == 6:
            Velocity["Zoom"] = -self.ZoomSpeedMax * 50 / 100.0
            self._z_down(Velocity)
        else:
            raise InspectionException("位置操作")

    def _move_up(self, Velocity):
        logger.info('move up...')
        self.request.Velocity = Velocity
        self.continuous_move()

    def _move_down(self, Velocity):
        logger.info('move down...')
        self.request.Velocity = Velocity
        self.continuous_move()

    def _move_right(self, Velocity):
        logger.info('move right...')
        self.request.Velocity = Velocity
        self.continuous_move()

    def _move_left(self, Velocity):
        logger.info('move left...')
        self.request.Velocity = Velocity
        self.continuous_move()

    def _z_up(self, Velocity):
        self.request.Velocity = Velocity
        self.continuous_move()

    def _z_down(self, Velocity):
        self.request.Velocity = Velocity
        self.continuous_move()

    def continuous_move(self):
        self.ptz.ContinuousMove(self.request)

    def stop_continuous_move(self):
        if self.ptz is None:
            raise InspectionException("当前设备不支持PTZ控制", code=NOT_SUPPORT_YUM_PLATFORM_OPERATE)
        self.ptz.Stop({'ProfileToken': self.request.ProfileToken})

    def get_scale(self):
        camera_media = self.mycam.create_media_service()
        camera_media_profile = camera_media.GetProfiles()[0]
        resolution = camera_media_profile.VideoEncoderConfiguration.Resolution
        return {"width": resolution.Width, "height": resolution.Height}

    @staticmethod
    def get_scale_rtsp(rtsp):
        cap = cv2.VideoCapture(rtsp)
        if not cap.isOpened():
            logger.info(f"========== {rtsp}视频流无法打开 ==========")
            raise InspectionException("获取分辨率失败请检查rtsp")
        ret, frame = cap.read()
        if not ret:
            raise InspectionException("获取分辨率失败请检查rtsp")
        h, w, c = frame.shape

        return {"width": w, "height": h}

    def resolution_ration(self, img_path: str):
        img = cv2.imread(img_path, 0)
        h, w = img.shape
        photo = {"width": w, "height": h}
        scale = self.get_scale()
        return {'result': h == scale['height'] and w == scale['width'], 'scale': scale, "photo": photo}

    @staticmethod
    def resolution_ration_rtsp(img_path: str, response):
        img = cv2.imread(img_path, 0)
        h, w = img.shape
        photo = {"width": w, "height": h}
        body = response.body
        result = json.loads(body.decode('utf-8'))
        scale = result["data"]
        return {'result': h == scale['height'] and w == scale['width'], 'scale': scale, "photo": photo}

    def set_point_position(self, number: str, name: str = None):
        ptzService = self.mycam.create_ptz_service()
        if not name:
            name = name + " " + number
        ptzService.SetPreset(
            {'ProfileToken': self.camera_media_profile.token, 'PresetToken': number, 'PresetName': name})
        return True

    def go_to_point_position(self, number: str):
        ptzService = self.mycam.create_ptz_service()
        ptzService.GotoPreset({'ProfileToken': self.camera_media_profile.token, "PresetToken": number})
        return True

    def go_to_point_position_async(self, number: str):
        ptzService = self.mycam.create_ptz_service()
        # ptzService.GotoPreset({'ProfileToken': self.camera_media_profile.token, "PresetToken": number})
        # 跳转并监控
        success = goto_preset_and_monitor(
            ptzService, self.camera_media_profile.token, number)
        return success

    def remove_point_position(self, number: str):
        ptzService = self.mycam.create_ptz_service()
        ptzService.GotoPreset({'ProfileToken': self.camera_media_profile.token, "PresetToken": number})
        return True

    def get_camera_manufacturer_info(self):
        if self.mycam is None:
            try:
                self.connect()
            except InspectionException as e:
                raise InspectionException(f"无法连接到摄像头以获取厂商信息: {e.message}", code=e.code)
            except Exception as e:
                raise InspectionException(f"连接摄像头失败: {e}", code=NOT_SUPPORT_ONVIF)  # 使用一个通用的错误码

        try:
            # 创建设备管理服务客户端
            devicemgmt_service = self.mycam.create_devicemgmt_service()
            # 调用GetDeviceInformation方法
            device_info = devicemgmt_service.GetDeviceInformation()
            logger.info(f"获取摄像头厂商信息: {device_info}")

            manufacturer = getattr(device_info, 'Manufacturer', '未知厂商')

            return {
                "manufacturer": manufacturer
            }
        except InspectionException as e:
            raise InspectionException(f"无法获取摄像头厂商信息，请检查ONVIF服务或权限: {e}", code=NOT_SUPPORT_ONVIF)
