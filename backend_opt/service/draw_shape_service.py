import time
import traceback
from typing import Any

import cv2
from loguru import logger
from backend_common.models.request.request_body import Draw<PERSON>hapeBody
from backend_common.utils import MinioUtil
from backend_common.utils.util import render_text_on_position


def draw_shape(draw_shape_body: DrawShapeBody):
    img_url = draw_shape_body.imgUrl
    img_path = MinioUtil.download(img_url)
    img = cv2.imread(img_path)
    shape_list = draw_shape_body.osdItemList

    for shape in shape_list:
        try:
            img = do_draw(shape, img)
        except Exception as e:
            logger.error(f"绘制形状错误，shape = {str(shape)} {e}")
            traceback.print_exc()
            continue

    timestamp = int(time.time() * 1000)
    result_path = MinioUtil.name_add_suffix(img_path, "drawShap" + str(timestamp))
    cv2.imwrite(result_path, img)
    out_url = MinioUtil.upload(result_path)
    return out_url


def point_to_array(point: Any):
    return [point['x'], point['y']]


def do_draw(shape: Any, img):
    shape_type = shape['dataType']
    coordinate = shape['coords']
    is_alarm = shape['isAlarm']
    shape_config = shape['osdConfig']
    if is_alarm:
        color = shape_config['colorWarn']
    else:
        color = shape_config['colorNormal']
    thickness = shape_config['fontThickness']
    # coordinate = eval(coordinate)

    if shape_type == 'POINT':
        cv2.circle(img, point_to_array(coordinate), 5, color, -1)
    elif shape_type == 'LINE':
        start = coordinate['start']
        stop = coordinate['end']
        cv2.line(img, point_to_array(start), point_to_array(stop), color, thickness)
    elif shape_type == 'LINE_ARROW':
        start = coordinate['start']
        stop = coordinate['end']
        cv2.arrowedLine(img, point_to_array(start), point_to_array(stop), color, thickness)
    elif shape_type == 'CIRCLE':
        center = coordinate['center']
        radius = coordinate['radius']
        cv2.circle(img, point_to_array(center), radius, color, thickness)
    elif shape_type == 'ELLIPSE':
        center = coordinate['center']
        axes = [coordinate['axes'][0] * 2, coordinate['axes'][1] * 2]
        angle = coordinate['angle']
        cv2.ellipse(img, (point_to_array(center), axes, angle), color, thickness)
    elif shape_type == 'SQUARE':
        # [左上、左下、右下、右上] 从左上点开始逆时针顺序
        square = coordinate
        point0 = point_to_array(square[0])
        point1 = point_to_array(square[1])
        point2 = point_to_array(square[2])
        point3 = point_to_array(square[3])
        cv2.line(img, point0, point1, color, thickness)
        cv2.line(img, point1, point2, color, thickness)
        cv2.line(img, point2, point3, color, thickness)
        cv2.line(img, point3, point0, color, thickness)
    elif shape_type == 'POLYGON':
        coordinate_len = len(coordinate)
        if coordinate_len < 3:
            logger.error(f"多边形点坐标个数必须大于等于3，coordinate= {coordinate}")
            return
        for i in range(coordinate_len - 1):
            point0 = point_to_array(coordinate[i])
            point1 = point_to_array(coordinate[i + 1])
            cv2.line(img, point0, point1, color, thickness)
        cv2.line(img, point_to_array(coordinate[0]), point_to_array(coordinate[coordinate_len - 1]), color, thickness)
    elif shape_type == 'STRING':
        content = shape['text']
        font_size = shape_config['fontSize']
        color = shape_config['fontColor']
        if shape_config.__contains__('fontBgColor'):
            bg_color = shape_config['fontBgColor']
        else:
            bg_color = None
        # FIXME FAKE_PERCENT ??  font_scale = font_size/{BASE_FONT_SIZE}
        # text = f'{content} { "%.2f" % FAKE_PERCENT }'
        text = content
        coordinate = point_to_array(coordinate)
        text_start_position = shape['textPosition']
        # 颜色转化   BGR转RGB
        if bg_color is None:
            img = render_text_on_position(img, coordinate, text, text_start_position, (color[2], color[1], color[0]),
                                          None, font_size, thickness)
        else:
            img = render_text_on_position(img, coordinate, text, text_start_position, (color[2], color[1], color[0]),
                                          (bg_color[2], bg_color[1], bg_color[0]), font_size, thickness)
    else:
        logger.error(f"shape_type 错误 {shape_type}")
    return img


def get_scale_for_img(img: str):
    img = cv2.imread(img, 0)
    return img.shape
