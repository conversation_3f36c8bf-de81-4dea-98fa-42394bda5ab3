import cv2
import numpy as np

from backend_common.exceptions.inspection_exception import InspectionException
from service.matching.FeatureMatching import FeatureMatching
from service.matching.template_match import TemplateMatching, image_crop
from loguru import logger


def coordinate_restoration(crop_coor, result_coor):
    new_result_coor = []
    for i in result_coor:
        temp_list = []
        x = i[0] + crop_coor[0]
        y = i[1] + crop_coor[1]
        temp_list.append(x)
        temp_list.append(y)
        new_result_coor.append(temp_list)
    return new_result_coor


def coordinate_transformation(coor):
    x1, y1, x2, y2 = coor
    return [[x1, y1],[x2, y1], [x2, y2], [x1, y2]]


class MatchingService:
    def __init__(self, origin_img, template_imgs: list, match_method):
        self.origin_img = origin_img
        self.template_imgs = template_imgs
        self.match_method = match_method

    def matching(self):
        val_list = []
        coor_list = []

        # first stage matching
        for temp_img in self.template_imgs:
            try:
                template_matching = TemplateMatching(self.origin_img, temp_img, match_method=self.match_method)
                val, coor = template_matching.match()
                val_list.append(val)
                coor_list.append(coor)
            except InspectionException:
                return 500, "输入图片读取失败，模板与场景匹配失败"

        val_list_index = np.argsort(val_list).tolist()

        if self.match_method == "max_match":
            indexs = val_list_index[::-1][0:2]
        if self.match_method == "min_match":
            indexs = val_list_index[0:2]

        for index in indexs:
            result_list = []
            logger.info("第一阶段匹配结果: 模版id:{0}, 匹配模板在测试图对应坐标: {1}".format(index, coor_list[index]))

            # second stage matching
            img_gray_crop, crop_coor = image_crop(self.origin_img, coor_list[index])
            template = cv2.imread(self.template_imgs[index], cv2.IMREAD_GRAYSCALE)
            try:
                feature_matching = FeatureMatching(img_gray_crop, template)
                result = feature_matching.image_comparison()
                result = coordinate_restoration(crop_coor, result)
                result_list.append(result)
            except InspectionException:
                logger.info("第二阶段匹配结果失败, 模版id:{0}".format(index))

            if result_list:
                result = coordinate_transformation(coor_list[index])
                return 200, {"index": index, "square": result}

        return 500, "模板与场景匹配失败"

    def zero_calibration_result(self):
        feature_matching = FeatureMatching(self.origin_img, self.template_imgs[0])
        return feature_matching.zero_calibration_result()
