from typing import Callable

from fastapi import FastAPI

from backend_common.config.logging import get_logger



def app_startup_event(app: FastAPI) -> Callable:
    '''
    # 应用启动的事件接收---类似应用级别上的钩子函数
    :return:
    '''

    def startup() -> None:
        get_logger('opt')
        print("应用被启动了")

    return startup


def app_shutdown_event(app: FastAPI) -> Callable:
    '''
    #  应用关闭的事件接收---类似应用级别上的钩子函数
    :return:
    '''

    def shutdown() -> None:
        print("应用被关闭了")

    return shutdown
