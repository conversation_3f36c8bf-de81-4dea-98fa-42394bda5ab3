FROM ***************:5000/inspection-backend-tensorrt:1.0.5

COPY ground_dino_install_gpu/GroundingDINO /home/<USER>
COPY ground_dino_install_gpu/torch2trt /home/<USER>

WORKDIR /home

RUN pip uninstall -y safetensors huggingface-hub

WORKDIR /home/<USER>

RUN  pip install -e .

WORKDIR /home/<USER>

RUN python setup.py install

WORKDIR /home/

RUN rm -rf torch2trt \
    # 以下删除from镜像中遗留安装包
    && rm -f tensorrt-8.6.0-cp38-none-linux_x86_64.whl \
    && rm -f torch-1.13.1+cu117-cp38-cp38-linux_x86_64.whl \
    && rm -f torchvision-0.14.1+cu117-cp38-cp38-linux_x86_64.whl

# docker build -f Dockerfile-DEPL-GPU-WQD-BASE -t  ***************:5000/inspection-backend-base:dino-vitsam-gpu-base .
