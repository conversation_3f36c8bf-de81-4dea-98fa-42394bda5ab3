ARG ARCHTECTURE
FROM 192.168.1.16:5000/inspection-backend-base:${ARCHTECTURE}dino-vitsam-gpu-base-0.2

#代码添加到home文件夹
ADD backend_depl /home/<USER>
ADD backend_common /home/<USER>/backend_common

# 设置home文件夹是工作目录
WORKDIR /home/<USER>

EXPOSE 6300

ENTRYPOINT ["python3","application_depl.py"]

#docker run --name inspection-backend-depl --restart=always \
#-v /home/<USER>/intelligent_inspection/file-server/images:/project/file-server/images/ \
#-v /home/<USER>/intelligent_inspection/file-server/voice/:/voice/ \
#--gpus all --shm-size=8g --ulimit memlock=-1 -p 6300:6300 -d  inspection-backend-depl:1.0.0

# docker build -f Dockerfile-DEPL -t  192.168.253.216:5000/inspection-backend-depl:sprint3 .