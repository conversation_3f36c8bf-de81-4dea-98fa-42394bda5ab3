{
  "$schema": "http://json-schema.org/draft-07/schema#",
  "title": "Algorithm Schema",
  "description": "增强版算法定义规范",
  "algorithmMetadata": {
    "base": {
      "name": "人物、物体识别算法",
      // 算法展示名称
      "code": "common_det",
      // 算法唯一标识符(遵守命名约定)
      "version": "1.1.0",
      // 新增版本控制
      "description": "人物、物体识别算法.V1",
      "type": "deeplearn",
      // [openCV|deeplearn] 类型影响预加载策略
      "class": "common_det"
      // 对应 clazz._name 必须与文件夹名一致
      //      "subClasses": [0,1]           // 当前支持子算法编号(取代手动维护all导入)
    },
    "capability": {
      "alarmSupport": true,
      // 是否支持报警功能
      "alarmCustomizable": false//
      是否支持界面自定义报警规则
      // "batchProcessing": true,   // 是否支持批量计算, ps:所有算法都应支持

      // 取图间隔
    },
    "roi": {
      "dataType": "POLYGON|SQUARE|CIRCLE|ELLIPSE",
      "constraints":{
        "required": false
      }
    }
  },
  "input": {
    // 示例
    "parameters": [
      //      {
      //        "id": "roi",
      //        "displayName": {"zh-CN":"感兴趣区域","en-US":"ROI"},
      //        "dataType": "POLYGON|SQUARE|CIRCLE|ELLIPSE",
      //        "constraints": {
      //          "required": false
      //        },
      //        "display": false
      //      },
      // 基础数据类型
      {
        "id": "text_param",
        "displayName": {
          "zh-CN": "文本参数",
          "en-US": "text-param"
        },
        "dataType": "STRING",
        "constraints": {
          "maxLength": 255,
          "regexPattern": "^[A-Za-z0-9_]+$",
          "required": true
        },
        "display": false
      },
      {
        "id": "float_param",
        "displayName": {
          "zh-CN": "浮点参数",
          "en-US": "float-param"
        },
        "dataType": "FLOAT",
        "default": 0,
        "constraints": {
          "min": 0.0,
          "max": 100.0,
          "precision": 0.1,
          "required": true
        },
        "display": false
        //        "val_range": {  //量程放到算法实例阶段在java侧配置
        //          "subType": "FLOAT",
        //          "constraints": {
        //            "lower": -30.0,
        //            "upper": 100.0,
        //            "precision": 0.5
        //          }
        //        }
      },
      {
        "id": "int_param",
        "displayName": {
          "zh-CN": "整形参数",
          "en-US": "int-param"
        },
        "dataType": "INT",
        "default": 0,
        "constraints": {
          "min": 0,
          "max": 100,
          "required": true
        },
        "display": false
      },
      {
        "id": "boolean_param",
        "displayName": {
          "zh-CN": "布尔参数",
          "en-US": "boolean-param"
        },
        "dataType": "BOOLEAN",
        "default": false,
        "constraints": {
          "required": true
        },
        "display": false
      },
      // 复合数据类型
      {
        "id": "coordinates",
        "displayName": {
          "zh-CN": "坐标点",
          "en-US": "point-param"
        },
        "dataType": "POINT",
        "default": [
          0.5,
          0.5
        ],
        // or {"x":0.5, “y”:0.5}
        "constraints": {
          "required": true
        },
        "display": true
      },
      {
        "id": "circle",
        "displayName": {
          "zh-CN": "仪表盘",
          "en-US": "circle-param"
        },
        "dataType": "CIRCLE",
        "default": {
          "center": {
            "x": 0.5,
            "y": 0.5
          },
          "radius": 20
        },
        "constraints": {
          "required": true
        },
        "display": true
      },
      {
        "id": "ellipse",
        "displayName": {
          "zh-CN": "椭圆",
          "en-US": "ellipse"
        },
        "dataType": "ELLIPSE",
        "default": {
          "center": {
            "x": 0.5,
            "y": 0.5
          },
          "axes": [
            20,
            40
          ],
          "angle": 20
        },
        //圆心、轴、角度
        "constraints": {
          "required": true
        },
        "display": true
      },
      {
        "id": "region",
        "displayName": {
          "zh-CN": "感兴趣区域",
          "en-US": "ROI"
        },
        "dataType": "POLYGON",
        // 多边形区域, 4个点以上
        "default": [
          {
            "x": 0,
            "y": 0
          },
          ...
        ],
        "constraints": {
          "required": true
        },
        "display": true
      },
      {
        "id": "square",
        "displayName": {
          "zh-CN": "矩形",
          "en-US": "square"
        },
        "dataType": "SQUARE",
        // 多边形区域, 4个点以上
        "default": [
          {
            "x": 0,
            "y": 0
          }
          ...
        ],
        "constraints": {
          "required": true
        },
        "display": true
      },
      {
        "id": "line_param",
        "displayName": {
          "zh-CN": "跨越计数线",
          "en-US": "a line"
        },
        "dataType": "LINE",
        "default": {
          "start": {
            "x": 0,
            "y": 0
          },
          "end": {
            "x": 0,
            "y": 0
          },
          "direct": true
          //带不带方向箭头
        },
        "constraints": {
          "required": true
        },
        "display": true
      },
      // 增强选择类型
      {
        "id": "color_picker",
        "displayName": {
          "zh-CN": "指针颜色",
          "en-US": "pointer color"
        },
        "dataType": "RGB",
        "default": [
          255,
          255,
          255
        ],
        "constraints": {
          "required": true,
          "minLength": 3,
          "maxlength": 3
        },
        "display": false
      },
      {
        "id": "color_hex",
        "displayName": {
          "zh-CN": "指针颜色",
          "en-US": "pointer color"
        },
        "dataType": "RGB_HEX",
        "default": "#FF0000",
        "constraints": {
          "required": true,
          "minLength": 7,
          "maxlength": 7
        },
        "display": false
      },
      //      {
      //        "id": "single_selector",
      //        "displayName": {"zh-CN":"单选框","en-US":"single-param"},
      //        "dataType": "SELEC",

      //        "default": [],
      //        "constraints": {
      //          "required": true,
      //        "options": [
      //          {"value": "cat", "display": "猫"},
      //          {"value": "dog", "display": "狗"}
      //        ],
      //        },
      //      "display": false,
      //      },
      {
        "id": "multi_selector",
        "displayName": {
          "zh-CN": "单选或多选框",
          "en-US": "selector-param"
        },
        "dataType": "SELECTOR",
        "default": [],
        "constraints": {
          "required": true,
          "minLength": 1,
          "maxlength": 3,
          // or 1 for single-selector
          "options": [
            {
              "value": "cat",
              "display": "猫"
            },
            {
              "value": "dog",
              "display": "狗"
            }
          ]
        },
        "display": false
      },
      // 高级类型
      {
        "id": "time_series",
        "displayName": {
          "zh-CN": "单选或多选框",
          "en-US": "selector-param"
        },
        "dataType": "TIME_SERIES",
        "default": {
          "t": [
            0,
            1,
            2
          ],
          "v": [
            0,
            5,
            3
          ],
          "s": [
            0,
            0,
            0
          ]
        },
        "constraints": {
          "required": true
        },
        "display": false
      }
    ]
  },
  "output": {
    // 示例
    //"resultDisplayType": "ALARM" // [RESULT|ALARM] 结果展示方式
    // STRING ANALOGUE SWITCH DIGITAL
    "parameters": [
      // TODO
      //      ret: True,  // 质量OK
      //      val: [[], ['helmet', 'no_helmet', 'no_helmet', 'no_helmet']],  // 第一张图未检测出结果,第二张图有四个检测结果
      //      points: [[], [[[[590, 79], [652, 79], [652, 145], [590, 145]], 0.80225670337677], //  [[bbox四个点],置信度,] and 无扩展点
      //              [[[422, 40], [451, 40], [451, 75], [422, 75]], 0.7042343616485596], //  同上
      //              [[[283, 25], [311, 25], [311, 58], [283, 58]], 0.6804124116897583],//  同上
      //              [[[357, 38], [380, 38], [380, 72], [357, 72]], 0.5978320837020874]]], //总体, 第一张图未有检测结果图形,第二张图有四个检测结果
      {
        "id": "string",
        "displayName": {
          "zh-CN": "识别对象",
          "en-US": "selector-param"
        },
        "dataType": "STRING",
        "default": "",
        "display": false
      },
      {
        "id": "analogue",
        "displayName": {
          "zh-CN": "识别对象",
          "en-US": "selector-param"
        },
        "dataType": "FLOAT",
        //
        "default": 0.0,
        "display": false
      },
      {
        "id": "switch",
        "displayName": {
          "zh-CN": "识别对象",
          "en-US": "selector-param"
        },
        "dataType": "BOOLEAN",
        //
        "default": true,
        "display": false
      },
      {
        "id": "digital",
        "displayName": {
          "zh-CN": "识别对象",
          "en-US": "selector-param"
        },
        "dataType": "INT",
        //
        "default": "",
        "display": false
      }
      // OSD 单独定义？
    ]
    ，
  }
  //  "osdInfo": [
  //    {
  //      "osdType": "line",
  //      "osd": {"start": {
  //            "x": 0,
  //            "y":0
  //          },
  //          "end": {
  //            "x": 0,
  //            "y":0
  //          }
  //      }
  //    },
  //    //
  //
  //  ],
  //
}


// Q:算法元数据定义   A: 通过algorithmMetadata 定义, 分为 base 基本信息和 capability 能力两部分
// Q:入参是否需要显示问题   A: 1. 每个input参数 display = true的都显示
// Q:参数前、后端约束校验问题   A: 由 constraints 统一约束
// TODO Q: ROI 参数如何传递问题 A: 默认每个算法实例每次识别都必带的参数，ROI单独传参
// TODO 算法识别结果图形的OSD显示问题 A: 可定义专门的osd-info 字段用于表示算法出参中可展示的部分
// TODO 左下角汇总信息显示问题 A: 每个算法实例支持单独标识/命名(or 关联到具体点项），汇总展示时按照一定规则，逐行显示
// TODO 批量传参识别问题    A: 算法调用时统一入参名称，默认支持
// TODO 多张图同时传入识别问题    A: 算法调用时统一入参名称，默认支持批量识别， batch > 1


// TODO 点项关联是直接关联算法识别结果，还是关联报警后的结果? resultShowType? / resultType?
