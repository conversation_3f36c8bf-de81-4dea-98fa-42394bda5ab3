FROM registry.baidubce.com/paddlepaddle/serving:0.7.0-cuda11.2-cudnn8-devel
COPY TensorRT-********.Linux.x86_64-gnu.cuda-11.8.tar.gz /root/TensorRT-********.Linux.x86_64-gnu.cuda-11.8.tar.gz
RUN cd /root && tar zxf TensorRT-********.Linux.x86_64-gnu.cuda-11.8.tar.gz
RUN cd /root/TensorRT-********/samples/sampleOnnxMNIST && make
RUN echo 'export PATH=/root/TensorRT-********/bin:$PATH'>> /etc/profile
CMD source /etc/profile
ADD requirements.txt /root/requirements.txt
RUN pip3 install --upgrade pip -i https://pypi.doubanio.com/simple
RUN cd /root && pip3 install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple
#docker build -t ***************:5000/inspection-backend-tensorrt:1.0.0 .
# TensorRT-********.Linux.x86_64-gnu.cuda-11.8.tar.gz位于216中的/home/<USER>/Downloads


