# ARM cann 工具包镜像
# 构建记录
# FROM ************:5000/inspection-backend-depl:cann-base

# RUN pip install minio shapely pyclipper onnxruntime
# RUN pip uninstall -y torch-npu
# 构建结果：************:5000/backend-depl-cann-base:arm64-1.0.0

ARG ARCHTECTURE
FROM ************:5000/backend-depl-cann-base:${ARCHTECTURE}1.0.0

ADD backend_depl /home/<USER>
ADD backend_common /home/<USER>/backend_common

# 设置home文件夹是工作目录
WORKDIR /home/<USER>

EXPOSE 6300

RUN sed -i 's/\r$//' /home/<USER>/run.sh && chmod +x /home/<USER>/run.sh


CMD ["/home/<USER>/run.sh"]

# docker build -f Dockerfile-DEPL -t  ************:5000/inspection-backend-depl:arm64-V1.0.0_LTSP7_2025.8.30 .

# 镜像运行
#  docker run -it --entrypoint='bash' -p 6301:6300
#  --device /dev/davinci0:/dev/davinci0
#  --device /dev/davinci_manager
#  --device /dev/devmm_svm
#  --device /dev/hisi_hdc
#  -v /usr/local/dcmi:/usr/local/dcmi
#  -v /usr/local/bin/npu-smi:/usr/local/bin/npu-smi
#  -v /usr/local/Ascend/driver/lib64/common:/usr/local/Ascend/driver/lib64/common
#  -v /usr/local/Ascend/driver/lib64/driver:/usr/local/Ascend/driver/lib64/driver
#  -v /etc/ascend_install.info:/etc/ascend_install.info
#  -v /usr/local/Ascend/driver/version.info:/usr/local/Ascend/driver/version.info
# [docker_image_name]