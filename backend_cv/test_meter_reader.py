#!/usr/bin/env python

"""Tests for `MeterReader` algorithm."""
import importlib
import json
import os

import cv2
import pytest

from backend_common.constants.alarm import AlarmRule
from backend_common.constants.constant import AlgorithmClassifyEnum, AlgorithmStrategyEnum, AlgorithmOutputTypeEnum
from backend_common.constants.math_constants import ALGORITHM_CV_SYSTEM_INNER_PATH, ALGORITHM_CV_USER_DEFINE_PATH
from backend_common.exceptions.inspection_exception import AlgorithmProcessException, InspectionException
from backend_common.maths.algorithm.base.algorithm_base import AlgorithmOutput, AlgorithmBase, AlgorithmSettings, AlgorithmInput
from backend_common.utils.util import to_camel_case

clazz_name_prefix = "MeterReader"


def do(classify, strategy, output_type, alarm_rule: AlarmRule = None):
    settings = AlgorithmSettings(classify, strategy, alarm_rules=[alarm_rule])
    basedir = os.path.dirname(os.path.abspath(__file__))

    inputs = AlgorithmInput({"images": ['/opt/tjh/cv_device_identification/liquid-level/test_images/ws.jpg'],
                             "colorSeries": "Yellow", "range": [0,100],
                             # "thresholdUpper": [335, 157], "thresholdLower": [335, 217],
                             "column": [[914, 201], [935, 512], [975, 510], [954, 199]]})

    liquid_reco: AlgorithmBase
    if settings.classify == AlgorithmClassifyEnum.Primary:
        module_primary = importlib.import_module(".liquid_level.ab_liquid_level_reco_primary",
                                                 package=ALGORITHM_CV_USER_DEFINE_PATH)
        clazz_ = getattr(module_primary, f"{clazz_name_prefix}{AlgorithmClassifyEnum.Primary.name}")
        liquid_reco: AlgorithmBase = clazz_(inputs, 1)
    elif settings.classify == AlgorithmClassifyEnum.Secondary:
        module_secondary = importlib.import_module(".liquid_level.ab_liquid_level_reco_secondary",
                                                   package=ALGORITHM_CV_USER_DEFINE_PATH)
        clazz_ = getattr(module_secondary, f"{clazz_name_prefix}{AlgorithmClassifyEnum.Secondary.name}")
        liquid_reco: AlgorithmBase = clazz_(inputs, 0)
    else:
        raise AlgorithmProcessException(f"Unsupported algorithm classify: {settings.classify}")

    assert liquid_reco is not None
    ret, val, points, alarms = liquid_reco.perform(settings)
    print(f"结果 {ret, val, points, alarms}")

    outputs = AlgorithmOutput(ret, val, points, alarms)
    print(f"outputs {str(outputs)}")

    # 可选操作
    ret_img = liquid_reco.gen_result_img()
    if ret_img is not None:
        cv2.imwrite(os.getcwd() + "/40_result.jpg", ret_img)

    return outputs


def do_itself(algorithm_index, classify, strategy, output_type, alarm_rule: AlarmRule = None):
    settings = AlgorithmSettings(classify, strategy, alarm_rules=alarm_rule)
    basedir = os.path.dirname(os.path.abspath(__file__))
    algorithm_name = "meter"

    inputs = AlgorithmInput({"images": ["/opt/tjh/model_server/inspection-backend-cv/202307211700005481.jpg"],
                             "meterList": [
            {
                "endPoint": [
                    864,
                    334
                ],
                "roiSquare": {
                    "center": [
                        783,
                        283
                    ],
                    "radius": 96
                },
                "zeroPoint": [
                    723,
                    205
                ],
                "startPoint": [
                    699,
                    334
                ],
                "range": [
                    -30,
                    50
                ],
                "colorSeries": "Gray"
            }
            # {
            #     "endPoint": [
            #         1249,
            #         288
            #     ],
            #     "roiSquare": {
            #         "center": [
            #             1155,
            #             247
            #         ],
            #         "radius": 103
            #     },
            #     "zeroPoint": [
            #         1096,
            #         159
            #     ],
            #     "startPoint": [
            #         1072,
            #         297
            #     ],
            #     "range": [
            #         -30,
            #         50
            #     ],
            #     "colorSeries": "Black"
            # }
        ]})


    sup: AlgorithmBase
    sup = load_algorithm_super(algorithm_name, inputs)
    target_instance = sup.determine_algorithm_instance(settings, algorithm_index)

    assert target_instance is not None
    ret, val, points, alarms = target_instance.perform(settings)
    print(f"结果 {ret, val, points, alarms}")

    from fastapi import status
    from fastapi.responses import JSONResponse
    data =     {
                #'isSuccess': ret,
                #'outputValue': val,
                'outputValuePosition': points
                #'alarms': alarmspoints
            }
    JSONResponse(
        status_code=status.HTTP_200_OK,
        content={
            'data': data,
        }
    )
    outputs = AlgorithmOutput(ret, val, points, alarms)
    print(f"outputs {str(outputs)}")

    # 可选操作
    ret_images = target_instance.gen_result_img()
    if ret_images is not None:
        for idx, img in enumerate(ret_images):
            cv2.imwrite(os.getcwd() + f"/meter_result_{idx}.jpg", img)
    return outputs


def load_algorithm_super(algorithm_name, inputs: AlgorithmInput):
    # module_ = importlib.import_module(f".{algorithm_name}.ab_{algorithm_name}_primary", ALGORITHM_CV_USER_DEFINE_PATH)
    # clazz_ = getattr(module_, f"{clazz_name_prefix}{AlgorithmClassifyEnum.Primary.name}")
    # liquid_reco: AlgorithmBase = clazz_(inputs, 1)

    # lcase = instance.classify.name.lower()
    # self._name  算法块唯一标识名,通常需与算法块文件夹名称一致，比如 liquid_level
    module_name = f".{algorithm_name}.ab_{algorithm_name}"
    target_clazz_name = f"{to_camel_case(algorithm_name, '_')}Reader"

    try:
        M = importlib.import_module(module_name, ALGORITHM_CV_SYSTEM_INNER_PATH)
        print('#'*50, M, target_clazz_name)
        target_clazz_ = getattr(M, target_clazz_name)
    except ModuleNotFoundError:
        raise InspectionException(
            f"Auto-load module '{module_name}' from '{ALGORITHM_CV_SYSTEM_INNER_PATH}' failure, please check it manually")

    algorithm_instance: AlgorithmBase = target_clazz_(inputs, -1)
    return algorithm_instance


@pytest.fixture
def primary_detect():
    algorithm_index = 0
    classify = AlgorithmClassifyEnum.Primary
    strategy = AlgorithmStrategyEnum.Main
    output_type = AlgorithmOutputTypeEnum.AlarmLevel

    # 报警条件（可选）
    #alarm_rule = ExceedLimitAlarmRule(47, 17, -1, -15)
    return do_itself(algorithm_index, classify, strategy, output_type)


# @pytest.fixture
# def second_detect():
#     algorithm_index = 1
#     classify = AlgorithmClassifyEnum.Secondary
#     strategy = AlgorithmStrategyEnum.Main
#     output_type = AlgorithmOutputTypeEnum.AlarmLevel

#     # 报警条件（可选）
#     #alarm_rule = ExceedLimitAlarmRule(47, 17, -1, -15)
#     return do_itself(algorithm_index, classify, strategy, output_type)


@pytest.fixture
def algorithm_detail():
    def func(o):
        # if isinstance(o, ValueType):
        #     return [k for k, v in TYPING_MAP.items() if v == o][0]
        return o.__dict__

    algorithm_name = "liquid_level"
    # algorithm_name = "indicator_flash"
    sup: AlgorithmBase
    sup = load_algorithm_super(algorithm_name, AlgorithmInput())
    algorithm_detail = sup.get_algorithm_detail('system_inner')
    strs = json.dumps(algorithm_detail, default=func, sort_keys=True, indent=2, ensure_ascii=False)
    #print(strs)
    return strs


def test_primary(primary_detect):
    assert primary_detect.ret is True


# def test_second(second_detect):
#     assert second_detect.ret is True


def test_get_detail(algorithm_detail):
    assert algorithm_detail is not None
