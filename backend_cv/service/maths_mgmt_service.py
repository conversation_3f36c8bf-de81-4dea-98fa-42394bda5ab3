import importlib
import os
import tempfile

from loguru import logger
from backend_common.constants.math_constants import ALGORITHM_CV_USER_DEFINE_PATH, ALGORITHM_CV_SYSTEM_INNER_PATH
from backend_common.exceptions.inspection_exception import InspectionEx<PERSON>, AlgorithmCheckException, \
    AlgorithmProcessException
from backend_common.maths.algorithm.base.algorithm_base import AlgorithmInput, AlgorithmBase
from backend_common.utils.util import remove_quietly
from backend_common.maths.algorithm.base.parameter_check import ParameterCheck
from backend_common.utils import MinioUtil
import shutil
import sys


def to_camel_case(string, spliter):
    components = string.split(spliter)
    # Capitalize the first letter of each word except the first one if index > 0 else word
    components = [word.capitalize() for index, word in enumerate(components)]
    # Join the components together
    camel_case_string = ''.join(components)
    return camel_case_string


def load_algorithm_super(algorithm_name, suer_or_system, inputs: AlgorithmInput):
    # 算法块唯一标识名,通常需与算法块文件夹名称一致，比如 liquid_level
    module_name = f".{algorithm_name}.ab_{algorithm_name}"
    target_clazz_name = f"{to_camel_case(algorithm_name, '_')}Reader"

    base_path = ALGORITHM_CV_SYSTEM_INNER_PATH
    if suer_or_system == "user_define":
        base_path = ALGORITHM_CV_USER_DEFINE_PATH

    try:
        logger.info(f"algorithm_cv_base_path : {base_path}")
        logger.info(f"module_name : {module_name}")
        logger.info(f"target_clazz_name : {target_clazz_name}")

        M = importlib.import_module(module_name, base_path)
        # 找到对应的类
        target_clazz_ = getattr(M, target_clazz_name)
    except ModuleNotFoundError:
        raise InspectionException(
            f"Auto-load module '{module_name}' from '{base_path}' failure, please check it manually")

    # 类实例创建
    algorithm_instance: AlgorithmBase = target_clazz_(inputs)
    return algorithm_instance


class MathABMgmt:
    def __init__(self, request_param: dict):
        self.request_param = request_param
        self.param_check = ParameterCheck()

    def execute(self):
        """ 算法执行 """

        try:
            inputs = AlgorithmInput()

            images = self.request_param['images']

            # 现在不需要Minio下载图片到本地路径
            # new_image_arr = []
            # for img_path in images:
            #     # 下载图片到本地
            #     local_path = MinioUtil.download(img_path)
            #     new_image_arr.append(local_path)
            inputs['images'] = images

            roi = self.request_param['roi']
            inputs['roi'] = roi

            inputParam = self.request_param['inputParam']
            inputs['inputParam'] = inputParam

            method_code = self.request_param['code']
            user_or_system = self.request_param['userOrSystem']

            # 输入参数校验
            self.param_check.do_check_param(inputs)

            # 加载算法类
            custom_algorithm_instance = load_algorithm_super(method_code, user_or_system, inputs)

            assert custom_algorithm_instance is not None, "无法加载CV算法实例!"

            # 算法执行
            isSuccess, output, osdInfo = custom_algorithm_instance.perform()

            return {
                'isSuccess': isSuccess,
                'output': output,
                'osdInfo': osdInfo
            }

        except ModuleNotFoundError as e:
            raise InspectionException(message=e.message)
        except InspectionException as e:
            raise InspectionException(message=e.message)
        except AlgorithmCheckException as e:
            raise AlgorithmCheckException(message=e.message)
        except AlgorithmProcessException as e:
            raise AlgorithmProcessException(message=e.message)
        except Exception as e:
            raise Exception(e.__str__())
        
    def block_import(self) -> (bool, str):
        """
        file: the uploaded wheel file
        :return:
            是否导入成功
        """
        file = self.request_param['file']
        # block_name = self.request_param['name']
        tmp_file = f"{tempfile.gettempdir()}/{file.filename}"

        # async with aiofiles.open(tmp_file, "wb") as buffer:
        #     while content := await file.read(1024):
        #         await buffer.write(content)
        writer = open(tmp_file, 'wb')
        writer.write(file.file.read())
        # Install the package using pip
        cmd = f"pip install --no-cache --upgrade {tmp_file} -t {ALGORITHM_CV_USER_DEFINE_PATH.replace('.', '/')}"
        # exit_code = subprocess.call(cmd)
        exit_code = os.system(cmd)

        remove_quietly(tmp_file)
        return (True, "import success") if exit_code == 0 else (False, "import failure")
        
    def block_remove(self) -> (bool, str):
        """
        删除指定的用户自定义算法包
        :return:
            (bool, str): 是否删除成功 和 提示信息
        """
        # 获取算法包名称
        block_name = self.request_param.get('code')

        # 构建算法包路径
        user_path = ALGORITHM_CV_USER_DEFINE_PATH.replace('.', '/')
        block_name_list = []
        for black in os.listdir(user_path):
            if block_name in black:
                block_name_list.append(black)

        if block_name_list == []:
            logger.info(f"算法文件 {block_name} 不存在")
            # return (False, f"模块 {block_name} 不存在")
        else:
            for block_dir in block_name_list:
                package_path = os.path.join(user_path, block_dir)
                shutil.rmtree(package_path)
                logger.info(f"算法文件 {block_name} 删除成功")
        try:
            # 1. 查找所有缓存中与该模块相关的子模块
            sub_modules = [k for k in sys.modules.keys() if block_name in str(k)]
            # 2. 删除缓存中的子模块
            for sub_module in sub_modules:
                if sub_module in sys.modules:
                    logger.info(f"Removing module: {sub_module}")
                    del sys.modules[sub_module]
            # 删除模块目录
            logger.info(f"模块 {block_name} 删除成功")
            return (True, f"模块 {block_name} 删除成功")
        except Exception as e:
            return (False, f"删除失败: {str(e)}")
