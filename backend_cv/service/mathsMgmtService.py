import importlib
import json
import os
import tempfile

from backend_common.constants.alarm import ExceedLimitAlarmRule
from backend_common.constants.constant import AlgorithmClassifyEnum, AlgorithmStrategyEnum
from backend_common.constants.math_constants import ALGORITHM_CV_USER_DEFINE_PATH, ALGORITHM_CV_SYSTEM_INNER_PATH
from backend_common.exceptions.inspection_exception import InspectionException, AlgorithmCheckException, AlgorithmProcessException
from backend_common.maths.algorithm.base.algorithm_base import AlgorithmInput, AlgorithmBase, AlgorithmOutput, \
    AlgorithmSettings
from backend_common.utils.util import remove_quietly


def to_camel_case(string, spliter):
    components = string.split(spliter)
    # Capitalize the first letter of each word except the first one if index > 0 else word
    components = [word.capitalize() for index, word in enumerate(components)]
    # Join the components together
    camel_case_string = ''.join(components)
    return camel_case_string


def load_algorithm_super(algorithm_name, suer_or_system, inputs: AlgorithmInput):
    # self._name  算法块唯一标识名,通常需与算法块文件夹名称一致，比如 liquid_level
    module_name = f".{algorithm_name}.ab_{algorithm_name}"
    target_clazz_name = f"{to_camel_case(algorithm_name, '_')}Reader"

    base_path = ALGORITHM_CV_SYSTEM_INNER_PATH
    if suer_or_system == "user_define":
        base_path = ALGORITHM_CV_USER_DEFINE_PATH

    try:
        print("ALGORITHM_CV_USER_DEFINE_PATH", base_path)
        print("module_name", module_name)
        print("target_clazz_name", target_clazz_name)
        M = importlib.import_module(module_name, base_path)
        target_clazz_ = getattr(M, target_clazz_name)
    except ModuleNotFoundError:
        raise InspectionException(
            f"Auto-load module '{module_name}' from '{base_path}' failure, please check it manually")

    algorithm_instance: AlgorithmBase = target_clazz_(inputs, -1)
    return algorithm_instance


class MathABMgmt:
    def __init__(self, request_param: dict):
        self.request_param = request_param

    def execute(self):
        """ 算法执行 """
        try:
            input_param = self.request_param['inputParam']
            inputs = AlgorithmInput(input_param)

            roi = self.request_param['roi']
            inputs['roi'] = roi

            images = self.request_param['images']
            inputs['images'] = images

            scene_image = self.request_param['sceneImage']
            inputs['targetSceneImage'] = {'uri': scene_image}

            classify = AlgorithmClassifyEnum.from_str(self.request_param['classify'])
            strategy = AlgorithmStrategyEnum.from_str(self.request_param['strategy'])

            alarm_rules = []
            alarm_rules_info = self.request_param['alarmRulesInfo']
            if alarm_rules_info is not None:
                # alarm_rules_info = eval(alarm_rules_info)
                for alarm_rule_info in alarm_rules_info:
                    hh_info = alarm_rule_info["HH"]
                    h_info = alarm_rule_info["H"]
                    l_info = alarm_rule_info["L"]
                    ll_info = alarm_rule_info["LL"]
                    alarm_rule = ExceedLimitAlarmRule(
                        hh_info["val"], h_info["val"], l_info["val"], ll_info["val"],
                        hh_info["desc"], h_info["desc"], l_info["desc"], ll_info["desc"],
                        hh_info["isBind"], h_info["isBind"], l_info["isBind"], ll_info["isBind"]
                    )
                    alarm_rules.append(alarm_rule)

            settings = AlgorithmSettings(classify, strategy, alarm_rules)
            sup: AlgorithmBase
            method_code = self.request_param['code']
            suer_or_system = self.request_param['userOrSystem']
            # opencv 算法当前看不需要热加载,每次执行时现load即可
            sup = load_algorithm_super(method_code, suer_or_system, inputs)
            algorithm_index = self.request_param['algorithmIndex']

            target_instance = sup.determine_algorithm_instance(settings, algorithm_index, suer_or_system)

            assert target_instance is not None, "无法加载CV算法实例!!!"
            # 算法执行
            ret, val, points, alarms = target_instance.perform(settings)

            outputs = AlgorithmOutput(ret, val, points, alarms)
            print(f"outputs {str(outputs)}")

            return {
                'isSuccess': ret,
                'outputValue': val,
                'outputValuePosition': points,
                'alarms': alarms
            }
        # except ModuleNotFoundError as e:
        #     raise InspectionException(message=e.msg)
        # except Exception as e:
        #     raise InspectionException(e.__str__())


        except ModuleNotFoundError as e:
            raise InspectionException(message=e.message)
        except InspectionException as e:
            raise InspectionException(message=e.message)
        except AlgorithmCheckException as e:
            raise AlgorithmCheckException(message=e.message)
        except AlgorithmProcessException as e:
            raise AlgorithmProcessException(message=e.message)
        except Exception as e:
            raise Exception(e.__str__())

    def get_detail(self):
        """ """
        code = self.request_param['code']
        a_type = self.request_param['userOrSystem']
        sup: AlgorithmBase
        sup = load_algorithm_super(code, a_type, AlgorithmInput())
        algorithm_detail = sup.get_algorithm_detail(a_type)

        ret = json.dumps(algorithm_detail, default=lambda o: o.__dict__, sort_keys=True, ensure_ascii=False)
        # logger.debug(f"algorithm block detail of {code} :: \r\n {ret}")

        return json.loads(ret)

    def block_import(self) -> (bool, str):
        """
        file: the uploaded wheel file
        :return:
            是否导入成功
        """
        file = self.request_param['file']
        # block_name = self.request_param['name']
        tmp_file = f"{tempfile.gettempdir()}/{file.filename}"

        # async with aiofiles.open(tmp_file, "wb") as buffer:
        #     while content := await file.read(1024):
        #         await buffer.write(content)
        writer = open(tmp_file, 'wb')
        writer.write(file.file.read())
        # Install the package using pip
        cmd = f"pip install --no-cache --upgrade {tmp_file} -t {ALGORITHM_CV_USER_DEFINE_PATH.replace('.', '/')}"
        # exit_code = subprocess.call(cmd)
        exit_code = os.system(cmd)

        remove_quietly(tmp_file)

        return (True, "import success") if exit_code == 0 else (False, "import failure")
