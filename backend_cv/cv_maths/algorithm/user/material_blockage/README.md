## 物料堵塞识别算法V1.0.0

### 描述

该算法用于识别矩形ROI区域中（输入参数条件下）是否存在物料堵塞

- 是否配置ROI：否

- 是否配置多图输入：是

### 输入参数

| 参数名     | 是否绘制              | 参数类型     | 默认值 | 是否必填 | 数据范围   | 精度   | 描述                                            |
| ------- | ----------------- | -------- | --- | ---- | ------ | ---- | --------------------------------------------- |
| ROI区域    | 是                   | SQUARE   | -      | 否       | -        | -    | 矩形ROI区域                                                  |
| 差分面积    |否                   |  INTEGER     |200     |是        |0-3000    | 1     |前后两张图片差分面积阈值，小于此面积则认为前后两张图片此区域无变化|
| 差分数量    |否                   |  INTEGER     |2     |是        |0-10    | 1     | 前后两张图片差分区域数量阈值，小于此阈值则认为前后两张图片静止，已堵塞|
| 结果描述    | 是                 | STRING   | -   | 否    | -      | -    | 结果描述文字                                              |

### 输出参数

| 参数名         | 参数类型   | 描述               |
| ----------- | ------ | ---------------- |
| 物料堵塞识别结果 | SRTING | 输出值为"检测到堵塞"或"未检测到堵塞"|
| 物料堵塞识别结果布尔值 | BOOLEAN | 输出值为"检测到堵塞":True或"未检测到堵塞":False|

### 结果展示

| 名称     | 描述                  |
| ------ | ------------------- |
| 物料堵塞识别结果   | "检测到堵塞"或"未检测到堵塞"    |
| 物料堵塞识别结果矩形框|矩形，代表识别出的物料堵塞区域 |