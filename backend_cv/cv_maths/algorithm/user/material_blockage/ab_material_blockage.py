import cv2
import numpy as np

from loguru import logger
from backend_common.exceptions.inspection_exception import AlgorithmProcessException
from backend_common.maths.algorithm.base.algorithm_base import AlgorithmInput, AlgorithmBase
from backend_common.utils.util import RectUtils


class MaterialBlockageReader(AlgorithmBase):

    _name = "物料堵塞识别算法"

    def __init__(self, _inputs: AlgorithmInput):
        super(MaterialBlockageReader, self).__init__(self.__class__._name)

        if _inputs.__eq__(dict()):
            return

        self._local_var._inputs = _inputs
        # 获取图片
        self.origin_imgs = self._get_input_images()
        if len(self.origin_imgs) < 2:
            raise AlgorithmProcessException("请输入多张图片")
        self.h, self.w = self.origin_imgs[0].shape[:2]
        # 获取参数字典
        input_param = self._get_input_param()
        # 算法输入ROI参数获取
        input_roi = input_param["input_roi"]
        if input_roi is None:
            self.input_roi = None
        else:
            self.input_roi = [[coor['x'], coor['y']] for coor in input_roi]

        # 算法输入参数获取
        self.area_thresh = input_param["area_thresh"]
        self.num_thresh = input_param["num_thresh"]
        self.result_des = input_param["resultDes"]

    def get_roi(self, ori_image):

        if self.input_roi is None:
            roi_img = ori_image
        else:
            roi = np.array(self.input_roi).astype('int32')
            xmin, ymin = np.min(roi[:, 0]), np.min(roi[:, 1])
            xmax, ymax = np.max(roi[:, 0]), np.max(roi[:, 1])
            roi_img = ori_image[ymin:ymax, xmin:xmax]  # [ymin:ymax, xmin:xmax]
        return roi_img

    def staticFrame(self, lastFrame, frame):
        # 计算当前帧和前帧的不同
        frameDelta = cv2.absdiff(lastFrame, frame)
        # 结果转为灰度图
        thresh = cv2.cvtColor(frameDelta, cv2.COLOR_BGR2GRAY)
        # 图像二值化
        thresh = cv2.threshold(thresh, 25, 255, cv2.THRESH_BINARY)[1]
        # 阀值图像上的轮廓位置
        cnts, hierarchy = cv2.findContours(thresh.copy(), cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        # 遍历轮廓
        num_cnts = 0
        for c in cnts:
            # 忽略小轮廓，排除误差
            if cv2.contourArea(c) < self.area_thresh:
                continue
            # 计算轮廓的边界框，在当前帧中画出该框
            (x, y, w, h) = cv2.boundingRect(c)
            num_cnts += 1
        if num_cnts < self.num_thresh:
            return True
        else:
            return False

    def _do_detect(self):
        """
        调用入口函数
        """
        res_name = "检测到堵塞"
        res_name_bool = True
        for i in range(1, len(self.origin_imgs)):
            lastFrame = self.origin_imgs[i-1]
            frame = self.origin_imgs[i]
            if self.input_roi is None:
                logger.info("ROI is empty, input the entire image")
            else:
                lastFrame = self.get_roi(lastFrame)
                frame = self.get_roi(frame)
            static = self.staticFrame(lastFrame, frame)
            if not static:
                res_name = "未检测到堵塞"
                res_name_bool = False
                break
        osdinfo = []
        if self.result_des is None:
            output = {"value": res_name}
        else:
            output = {"value": res_name, "resultDes": self.result_des}
        output_bool = {"value": res_name_bool}
        if self.input_roi is None:
            coord = RectUtils.ltrb_to_4_points_dict([int(self.w / 4), int(self.h / 4)], [int(self.w * 3 / 4), int(self.h * 3 / 4)])
        else:
            coord = RectUtils.ltrb_to_4_points_dict(self.input_roi[0], self.input_roi[2])
        osdinfo.append({"dataType": "SQUARE", "textObj": output, "coords": coord})

        return True, {"material_blockage": output, "material_blockageBoolean": output_bool}, osdinfo
