{"algorithmMetadata": {"name": "物料堵塞识别", "code": "material_blockage", "version": "1.0.0", "description": "物料堵塞识别.V1", "type": "openCV", "isBatchProcessing": true, "roiDrawType": "SQUARE", "roiRequired": false, "classification": "OTHERS"}, "input": [{"key": "input_roi", "label": "ROI区域", "dataType": "SQUARE", "constraints": {"required": false}, "drawToOsd": true}, {"key": "area_thresh", "label": "差分面积", "dataType": "INTEGER", "defaultValue": 200, "constraints": {"min": 0, "max": 3000, "precision": 10, "required": true}, "drawToOsd": false}, {"key": "num_thresh", "label": "差分数量", "dataType": "INTEGER", "defaultValue": 2, "constraints": {"min": 0, "max": 10, "precision": 1, "required": true}, "drawToOsd": false}, {"key": "resultDes", "label": "结果描述", "dataType": "STRING", "value": "", "constraints": {"required": false}, "drawToOsd": true}], "output": [{"key": "material_blockage", "label": "物料堵塞识别结果", "dataType": "STRING"}, {"key": "material_blockageBoolean", "label": "物料堵塞识别布尔结果", "dataType": "BOOLEAN"}]}