## 液体浑浊度识别算法V1.0.0

### 描述

 该算法用于识别圆形管道窗中液体颜色的浑浊度

- 是否配置ROI：否

- 是否配置多图输入：否

### 输入参数

| 参数名     | 是否绘制 | 参数类型     | 默认值 | 是否必填 | 数据范围 | 精度  | 描述            |
| ------- | ---- | -------- | --- | ---- | ---- | --- | ------------- |
| 圆心坐标及半径 | 是    | CIRCLE   | -   | 是    | -    | -   | 圆形ROI区域       |
| 识别颜色    | 否    | SELECTOR | -   | 是    | 单选框  | -   | 单选框，选择需要检测的颜色 |
| 结果描述    | 是    | STRING   | -   | 否    | -    | -   | 结果描述文字        |

### 输出参数

| 参数名   | 参数类型    | 描述                           |
| ----- | ------- | ---------------------------- |
| 浑浊度结果 | INTEGER | -1：代表未检测到颜色， 0-756之间的值代表浑浊程度 |

### 结果展示

| 名称        | 描述                                        |
| --------- | ----------------------------------------- |
| 圆形ROI外接矩形 | 矩形框                                       |
| 浑浊度结果     | 整数值，范围-1-756，-1：代表未检测到颜色， 0-756之间的值代表浑浊程度 |