{"algorithmMetadata": {"name": "液体浑浊度", "code": "liquid_turbid", "version": "1.0.0", "description": "液体浑浊度.V1", "type": "openCV", "isBatchProcessing": false, "roiDrawType": "SQUARE", "roiRequired": false, "classification": "OTHERS"}, "input": [{"key": "circle_roi", "label": "圆心坐标及半径", "dataType": "CIRCLE", "constraints": {"required": true}, "drawToOsd": true}, {"key": "color_select", "label": "检测颜色", "dataType": "SELECTOR", "constraints": {"required": true, "maxLength": 1, "options": [{"key": 0, "label": "红色"}, {"key": 2, "label": "绿色"}, {"key": 3, "label": "蓝色"}, {"key": 4, "label": "橙黄色"}, {"key": 5, "label": "青色"}, {"key": 6, "label": "紫色"}, {"key": 7, "label": "黑色"}, {"key": 8, "label": "白色"}, {"key": 9, "label": "灰色"}]}, "drawToOsd": false}, {"key": "resultDes", "label": "结果描述", "dataType": "STRING", "value": "", "constraints": {"required": false}, "drawToOsd": true}], "output": [{"key": "liquid_turbid", "label": "浑浊度结果", "dataType": "INTEGER"}]}