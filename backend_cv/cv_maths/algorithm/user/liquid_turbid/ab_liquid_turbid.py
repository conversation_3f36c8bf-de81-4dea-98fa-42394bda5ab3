import cv2
import numpy as np

from loguru import logger
from backend_common.maths.algorithm.base.algorithm_base import AlgorithmInput, AlgorithmBase
from backend_common.constants.constant import LOWER, UPPER
from backend_common.utils.util import circle2bbox


class LiquidTurbidReader(AlgorithmBase):
    """
    液体浑浊度识别算法
    """
    _name = "liquid_turbid"

    def __init__(self, _inputs: AlgorithmInput):
        super(LiquidTurbidReader, self).__init__(self.__class__._name)

        if _inputs.__eq__(dict()):
            return

        self._local_var._inputs = _inputs
        self.origin_imgs = self._get_input_images(first_only=True)

        # 算法输入参数
        input_param = self._get_input_param()

        self.circle_roi = input_param["circle_roi"]
        self.color_select = input_param["color_select"][0]
        self.result_des = input_param["resultDes"]

    def get_circle_roi(self, img, center_coordinates, radius):
        # 黑色的背景图像
        circle = np.zeros(img.shape, dtype="uint8")

        # 背景黑色，白色圆形mask
        circle_mask = cv2.circle(circle, center_coordinates, radius, (255, 255, 255), -1)

        # 获取圆形ROI原始图像
        black_img = cv2.bitwise_and(img, circle_mask)

        x = center_coordinates[0] - radius
        y = center_coordinates[1] - radius
        w = radius * 2
        h = radius * 2

        # 获取圆外接矩形区域
        roi_img = black_img[y:y + h, x:x + w]
        return roi_img

    def get_turbid(self, roi_img):
        roi_hsv_img = cv2.cvtColor(roi_img, cv2.COLOR_BGR2HSV)

        color_mask = cv2.inRange(roi_hsv_img, LOWER[self.color_index], UPPER[self.color_index])

        if self.color_index == 0:
            color_mask_red = cv2.inRange(roi_hsv_img, LOWER[1], UPPER[1])
            color_mask = cv2.add(color_mask, color_mask_red)

        color_mask_number = len(color_mask[color_mask == 255])
        roi_area = 3.14 * int(self.radius) ** 2

        area_ratio = color_mask_number / int(roi_area)

        # 未检测到颜色区域
        if color_mask_number == 0:
            logger.info("浑浊度算法：未检测到颜色区域")
            return -1

        roi_h_value = roi_hsv_img[:, :, 0][color_mask == 255].mean()
        roi_s_value = roi_hsv_img[:, :, 1][color_mask == 255].mean()
        roi_v_value = roi_hsv_img[:, :, 2][color_mask == 255].mean()

        hsv_value = roi_h_value + roi_s_value + roi_v_value
        tubid_value = int(hsv_value * area_ratio)
        return tubid_value

    def det_turbid(self, img, roi_square, color_select):
        # "center": {"x": 958, "y": 457}
        center = roi_square['center']
        center = (center["x"], center["y"])

        self.radius = int(roi_square['radius'])
        self.color_index = int(color_select)

        roi_img = self.get_circle_roi(img, center, self.radius)

        tubid = self.get_turbid(roi_img)
        return tubid

    def _do_detect(self):
        """
        调用入口函数
        return:
            ret: 检测是否成功
            val: 数值化结果（颜色浑浊度）
            points: 绘制输入圆形
        """
        res = self.det_turbid(self.origin_imgs, self.circle_roi, self.color_select)

        if self.result_des is None:
            output = {"value": res}
        else:
            output = {"value": res, "resultDes": self.result_des}

        osdinfo = [{"dataType": "SQUARE", "textObj": output, "coords": circle2bbox(self.circle_roi)}]

        return True, {"liquid_turbid": output}, osdinfo
