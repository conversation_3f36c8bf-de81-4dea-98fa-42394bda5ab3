## 液位读数识别算法V1.0.0

### 描述

 该算法用于液位读数识别，支持工厂以及实验室大多数液位识别，液体颜色参数建议选取图片中液柱内大多数相同的颜色，颜色偏差不建议调整。
 - 是否配置ROI：否
- 是否配置多图输入：否
- 算法一 基于颜色的液位识别主策略 
- 算法二 基于连通域的液位识别主策略

### 输入参数

| 参数名     | 是否绘制 | 参数类型    | 默认值 | 是否必填 | 数据范围   | 精度   |描述
|-------|--------------|----------------------|--------------|----------------------|--------------|----------------------|----------------------|
| 液柱区域 | 是|SQUARE |-|是|-|-| 矩形， item为4个point，具体表现为<br>用矩形框代表液柱区域 |
| 液体颜色 |  否|RGB | [0, 0, 0] |是|0-255|1|取色笔，提取液柱的颜色|
| 颜色偏差 | 否|INTEGER | 55|是|0-255|1|数值，指的是液柱颜色可变幅度 |
| 量程 | 否|RANGE | -|是|-999999-999999|0.001|液位仪器的量程 |
| 结果精度 |否| INTEGER|2|否|0-5|1| 结果保留小数点后几位|
| 单位 | 否|STRING|-|否|-|-| 结果识别单位，如ml|
| 结果描述 | 是|STRING| -|否|-|-|结果描述文字，例如主油箱液位读数|

### 输出参数

| 参数名         | 参数类型   | 描述               |
| ----------- | ------ | ---------------- |
|luquidLevel | FLOAT | 液位读数数值 |
|resultPrecision|INTEGER|结果保留小数点后几位|
|unit|STRING|结果识别单位，如ml|
|resultDes|STRING|结果描述文字，例如主油箱液位读数|

### 结果展示

| 名称     | 描述                  |
| ------ | ------------------- |
| 液位位置 |矩形，代表识别出的液位区域 |
| 液位读数 |数值，液位的读数识别，例如液位读数识别为75.0 |
