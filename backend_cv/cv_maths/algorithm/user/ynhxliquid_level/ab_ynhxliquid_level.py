import traceback
from typing import Any

import numpy as np
from loguru import logger

from backend_common.exceptions.inspection_exception import AlgorithmProcessException
from backend_common.maths.algorithm.base.algorithm_base import AlgorithmInput, AlgorithmBase

# 是否输出中间过程图片
WRITE_PROCESS_IMAGE = False
# 是否show中间过程图片
SHOW_PROCESS_IMAGE = False
# 透视变化阈值角度
# theta = 15
# THRESHOLD = math.tan(theta * math.pi / 180)
# 最小量程精度百分比
MIN_PRECISION_PERCENT = 0.01

SUFFIX_START = "sub"


class YnhxliquidLevelReader(AlgorithmBase):
    """
    液位识别
    """
    _name = "ynhxliquid_level"

    def __init__(self, _inputs: AlgorithmInput):
        super(YnhxliquidLevelReader, self).__init__(self.__class__._name)

        if _inputs.__eq__(dict()):
            return

        self._local_var._inputs = _inputs
        # 算法实例参数
        self._local_var._settings = None
        self.originImg = self._get_input_images()[0]
        self.roi = self._get_roi()
        input_param = self._get_input_param()


        # self.originImg = self._get_input_images(first_only=True)
        self.rangeDown = input_param["range"]['start']
        self.rangeUp = input_param["range"]['end']
        self.liquid_color_rgb = input_param["colorPicker"]
        self.colorThreshold = input_param["colorThreshold"]
        self.liquid_roi = np.array([list(k.values()) for k in input_param["liquidColumn"]])

        self.resultScale = int(input_param["resultPrecision"])
        self.resultDes = input_param['resultDes']
        self.unit = input_param['unit']

        self.columnLeftTop = self.liquid_roi[0]
        self.columnLeftBottom = self.liquid_roi[1]
        self.columnRightBottom = self.liquid_roi[2]
        self.columnRightTop = self.liquid_roi[3]

    def _do_detect(self) -> (bool, Any, tuple):
        """
        核心检测算法
        return:
            ret: 检测是否成功
            val: 数值化结果(如果有,若没有留空)
            points: 检测到的关键结果点集(具体每个点含义由各算法自行约定)

        """
        from cv_maths.algorithm.user.ynhxliquid_level.ab_ynhxliquid_level_sub0 import YnhxliquidLevelReaderSub0
        from cv_maths.algorithm.user.ynhxliquid_level.ab_ynhxliquid_level_sub1 import YnhxliquidLevelReaderSub1
        try:
            isSuccess, output, osdInfo = YnhxliquidLevelReaderSub0()._do_detect(self.originImg, self.liquid_roi, self.liquid_color_rgb, self.colorThreshold, self.columnLeftTop, \
                                         self.columnRightTop, self.columnLeftBottom, self.columnRightBottom, self.rangeDown, self.rangeUp, self.resultDes, self.unit, self.resultScale)
            logger.info('Algorithm 1 executed successfully')
        except (AlgorithmProcessException, Exception) as Algorithm1e:
            tb = traceback.extract_tb(Algorithm1e.__traceback__)
            filename, lineno, funcname, text = tb[-1]
            logger.error(f"Error in file {filename}, line {lineno}, in {funcname}: {text}")
            try:
                isSuccess, output, osdInfo = YnhxliquidLevelReaderSub1()._do_detect(self.originImg, self.liquid_roi, self.columnLeftTop, self.columnLeftBottom, \
                                            self.columnRightBottom, self.columnRightTop, self.rangeDown, self.rangeUp, self.liquid_color_rgb, self.colorThreshold, self.resultDes, self.unit, self.resultScale)
                logger.info(f'Algorithm 2 executed successfully')
            except Exception as Algorithm2e:
                # logger.info(f"算法检测异常,")
                tb = traceback.extract_tb(Algorithm2e.__traceback__)
                filename, lineno, funcname, text = tb[-1]
                logger.error(f"Error in file {filename}, line {lineno}, in {funcname}: {text}")
                if isinstance(Algorithm1e, AlgorithmProcessException):
                    raise AlgorithmProcessException(Algorithm1e.__str__())
                else:
                    raise Exception(Algorithm1e.__str__())
                # return False, None, None
        return isSuccess, output, osdInfo
