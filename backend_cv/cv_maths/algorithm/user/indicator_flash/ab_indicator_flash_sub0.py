from typing import Any

import cv2
import math

from loguru import logger
from backend_common.utils.util import get_mask_by_color, img_cut_check
from cv_maths.algorithm.user.indicator_flash.ab_indicator_flash import IndicatorStatus


class IndicatorFlashReaderSub0():
    """
    指示灯闪烁识别1
    """

    def _do_detect(self, param_dict, img):
        """
        核心检测算法
        return:
            isSuccess: 检测是否成功
            output: 数值化结果(如果有,若没有留空)
            osdInfo: 需要绘制到osd上面的参数(具体由各算法自行约定)

        """
        cir = param_dict['circle']
        color_rgb = param_dict['colorPicker']
        colorThreshold = int(param_dict['colorThreshold'])
        threshold = float(param_dict['threshold'])
        resultDes = param_dict['resultDes']
        result = self.get_values(img, cir, color_rgb, colorThreshold, threshold)
        return result

    def _postprocess(self) -> Any:
        pass

    def get_values(self, img, cir, color_rgb, colorThreshold, threshold):
        """
        基于连通域分析的指示灯状态识别

        基于观察到的基本事实： 亮灯状态下最大连通域面积大于灭灯状态下最大连通域面积
        """
        roi = self._get_roi(img, cir)
        binary = get_mask_by_color(roi, color_rgb, colorThreshold, color_picker=True)
        # 连通区域分析
        # 连通性，可以取值为 4 或 8，表示分析时考虑像素的上下左右或上下左右和对角线方向上的连通关系。
        # stats 每个连通区域的统计信息，包括面积、左上角坐标、宽度、高度和中心点坐标等，格式为 numpy 数组，每一行对应一个连通区域。
        num_labels, labels, stats, _ = cv2.connectedComponentsWithStats(binary, connectivity=4, ltype=cv2.CV_32S)

        # 获取指示灯区域的统计信息
        max_area, (w, h) = 0, roi.shape[:2]
        # max_label = 0
        for i in range(1, num_labels):
            # 取其面积
            area = stats[i, cv2.CC_STAT_AREA]
            if area > max_area:
                max_area = area

        # magic_factor = threshold if threshold is not None else self.AREA_THRESHOLD
        status = "亮" if max_area / (w * h) >= threshold else "灭"
        logger.info(f"指示灯闪烁识别算法1: max_area=={max_area}, {w}*{h}={w * h}, 状态：{status}")

        return IndicatorStatus.On.value[1] if max_area / (w * h) >= threshold else IndicatorStatus.Off.value[1]

    def _get_roi(self, img, cir):
        """
        按圆心和半径抠出ROI区域, 取圆内接正方形
        :param img:  图片
        :param cir:  圆心坐标，半径
        :return:
        """
        x, y, r = cir['center']['x'], cir['center']['y'], cir['radius']

        offset = int(r / math.sqrt(2))
        # offset = r
        rect_x = (x - offset)
        rect_y = (y - offset)
        img_cut_check(img, x + offset, y + offset)
        part_img = img[rect_y:(y + offset), rect_x:(x + offset)]
        return part_img
