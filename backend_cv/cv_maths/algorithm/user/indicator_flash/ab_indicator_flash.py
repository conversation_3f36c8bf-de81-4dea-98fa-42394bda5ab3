
from enum import Enum
from backend_common.utils.util import circle_bounding_box
from backend_common.exceptions.inspection_exception import AlgorithmProcessException
from backend_common.maths.algorithm.base.algorithm_base import AlgorithmBase, AlgorithmInput
from loguru import logger
import traceback

SUFFIX_START = "sub"


class IndicatorStatus(Enum):
    # 常亮
    On = (1, '亮', 'on', True)
    # 常灭
    Off = (0, '灭', 'off', False)
    # 闪烁 TODO 暂不支持
    Flashing = (2, '闪烁', 'flashing')
    # 未知
    Unknown = (3, '未知', 'unknown')

    @classmethod
    def from_str(cls, txt):
        for k, v in cls.__members__.items():
            if k.casefold() == txt.casefold():
                return v
        else:
            raise ValueError(f"'{cls.__name__}' enum not found for '{txt}'")

    @classmethod
    def value_of(cls, value):
        for k, v in cls.__members__.items():
            if v.value[0] == value:
                return v
        else:
            raise ValueError(f"'{cls.__name__}' enum not found for '{value}'")


class IndicatorFlashReader(AlgorithmBase):
    """
    指示灯闪烁识别
    """

    _name = "indicator_flash"  # 此名称同算法文件夹名

    def __init__(self, _inputs: AlgorithmInput, _id: int = -1):
        super(IndicatorFlashReader, self).__init__(self.__class__._name)
        if _inputs.__eq__(dict()):
            return

        self._local_var._inputs = _inputs
        # 算法实例参数
        self._local_var._settings = None
        self.originImgs = self._get_input_images()
        if len(self.originImgs) < 2:
            raise AlgorithmProcessException("请输入多张图片")
        self.roi = self._get_roi()
        self.input_param = self._get_input_param()
        self.resultDes = self.input_param['resultDes']
        self.cir = self.input_param['circle']

    def _do_detect(self):

        from cv_maths.algorithm.user.indicator_flash.ab_indicator_flash_sub0 import IndicatorFlashReaderSub0
        from cv_maths.algorithm.user.indicator_flash.ab_indicator_flash_sub1 import IndicatorFlashReaderSub1
        results = []
        for input_img in self.originImgs:
            try:
                result = IndicatorFlashReaderSub0()._do_detect(self.input_param, input_img)
                results.append(result)
                logger.info('Algorithm 1 executed successfully')
            except (AlgorithmProcessException, Exception) as Algorithm1e:
                tb = traceback.extract_tb(Algorithm1e.__traceback__)
                filename, lineno, funcname, text = tb[-1]
                logger.error(f"Error in file {filename}, line {lineno}, in {funcname}: {text}")
                try:
                    result = IndicatorFlashReaderSub1()._do_detect(self.input_param, input_img)
                    results.append(result)
                    logger.info(f'Algorithm 2 executed successfully')
                except Exception as e:
                    # logger.info(f"算法检测异常,")
                    tb = traceback.extract_tb(e.__traceback__)
                    filename, lineno, funcname, text = tb[-1]
                    logger.error(f"Error in file {filename}, line {lineno}, in {funcname}: {text}")
                    if isinstance(Algorithm1e, AlgorithmProcessException):
                        raise AlgorithmProcessException(Algorithm1e.__str__())
                    else:
                        raise Exception(Algorithm1e.__str__())
        logger.info("output is {}".format(results))
        results = list(set(results))
        if len(results) > 1:
            res = "闪烁"
            resBoolean = True
        else:
            res = "未闪烁"
            resBoolean = False
        if self.resultDes is not None:
            output = {"value":res, "resultDes": self.resultDes}
        else:
            output = {"value":res}
        outputBoolean = {"value": resBoolean}
        return True, {"indicatorFlash": output, "indicatorFlashBoolean": outputBoolean}, [{"dataType": "SQUARE","textObj": output,"coords": circle_bounding_box(self.cir)}]
