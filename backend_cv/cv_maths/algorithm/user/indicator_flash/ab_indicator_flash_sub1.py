from typing import Any

import cv2
import math

from loguru import logger
from backend_common.utils.util import get_mask_by_color, img_cut_check
from cv_maths.algorithm.user.indicator_flash.ab_indicator_flash import IndicatorStatus


class IndicatorFlashReaderSub1():
    """
    指示灯闪烁识别2
    """

    def _do_detect(self, param_dict, img) -> (bool, Any, tuple):
        """
        核心检测算法
        return:
            isSuccess: 检测是否成功
            output: 数值化结果(如果有,若没有留空)
            osdInfo: 需要绘制到osd上面的参数(具体由各算法自行约定)

        """
        cir = param_dict['circle']
        color_rgb = param_dict['colorPicker']
        colorThreshold = int(param_dict['colorThreshold'])
        threshold = float(param_dict['threshold'])
        # resultDes = param_dict['resultDes']
        result = self.get_values(img, cir, color_rgb, colorThreshold, threshold)
        return result

    def _postprocess(self) -> Any:
        pass

    def get_values(self, img, cir, color_rgb, colorThreshold=55, threshold=0.42):
        """
        单个指示灯状态识别,
        基于非零像素点统计的识别

        :param param_dict: 指示灯圆心坐标和半径，颜色
        :param img:  单张图片
        :param threshold 阈值灵敏度
        :return: 判断当前灯运行状态
        """
        # 要对比的图片
        #  指示灯 初始用户配置信息
        roi = self._get_roi(img, cir)
        mask = get_mask_by_color(roi, color_rgb, colorThreshold, color_picker=True)
        count = cv2.countNonZero(mask)
        w, h, _ = roi.shape
        # 非零点占全roi比率
        ratio = count / (w * h)

        status = "亮" if ratio >= threshold else "灭"
        logger.info(f"指示灯闪烁识别2算法2: count=={count}, {w}*{h}={w * h}, 状态：{status}")

        return IndicatorStatus.On.value[1] if ratio >= threshold else IndicatorStatus.Off.value[1]

    def _get_roi(self, img, cir):
        """
        按圆心和半径抠出ROI区域, 取圆内接正方形
        :param img:  图片
        :param cir:  圆心坐标，半径
        :return:
        """
        x, y, r = cir['center']['x'], cir['center']['y'], cir['radius']

        offset = int(r / math.sqrt(2))
        rect_x = (x - offset)
        rect_y = (y - offset)
        img_cut_check(img, x + offset, y + offset)
        part_img = img[rect_y:(y + offset), rect_x:(x + offset)]
        return part_img
