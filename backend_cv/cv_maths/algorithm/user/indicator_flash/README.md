## 指示灯闪烁识别算法V1.0.0

### 描述

该算法用于指示灯闪烁识别，支持工厂以及实验室大多数指示灯闪烁识别。指示灯颜色参数建议选取图片中灯亮后指示灯的颜色, 颜色偏差不建议调整。
- 是否配置ROI：否
- 是否配置多图输入：是
- 算法一 基于颜色值统计的指示灯闪烁识别策略 
- 算法二 基于连通域分析的指示灯闪烁识别策略

### 输入参数
| 参数名     | 是否绘制 | 参数类型    | 默认值 | 是否必填 | 数据范围   | 精度   |描述
|-------|--------------|----------------------|--------------|----------------------|--------------|----------------------|----------------------|
| 指示灯截取圆 |是| CIRCLE | - |是|-|-|圆，仪表包含指示灯在内的识别区域|
| 灯亮颜色 | 否|RGB | [0, 0, 0] |是|0-255|1|取色笔，指示灯亮的颜色|
| 颜色偏差 | 否|INTEGER | 55|是|0-255|1|数值，指的是指示灯颜色可变幅度 |
| 识别灵敏度 | 否|FLOAT |0.42|是|0-1|0.01| 0-1之间的数值， 指的是算法识别的灵敏度，<br>灵敏度越高识别准确率越低,可根据实际情况酌情调节 |
| 结果描述| 是|STRING| -|否|-|-|结果描述文字，例如主油箱指示灯|

### 输出参数

| 参数名         | 参数类型   | 描述               |
| ----------- | ------ | ---------------- |
| indicatorFlash | STRING | 指示灯是否闪烁 |
| indicatorFlashBoolean| BOOLEAN| 布尔结果，"闪烁":True, "未闪烁":False|
| resultDes|STRING| 结果描述文字，例如主油箱指示灯 |


### 结果展示

| 名称     | 描述                  |
| ------ | ------------------- |
| 指示灯状态 |文字，表示指示灯是否闪烁 |
| 指示灯截取圆外界矩形|矩形框|