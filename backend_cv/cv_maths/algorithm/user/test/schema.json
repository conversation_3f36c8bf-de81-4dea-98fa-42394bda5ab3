{"algorithmMetadata": {"name": "出入参测试", "code": "test", "version": "1.0.0", "description": "出入参测试.V1", "type": "openCV", "isBatchProcessing": false, "roiDrawType": "SQUARE", "roiRequired": false}, "input": [{"key": "STRING", "label": "文本参数", "dataType": "STRING", "constraints": {"required": true}, "drawToOsd": false}, {"key": "FLOAT", "label": "浮点参数", "dataType": "FLOAT", "constraints": {"required": false, "max": "999999", "min": "-999999"}, "drawToOsd": true}, {"key": "INTEGER", "label": "整形参数", "dataType": "INTEGER", "constraints": {"required": true, "max": "999999", "min": "-999999"}, "drawToOsd": false}, {"key": "POINT", "label": "坐标点", "dataType": "POINT", "constraints": {"required": true}, "drawToOsd": true}, {"key": "CIRCLE", "label": "圆", "dataType": "CIRCLE", "constraints": {"required": true}, "drawToOsd": true}, {"key": "ELLIPSE", "label": "椭圆", "dataType": "ELLIPSE", "constraints": {"required": true}, "drawToOsd": true}, {"key": "SQUARE", "label": "矩形", "dataType": "SQUARE", "constraints": {"required": true}, "drawToOsd": true}, {"key": "POLYGON", "label": "多边形", "dataType": "POLYGON", "constraints": {"required": true}, "drawToOsd": true}, {"key": "LINE", "label": "直线", "dataType": "LINE", "constraints": {"required": true}, "drawToOsd": true}, {"key": "LINE_ARROW", "label": "箭头直线", "dataType": "LINE_ARROW", "constraints": {"required": true}, "drawToOsd": true}, {"key": "RGB", "label": "点色笔", "dataType": "RGB", "constraints": {"required": true}, "drawToOsd": false}, {"key": "SELECTOR", "label": "下拉框", "dataType": "SELECTOR", "constraints": {"required": true, "maxlength": 3, "options": [{"key": "cat", "label": "猫"}, {"key": "dog", "label": "狗"}, {"key": "person", "label": "人"}, {"key": "car", "label": "车"}]}, "drawToOsd": false}, {"key": "RANGE", "label": "量程", "dataType": "RANGE", "constraints": {"required": true, "max": "999999", "min": "-999999"}, "drawToOsd": false}], "output": [{"key": "testResult", "label": "测试", "dataType": "STRING"}]}