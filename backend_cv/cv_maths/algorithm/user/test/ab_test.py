from backend_common.maths.algorithm.base.algorithm_base import AlgorithmBase, AlgorithmInput
from backend_common.utils.util import circle_bounding_box


class TestReader(AlgorithmBase):
    """
    测试接口算法
    """
    _name = "test"

    def __init__(self, _inputs: AlgorithmInput, _id: int = -1):
        super(<PERSON>Reader, self).__init__(self.__class__._name)

        if _inputs.__eq__(dict()):
            return

        self._local_var._inputs = _inputs
        # 算法实例参数
        self._local_var._settings = None
        self.originImg = self._get_input_images()[0]
        self.roi = self._get_roi()
        input_param = self._get_input_param()

        # 所有参数
        self.STRING = input_param['STRING']
        self.FLOAT = input_param['FLOAT']
        self.INTEGER = input_param['INTEGER']
        # self.BOOLEAN = input_param['BOOLEAN']
        self.POINT = input_param['POINT']
        self.CIRCLE = input_param['CIRCLE']
        self.ELLIPSE = input_param['ELLIPSE']
        self.SQUARE = input_param['SQUARE']
        self.POLYGON = input_param['POLYGON']
        self.LINE = input_param['LINE']
        self.LINE_ARROW = input_param['LINE_ARROW']
        self.RGB = input_param['RGB']
        self.SELECTOR = input_param['SELECTOR']
        self.RANGE = input_param['RANGE']


    def _do_detect(self):
        return True, {"test": {"value":"test"}}, [{"dataType": "SQUARE","textObj": {"value":self.FLOAT, "resultPrecision": 2, "unit": self.STRING}, "coords": circle_bounding_box(self.CIRCLE)}]
    