from copy import deepcopy
from PIL import Image, ImageDraw, ImageFont

import cv2
import numpy as np

font_path = 'simsun.ttc'


def cv2_put_chinese_txt(img, text, position, text_color=(255, 255, 255), bg_color=(255, 255, 255), text_size=24,
                        thickness=1):
    if isinstance(img, np.ndarray):  # 判断是否OpenCV图片类型
        img = Image.fromarray(cv2.cvtColor(img, cv2.COLOR_BGR2RGB))
    # 创建一个可以在给定图像上绘图的对象
    draw = ImageDraw.Draw(img)
    # 字体的格式
    font_style = ImageFont.truetype(font_path, text_size, encoding="utf-8")
    bbox = draw.textbbox(position, text, font_style)
    text_width, text_height = bbox[2] - bbox[0], bbox[3] - bbox[1]
    text_x, text_y = position
    # 绘制文本
    draw.rectangle(((text_x, text_y - text_height), (text_x + text_width, text_y)), fill=bg_color,
                   outline=bg_color)
    draw.text((text_x + 2, text_y - text_height - 2), text, fill=text_color, font=font_style, stroke_width=thickness)
    # 转换回OpenCV格式
    return cv2.cvtColor(np.asarray(img), cv2.COLOR_RGB2BGR)


def is_ash(im, th=100, ksize=3):
    """
    Adjust is ashed for one Image
    :param im: input image, type: ndarray
    :param th: threshold of amplitude, default 100
    :param ksize: kernel size, default 3
    :return: bool, if there is dust return True
    """
    # Convert the image to grayscale if it's not
    if len(im.shape) == 3:
        im = cv2.cvtColor(im, cv2.COLOR_BGR2GRAY)

    def scale2num(img, y_min_scale, y_max_scale, x_min_scale, x_max_scale):
        height, width = img.shape
        ymin = int(height * y_min_scale)
        ymax = int(height * y_max_scale)
        xmin = int(width * x_min_scale)
        xmax = int(width * x_max_scale)
        return ymin, ymax, xmin, xmax

    # Define detect area (avoid black area at right-bottom and left-bottom)
    y_min, y_max, x_min, x_max = scale2num(im, 0.13, 0.86, 0.03, 0.90)
    im = im[y_min: y_max, x_min: x_max]

    # Compression interval to [alpha_, beta_] & Smooth to reduce gradient
    alpha_, beta_, mean_grad_threshold = 25, 225, 4
    im = cv2.normalize(im, None, alpha_, beta_, cv2.NORM_MINMAX)
    im = cv2.medianBlur(im, ksize + 2)  # Simple remove noise / smooth

    # Compute gradients
    gradient_x = cv2.Sobel(im, cv2.CV_64F, 1, 0, ksize=ksize)
    gradient_y = cv2.Sobel(im, cv2.CV_64F, 0, 1, ksize=ksize)
    gradient_magnitude = np.sqrt(gradient_x ** 2 + gradient_y ** 2)

    # gradient_number_threshold increases as th decreases
    gradient_number_threshold = (beta_ - alpha_) / (2 * th) * 100
    line_pixels = np.sum(gradient_magnitude > th)

    # mean gradient is greater than 4 & line pixels is greater than gradient_number_threshold
    res = np.mean(gradient_magnitude) > mean_grad_threshold or line_pixels > gradient_number_threshold

    return ~res


def remove_noise_by_open_close(binary_image, kernel_size=3):
    """
    remove small area of binary images
    :param binary_image: input image
    :param kernel_size: kernel size
    :return: image removed small noise area
    """
    kernel_open = np.ones((kernel_size+2, kernel_size+2), np.uint8)
    kernel_close = np.ones((kernel_size, kernel_size), np.uint8)
    opening = cv2.morphologyEx(binary_image, cv2.MORPH_OPEN, kernel_open)
    closing = cv2.morphologyEx(opening, cv2.MORPH_CLOSE, kernel_close)
    return closing


def calculate_temperature(frame, k):
    """
    Calculate Temperature
    :param frame: RGB image
    :param k: T parameter -> hyper
    :return: Field of Temperature
    """
    frame = frame.astype(float)
    G_channel, R_channel = frame[:, :, 1], frame[:, :, 0]
    G_channel[G_channel == 0] = 1e-8
    R_channel[R_channel == 0] = 1e-8
    r = G_channel / R_channel
    lnr = np.log(r)
    T = 1.0 / (k[0] + k[1] * lnr + k[2] * lnr * lnr)
    return T


def watermark_area(img, y_min_scale, y_max_scale, x_min_scale, x_max_scale):
    """
    calculate location by scale of image
    :param img: 
    :param y_min_scale: 
    :param y_max_scale: 
    :param x_min_scale: 
    :param x_max_scale: 
    :return: 
    """
    height, width, _ = img.shape
    y_min = int(height * y_min_scale)
    y_max = int(height * y_max_scale)
    x_min = int(width * x_min_scale)
    x_max = int(width * x_max_scale)
    return y_min, y_max, x_min, x_max


def remove_watermark(image, mask_coordinates):
    """
    remove watermark
    :param image: input image
    :param mask_coordinates: location of watermark
    :return: image removed watermark
    """
    mask = np.zeros_like(image)
    cv2.fillPoly(mask, [mask_coordinates], (255, 255, 255))

    result = cv2.inpaint(image, mask[:, :, 0],
                         inpaintRadius=3, flags=cv2.INPAINT_TELEA)
    return result


def remove_my_watermark_twice(img):
    """
    remove wawter mark
    :param img: 
    :return: 
    """
    # 左上角水印，参数是水印矩形框的四个坐标占全图的比例
    y_min, y_max, x_min, x_max = watermark_area(img, 0.06, 0.13, 0.03, 0.53)
    img = remove_watermark(img, np.array(
        [(x_min, y_min), (x_max, y_min), (x_max, y_max), (x_min, y_max)]))

    # 右下角水印
    y_min, y_max, x_min, x_max = watermark_area(img, 0.86, 0.94, 0.70, 0.90)
    img = remove_watermark(img, np.array(
        [(x_min, y_min), (x_max, y_min), (x_max, y_max), (x_min, y_max)]))

    return img
