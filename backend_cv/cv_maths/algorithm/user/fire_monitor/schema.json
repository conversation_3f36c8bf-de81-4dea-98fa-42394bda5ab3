{"algorithmMetadata": {"name": "火焰识别监控算法", "code": "fire_monitor", "version": "1.0.0", "description": "火焰识别监控算法.v1.0.0", "type": "openCV", "isBatchProcessing": false, "roiDrawType": "SQUARE", "roiRequired": false, "classification": "ENVIRONMENTAL_SAFETY"}, "input": [{"key": "roi1", "label": "ROI1区域", "dataType": "SQUARE", "constraints": {"required": true}, "des": "ROI1区域，四阶段风机位置", "drawToOsd": false}, {"key": "roi2", "label": "ROI2区域", "dataType": "SQUARE", "constraints": {"required": true}, "des": "ROI区域，五阶段风机位置", "drawToOsd": false}, {"key": "range1", "label": "量程1", "dataType": "RANGE", "defaultValue": {"start": 50, "end": 100}, "constraints": {"required": true, "max": "999999", "min": "-999999", "precision": 1}, "drawToOsd": false}, {"key": "range2", "label": "量程2", "dataType": "RANGE", "defaultValue": {"start": 0, "end": 50}, "constraints": {"required": true, "max": "999999", "min": "-999999", "precision": 1}, "drawToOsd": false}, {"key": "main_fireline_temp", "label": "主火线分割温度", "dataType": "INTEGER", "defaultValue": 920, "constraints": {"min": 800, "max": 1000, "precision": 5, "required": true}, "drawToOsd": false}, {"key": "aux_fireline_temp", "label": "副火线分割温度", "dataType": "INTEGER", "defaultValue": 900, "constraints": {"min": 800, "max": 1000, "precision": 5, "required": true}, "drawToOsd": false}, {"key": "max_temp", "label": "设定最高温度", "dataType": "INTEGER", "defaultValue": 1200, "constraints": {"min": 800, "max": 1500, "precision": 10, "required": true}, "drawToOsd": false}, {"key": "remove_noise_kernel_size", "label": "滤波去噪内核大小", "dataType": "INTEGER", "defaultValue": 3, "constraints": {"min": 0, "max": 10, "precision": 1, "required": true}, "drawToOsd": false}, {"key": "ash_threshold", "label": "蒙灰阈值调整", "dataType": "INTEGER", "defaultValue": 50, "constraints": {"min": 10, "max": 100, "precision": 5, "required": true}, "drawToOsd": false}, {"key": "show_up_part_fireline", "label": "是否展示上边界分割效果", "dataType": "BOOLEAN", "defaultValue": true, "constraints": {"required": true}, "drawToOsd": false}], "output": [{"key": "is_ash", "label": "蒙灰", "dataType": "BOOLEAN"}, {"key": "fireline_main_lowest_in_roi1", "label": "ROI1 主火线最低点", "dataType": "INTEGER"}, {"key": "fireline_aux_lowest_in_roi1", "label": "ROI1 副火线最低点", "dataType": "INTEGER"}, {"key": "max_temp_in_roi1", "label": "ROI1 最高温度", "dataType": "FLOAT"}, {"key": "fireline_main_lowest_in_roi2", "label": "ROI2 主火线最低点", "dataType": "INTEGER"}, {"key": "fireline_aux_lowest_in_roi2", "label": "ROI2 副火线最低点", "dataType": "INTEGER"}, {"key": "max_temp_in_roi2", "label": "ROI2 最高温度", "dataType": "FLOAT"}, {"key": "ratio_between_fireline_of_all_roi", "label": "火线间面积与整个ROI的比例", "dataType": "FLOAT"}]}