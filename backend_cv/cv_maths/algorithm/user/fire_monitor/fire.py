from copy import deepcopy
import os

import cv2
import numpy as np
import random
from loguru import logger
try:
    import cv_maths.algorithm.user.fire_monitor.utils as utils
    from backend_common.exceptions.inspection_exception import AlgorithmProcessException
except:
    import utils
    # from inspection_exception import AlgorithmProcessException




class FireProcessor:
    def __init__(self):
        self.folder = str(random.randint(1000, 10000))
    
    def segment_temperature_by_temp(self, seg_T, thres_temp):
        BW = seg_T > thres_temp
        BW = BW.astype(np.uint8)
        BW[BW != 0] = 255
        return BW
    
    def T2COLOR(self, image_T, alpha_, beta_, c_map='jet'):
        color_map = {
            "jet": cv2.COLORMAP_JET
        }
        # draw
        normalized_T = cv2.normalize(
            image_T, None, alpha_, beta_, cv2.NORM_MINMAX)
        scaled_T = np.nan_to_num(normalized_T, nan=0)  # nan -> 0
        scaled_T = np.uint8(scaled_T)  # int8

        # colormap: jet
        color_image = cv2.applyColorMap(scaled_T, color_map[c_map])
        return color_image

    def execute(self, img, param: dict):
        # 温度标定
        Tpar = [0.001021, -0.000223976568957973, 0.000198664507931246]

        # img = cv2.resize(img, (1920, 1080))
        if img is None:
            msg = 'image failed to read, please check image file path'
            print(msg)
            # raise AlgorithmCheckException(msg)
        
        img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
        img = utils.remove_my_watermark_twice(img)
        h, w = img.shape[0], img.shape[1]

        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

        T = utils.calculate_temperature(img, Tpar)

        Tmain = deepcopy(T)
        Taux = deepcopy(T)

        BW_main = self.segment_temperature_by_temp(Tmain, param['main_fireline_temp'])
        BW_aux = self.segment_temperature_by_temp(Taux, param['aux_fireline_temp'])

        BW_main = utils.remove_noise_by_open_close(BW_main, int(param['remove_noise_kernel_size']))
        BW_aux = utils.remove_noise_by_open_close(BW_aux, int(param['remove_noise_kernel_size']))

        # Tmain满足条件的保持原值不变，不满足的就是nan
        Tmain[~BW_main.astype(bool)] = np.nan  #
        Taux[~BW_aux.astype(bool)] = np.nan  #


        threshold_max = param['max_temp']
        norm_b = np.nanmax(Tmain) / threshold_max * 255
        norm_a = np.nanmin(Tmain) / threshold_max * 125
        color_image_main = self.T2COLOR(Tmain, norm_a, norm_b)

        norm_b = np.nanmax(Taux) / threshold_max * 125
        norm_a = np.nanmin(Taux) / threshold_max * 0
        color_image_aux = self.T2COLOR(Taux, norm_a, norm_b)

        color_image_aux[~np.isnan(Tmain)] = color_image_main[~np.isnan(Tmain)]
        color_image = color_image_aux

        ashed = utils.is_ash(gray, int(param['ash_threshold']))
        ashed = 1 if ashed else 0

        result = {}
        result["is_ash"] = ashed
        if ashed:
            font_color = (255, 255, 255)
            bg_color = (255, 0, 0)
            try:
                text = "蒙灰"
                color_image = utils.cv2_put_chinese_txt(color_image, text, (1500, 900), font_color, bg_color, 100, 2)
            except Exception as e:
                print('中文字体异常' + str(e))

            try:
                text = "ash"
                color_image = utils.cv2_put_chinese_txt(color_image, text, (1500, 900), font_color, bg_color, 100, 2)
            except Exception as e:
                print("error:" + str(e))
        else:

            def calc_mask(roi, thickness_line):
                xmin, ymin = np.min(roi[:, 0]), np.min(roi[:, 1])
                xmax, ymax = np.max(roi[:, 0]), np.max(roi[:, 1])

                # mask
                mask_matrix = np.zeros([ymax - ymin, xmax - xmin])
                for i, t in enumerate(thickness_line[xmin: xmax]):
                    if t > ymin:
                        mask_matrix[:min(int(ymax), int(t)) - ymin, i] = 1
                return mask_matrix

            thickness_line_main = h - np.argmax(np.flipud(BW_main), axis=0)
            thickness_line_main[thickness_line_main == h] = 0
            thickness_line_aux = h - np.argmax(np.flipud(BW_aux), axis=0)
            thickness_line_aux[thickness_line_aux == h] = 0

            # 主副火线最低点
            thickness_line_main_max = max(thickness_line_main)
            thickness_line_main_max_index = np.where(thickness_line_main == thickness_line_main_max)[0][0]
            thickness_line_aux_max = max(thickness_line_aux)
            thickness_line_aux_max_index = np.where(thickness_line_aux == thickness_line_aux_max)[0][0]

            # =================== ROI1 ===================
            # roi1
            roi1 = param['roi1']
            roi1_list = []
            for coor in roi1:
                temp = [coor['x'], coor['y']]
                roi1_list.append(temp)
            roi1 = np.array(roi1_list).astype('int32')
            xmin1, ymin1 = np.min(roi1[:, 0]), np.min(roi1[:, 1])
            xmax1, ymax1 = np.max(roi1[:, 0]), np.max(roi1[:, 1])
            mask_matrix = calc_mask(roi1, thickness_line_main)

            # 主火线在roi1中的面积
            area_main_roi1 = np.sum(mask_matrix)
            if area_main_roi1 == 0:
                area_main_roi1_temp_mean = 0
                area_main_roi1_temp_max = 0
            else:
                # mean, max temp in area
                Tmain_mask = np.multiply(mask_matrix, Tmain[ymin1: ymax1, xmin1: xmax1])
                cv2.imwrite('/opt/tjh/model_server/test_imgs/output/Tmain_mask.jpg', Tmain_mask)
                area_main_roi1_temp_mean = np.nanmean(Tmain_mask)
                area_main_roi1_temp_max = np.nanmax(Tmain_mask)

            # 4阶段主火线最低位
            thickness_line_main_tmp = np.array(thickness_line_main[xmin1: xmax1])
            try:
                y_max_4_main = np.max(thickness_line_main_tmp[thickness_line_main_tmp <= ymax1])
            except Exception as e:
                logger.error("Error in calculating main fireline lowest point in ROI1: {}".format(e))
                raise AlgorithmProcessException("主火线在roi1中不存在，无法计算最低点")

            # 4阶段副火线的最低位
            thickness_line_aux_tmp = np.array(thickness_line_aux[xmin1: xmax1])
            try:
                y_max_4_aux = np.max(thickness_line_aux_tmp[thickness_line_aux_tmp <= ymax1])
            except Exception as e:
                logger.error("Error in calculating auxiliary fireline lowest point in ROI1: {}".format(e))
                raise AlgorithmProcessException("副火线在roi1中不存在，无法计算最低点")

            # 上面计算出来了火线在整张图上的y的平均值。对于整张图来说，坐标的(0, 0)点在左上方，现在需要映射到roi中的量程，0在下方，且两个roi的量程相对独立。
            roi1_range = param['range1']
            roi1_range = [roi1_range['start'], roi1_range['end']]
            roi1_range = np.array(roi1_range).astype('int32')
            roi1_range_low, roi1_range_up = roi1_range[0], roi1_range[1]
            y_max_4_main_inrange_1 = ((roi1_range_low - roi1_range_up) * y_max_4_main +
                                    roi1_range_up * ymax1 - roi1_range_low * ymin1) / (ymax1 - ymin1)

            # y_max_4_main_inrange_1, self.thickness_queue[0] = lowest_point_smooth(y_max_4_main_inrange_1,
            #                                                                       self.thickness_queue[0], roi1_range)
            if y_max_4_main_inrange_1 < roi1_range[0]:
                y_max_4_main_inrange_1 = roi1_range[0]
            elif y_max_4_main_inrange_1 > roi1_range[1]:
                y_max_4_main_inrange_1 = roi1_range[1]

            y_max_4_aux_inrange_1 = ((roi1_range_low - roi1_range_up) * y_max_4_aux +
                                    roi1_range_up * ymax1 - roi1_range_low * ymin1) / (ymax1 - ymin1)

            # y_max_4_aux_inrange_1, self.thickness_queue[1] = lowest_point_smooth(y_max_4_aux_inrange_1,
            #                                                                       self.thickness_queue[1], roi1_range)
            if y_max_4_aux_inrange_1 < roi1_range[0]:
                y_max_4_aux_inrange_1 = roi1_range[0]
            elif y_max_4_aux_inrange_1 > roi1_range[1]:
                y_max_4_aux_inrange_1 = roi1_range[1]
            
            result['fireline_main_lowest_in_roi1'] = float(y_max_4_main_inrange_1)
            result['fireline_aux_lowest_in_roi1'] = float(y_max_4_aux_inrange_1)

            # roi1的面积
            area_roi1 = (ymax1 - ymin1) * (xmax1 - xmin1)
            result['max_temp_in_roi1'] = float(area_main_roi1_temp_max)

            # =================== ROI2 ===================
            # roi2的坐标
            roi2 = param['roi2']
            roi2_list = []
            for coor in roi2:
                temp = [coor['x'], coor['y']]
                roi2_list.append(temp)
            roi2 = np.array(roi2_list).astype('int32')
            xmin2, ymin2 = np.min(roi2[:, 0]), np.min(roi2[:, 1])
            xmax2, ymax2 = np.max(roi2[:, 0]), np.max(roi2[:, 1])

            mask_matrix = calc_mask(roi2, thickness_line_main)
            # 主火线在roi2中的面积
            area_main_roi2 = np.sum(mask_matrix)
            if area_main_roi2 == 0:
                area_main_roi2_temp_mean = 0
                area_main_roi2_temp_max = 0
            else:
                # mean, max temp in area
                Tmain_mask = np.multiply(mask_matrix, Tmain[ymin2: ymax2, xmin2: xmax2])
                area_main_roi2_temp_mean = np.nanmean(Tmain_mask)
                area_main_roi2_temp_max = np.nanmax(Tmain_mask)

            # 5阶段主火线最低位
            y_max_5_main = min(np.max(thickness_line_main[xmin2: xmax2]), ymax2)
            # 5阶段副火线的最低位
            y_max_5_aux = min(np.max(thickness_line_aux[xmin2: xmax2]), ymax2)

            roi2_range = param['range2']
            roi2_range = [roi2_range['start'], roi2_range['end']]
            roi2_range = np.array(roi2_range).astype('int32')
            roi2_range_low, roi2_range_up = roi2_range[0], roi2_range[1]
            y_max_5_main_inrange_2 = ((
                roi2_range_low - roi2_range_up) * y_max_5_main + roi2_range_up * ymax2 - roi2_range_low * ymin2) / (
                ymax2 - ymin2)
            # y_max_5_main_inrange_2, self.thickness_queue[2] = lowest_point_smooth(y_max_5_main_inrange_2,
            #                                                                       self.thickness_queue[2], roi2_range)
            if y_max_5_main_inrange_2 < roi2_range[0]:
                y_max_5_main_inrange_2 = roi2_range[0]
            elif y_max_5_main_inrange_2 > roi2_range[1]:
                y_max_5_main_inrange_2 = roi2_range[1]

            y_max_5_aux_inrange_2 = ((
                roi2_range_low - roi2_range_up) * y_max_5_aux + roi2_range_up * ymax2 - roi2_range_low * ymin2) / (
                ymax2 - ymin2)

            # y_max_5_aux_inrange_2, self.thickness_queue[3] = lowest_point_smooth(y_max_5_aux_inrange_2,
            #                                                                      self.thickness_queue[3], roi2_range)
            if y_max_5_aux_inrange_2 < roi2_range[0]:
                y_max_5_aux_inrange_2 = roi2_range[0]
            elif y_max_5_aux_inrange_2 > roi2_range[1]:
                y_max_5_aux_inrange_2 = roi2_range[1]
            
            
            result['fireline_main_lowest_in_roi2'] = int(y_max_5_main_inrange_2)
            result['fireline_aux_lowest_in_roi2'] = int(y_max_5_aux_inrange_2)

            # roi2的面积
            area_roi2 = (ymax2 - ymin2) * (xmax2 - xmin2)
            result['max_temp_in_roi2'] = area_main_roi2_temp_max

            # 如果副火线的最大值仍旧比roi1的ymin小or主火线的最小值仍旧比roi2的ymax大，则主副火线与roi1 roi2都无交集，主副火线间面积为0
            if max(thickness_line_aux[xmin1: xmax1]) <= ymin1 or min(thickness_line_main[xmin2: xmax2]) >= ymax2:
                area_between_main2aux = 0
                area_main_aux_temp_mean_1, area_main_aux_temp_mean_2 = 0, 0
                area_main_aux_temp_max_1, area_main_aux_temp_max_2 = 0, 0
                area_between_main2aux_roi1, area_between_main2aux_roi2 = 0, 0
            else:
                # 火线与roi1围成的面积
                thickness_line_aux_tmp = np.array(thickness_line_aux[xmin1: xmax1])
                thickness_line_aux_tmp[thickness_line_aux_tmp > ymax1] = ymax1
                thickness_line_main_tmp = np.array(
                    thickness_line_main[xmin1: xmax1])
                thickness_line_main_tmp[thickness_line_main_tmp > ymax1] = ymax1
                area_between_main2aux_roi1 = np.sum(
                    thickness_line_aux_tmp - thickness_line_main_tmp)
                area_between_main2aux_mask = np.zeros(
                    (ymax1 - ymin1, xmax1 - xmin1))

                for x, y, i in zip(thickness_line_main_tmp, thickness_line_aux_tmp, range(xmax1 - xmin1)):
                    area_between_main2aux_mask[x - ymin1 if x -
                                            ymin1 > 0 else 0: y - ymin1, i] = 1
                # 区域内平均温度
                area_main_aux_temp_mean_1 = np.sum(
                    np.multiply(area_between_main2aux_mask, T[ymin1: ymax1, xmin1: xmax1])) / np.sum(
                    area_between_main2aux_mask)
                # 区域内最大温度
                area_main_aux_temp_max_1 = np.max(np.multiply(
                    area_between_main2aux_mask, T[ymin1: ymax1, xmin1: xmax1]))

                # 火线与roi2围成的面积
                thickness_line_aux_tmp = np.array(thickness_line_aux[xmin2: xmax2])
                thickness_line_aux_tmp[thickness_line_aux_tmp > ymax2] = ymax2
                thickness_line_main_tmp = np.array(
                    thickness_line_main[xmin2: xmax2])
                thickness_line_main_tmp[thickness_line_main_tmp > ymax2] = ymax2
                area_between_main2aux_roi2 = np.sum(
                    thickness_line_aux_tmp - thickness_line_main_tmp)
                area_between_main2aux_mask = np.zeros(
                    (ymax2 - ymin2, xmax2 - xmin2))

                for x, y, i in zip(thickness_line_main_tmp, thickness_line_aux_tmp, range(xmax2 - xmin2)):
                    area_between_main2aux_mask[x - ymin2 if x -
                                            ymin2 > 0 else 0: y - ymin2, i] = 1
                # 区域内平均温度
                area_main_aux_temp_mean_2 = np.sum(
                    np.multiply(area_between_main2aux_mask, T[ymin2: ymax2, xmin2: xmax2])) / np.sum(
                    area_between_main2aux_mask)
                # 区域内最大温度
                area_main_aux_temp_max_2 = np.max(np.multiply(
                    area_between_main2aux_mask, T[ymin2: ymax2, xmin2: xmax2]))
                area_between_main2aux = area_between_main2aux_roi1 + area_between_main2aux_roi2
            ratio_between_fireline_of_all_roi = area_between_main2aux / (area_roi1 + area_roi2)
            result['ratio_between_fireline_of_all_roi'] = float(ratio_between_fireline_of_all_roi)

            # =================== 绘制最低点 ===================
            cv2.circle(color_image, [int(thickness_line_main_max_index), int(thickness_line_main_max)], radius=5,
                    color=(0, 255, 0), thickness=1,
                    lineType=2)
            cv2.circle(color_image, [int(thickness_line_aux_max_index), int(thickness_line_aux_max)], radius=30,
                    color=(0, 0, 255), thickness=10,
                    lineType=2)

            # =================== 绘制ROI ===================
            cv2.rectangle(color_image, (xmin1, ymin1),
                        (xmax1, ymax1), (255, 255, 255), 10)
            cv2.rectangle(color_image, (xmin2, ymin2),
                        (xmax2, ymax2), (255, 255, 255), 10)

            # 画出主副火线的位置
            thickness_line_main_up_part = np.argmax(BW_main, axis=0)
            thickness_line_aux_up_part = np.argmax(BW_aux, axis=0)
            thickness_line_main[thickness_line_main == h] = 0
            # 画散点的火际线
            for i in range(len(thickness_line_main_up_part)):
                #  主火线下半部分
                if thickness_line_main[i] != 0:
                    cv2.circle(color_image, [i, int(thickness_line_main[i])], radius=5,
                            color=(255, 0, 255), thickness=-1,
                            lineType=2)
                # 副火线下半部分
                if thickness_line_aux[i] != 0:
                    cv2.circle(color_image, [i, int(thickness_line_aux[i])], radius=5,
                            color=(0, 0, 255), thickness=-1,
                            lineType=2)
                if int(param['show_up_part_fireline']):
                    # 主火线上半部分
                    if thickness_line_main_up_part[i] != 0:
                        cv2.circle(color_image, [i, int(thickness_line_main_up_part[i])], radius=5,
                                color=(255, 0, 255), thickness=-1,
                                lineType=2)
                    # 副火线上半部分
                    if thickness_line_aux_up_part[i] != 0:
                        cv2.circle(color_image, [i, int(thickness_line_aux_up_part[i])], radius=5,
                                color=(0, 0, 255), thickness=-1,
                                lineType=2)
        # path = os.path.join('/opt/tjh/model_server/lts/intelligent_inspection_backend/backend_cv/test/resources/output-img/fire_monitor', 'output.jpg')
        # cv2.imwrite(path, color_image)
        return result, color_image


if __name__ == "__main__":
    fire_processor = FireProcessor()
    img_path = '/opt/tjh/model_server/test_imgs/000.jpg'
    
    config = {
        # "roi1": [{"x": 7,"y": 18},{"x": 7,"y": 374},{"x": 1900,"y": 374},{"x": 1900,"y": 18}],
        "roi1": [{"x": 7,"y": 18},{"x": 7,"y": 599},{"x": 1900,"y": 599},{"x": 1900,"y": 18}],
        "range1": {
				"start": 50,
				"end": 100
			},
        "roi2": [{"x": 10,"y": 578},{"x": 10,"y": 1070},{"x": 1905,"y": 1070},{"x": 1905,"y": 578}],
        # "roi2": [{"x": 10,"y": 620},{"x": 10,"y": 1070},{"x": 1905,"y": 1070},{"x": 1905,"y": 620}],
        "range2": {
				"start": 0,
				"end": 50
			},
        "main_fireline_temp": 920,
        "aux_fireline_temp": 900,
        "max_temp": 1200,
        "remove_noise_kernel_size": 3,
        "ash_threshold": 50,
        "show_up_part_fireline": 1
    }
    img = cv2.imread(img_path)
    print("image shape is ", img.shape)
    result = fire_processor.execute(img, config)
    print(result)