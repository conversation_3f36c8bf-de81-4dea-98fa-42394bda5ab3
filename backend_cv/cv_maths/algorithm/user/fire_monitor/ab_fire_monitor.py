
from enum import Enum
from backend_common.utils.util import circle_bounding_box
from backend_common.exceptions.inspection_exception import AlgorithmProcessException
from backend_common.maths.algorithm.base.algorithm_base import AlgorithmBase, AlgorithmInput
from cv_maths.algorithm.user.fire_monitor.fire import FireProcessor
from loguru import logger
import traceback
import os
import platform
from copy import deepcopy
from PIL import Image, ImageDraw, ImageFont

import cv2
import numpy as np
SUFFIX_START = "sub"


class FireMonitorReader(AlgorithmBase):
    """
    火焰温度监测算法
    """

    _name = "fire_monitor"  # 此名称同算法文件夹名

    def __init__(self, _inputs: AlgorithmInput, _id: int = -1):
        super(FireMonitorReader, self).__init__(self.__class__._name)
        if _inputs.__eq__(dict()):
            return

        self._local_var._inputs = _inputs
        # 算法实例参数
        self._local_var._settings = None
        self.originImg = self._get_input_images()[0]
        self.roi = self._get_roi()
        self.input_param = self._get_input_param()

    def _do_detect(self):
        
        fire_processor = FireProcessor()
        try:
            res, res_img = fire_processor.execute(self.originImg, self.input_param)
            logger.info("output is {}".format(res))
            return True, {"is_ash": {"value":res['is_ash']}, "fireline_main_lowest_in_roi1": {"value":res['fireline_main_lowest_in_roi1']}, "fireline_aux_lowest_in_roi1": {"value":res['fireline_aux_lowest_in_roi1']},
                      "max_temp_in_roi1": {"value":res['max_temp_in_roi1']}, "fireline_main_lowest_in_roi2": {"value":res['fireline_main_lowest_in_roi2']}, "fireline_aux_lowest_in_roi2": {"value":res['fireline_aux_lowest_in_roi2']}, 
                      "max_temp_in_roi2": {"value":res['max_temp_in_roi2']}, "ratio_between_fireline_of_all_roi": {"value":res['ratio_between_fireline_of_all_roi']}}, []
        except (AlgorithmProcessException, Exception) as Algorithm1e:
            tb = traceback.extract_tb(Algorithm1e.__traceback__)
            filename, lineno, funcname, text = tb[-1]
            logger.error(f"Error in file {filename}, line {lineno}, in {funcname}: {text}")


