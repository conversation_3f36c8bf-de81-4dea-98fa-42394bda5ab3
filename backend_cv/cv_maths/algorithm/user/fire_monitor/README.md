## 火焰识别监测算法

## 版本:

 v 1.0.0

## 描述:

该算法用于识别锅炉或者其他装置火焰燃烧中火焰视频的火线位置，支持工厂以及实验室大多数火焰燃烧识别。

### 输入参数
| 参数名     | 是否绘制 | 参数类型    | 默认值 | 是否必填 | 数据范围   | 精度   |描述
|-------|--------------|----------------------|--------------|----------------------|--------------|----------------------|----------------------|
| ROI区域        |否        | SQUARE | -| 是| -| -|ROI区域，需绘制在球团集中区域                                      |
| 主火线分割温度   |否      | INTEGER | 920| 是 |800-1000|5|根据温度进行分割                                                   |
| 副火线分割温度    |否      | INTEGER |900| 是 |800-1000|5|根据温度进行分割                                                   |
| 设定最高温度      |否      | INTEGER | 1200| 是 |800-1500|10|设置最高温度，用于层级分割                                         |
| 滤波去噪内核大小   |否     | INTEGER | 3| 是 |0-10|1|设置去噪的滤波参数，参数越大，去噪越强                             |
| 蒙灰阈值调整      |否      | INTEGER | 50|是 |10-100|5|由于摄像头差异，需要对蒙灰算法进行调整，阈值越大，蒙灰检测更加灵敏 |
| 是否展示上边界分割效果 |否 | BOOLEAN | false| 是 |-|-|是否展示上部火线                                                       |

### 输出参数

| 参数名         | 参数类型   | 描述               |
| ----------- | ------ | ---------------- |
| 蒙灰             | BOOLEAN |    是否蒙灰  |
| ROI1主火线最低点 | INTEGER |   ROI1 主火线最低点位置   |
| ROI1副火线最低点 | INTEGER |    ROI1 副火线最低点位置  |
| ROI1最高温度     | FLOAT |   ROI1 中火焰最高温度   |
| ROI2主火线最低点 | INTEGER |   ROI2 主火线最低点位置   |
| ROI2副火线最低点 | INTEGER |   ROI2 副火线最低点位置   |
| ROI2最高温度     | FLOAT |    ROI2 中火焰最高温度  |
| 火线间面积占比   | FLOAT |    火线间面积与整个ROI的比例  |
