#!/usr/bin/env python

"""算法包安装制作脚本 打包指定算法"""

#  python .\setup.py bdist_wheel --dist-dir dist
#   pip install xxx.whl --target path/to/target
#  python .\setup.py sdist --dist-dir dist --formats=gztar
#   pip install xxx.tar.gz --target path/to/target

#  py\cv_maths\algorithm\user> python setup.py sdist --dist-dir ../dist --formats=gztar,zip --verbose
#  pip install ..\dist\liquid_level-1.0.0.tar.gz --target ..\target
#

from setuptools import setup

block_name = "<input user custom block name here>"

# requirements which will be auto install when setup
install_requires = []
# requirements which declared, but won't be auto install when setup
extras_requires = []
# requirements which for test case
test_requires = []
# requirements which for setup.py itself
setup_requires = []

test_requirements = ['pytest>=3', ]

setup(
    author="qinghaibo",
    author_email='<EMAIL>',
    python_requires='>=3.6',
    classifiers=[
        'Development Status :: 2 - Pre-Alpha',
        'Intended Audience :: Developers',
        'Natural Language :: English',
        'Programming Language :: Python :: 3',
        'Programming Language :: Python :: 3.6',
        'Programming Language :: Python :: 3.7',
        'Programming Language :: Python :: 3.8',
    ],
    description="算法块打包 by setuptools",
    install_requires=install_requires,
    extras_requires=extras_requires,
    test_requires=test_requires,
    setup_requires=setup_requires,
    # long_description=open("../../../README.md").read(),
    long_description="copyright © hollysys.com 2023",
    long_description_content_type='text/markdown',
    include_package_data=True,
    keywords=block_name,
    name=block_name,
    packages=[block_name],
    package_data={
        block_name: ['*.py', '*.json', '*.yml', 'inference_model/*'],
    },
    tests_require=test_requirements,
    url='https://www.hollysys.com',
    version='1.0.0',
    zip_safe=False,
)
