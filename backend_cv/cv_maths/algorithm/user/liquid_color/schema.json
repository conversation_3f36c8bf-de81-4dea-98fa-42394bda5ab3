{"algorithmMetadata": {"name": "液体颜色识别", "code": "liquid_color", "version": "1.0.0", "description": "液体颜色识别.V1", "type": "openCV", "isBatchProcessing": false, "roiDrawType": "SQUARE", "roiRequired": false, "classification": "OTHERS"}, "input": [{"key": "liquild_roi", "label": "液体区域", "dataType": "SQUARE", "constraints": {"required": true}, "drawToOsd": true}, {"key": "color_select", "label": "检测颜色", "dataType": "SELECTOR", "constraints": {"required": true, "maxLength": 1, "options": [{"key": 0, "label": "红色"}, {"key": 2, "label": "绿色"}, {"key": 3, "label": "蓝色"}, {"key": 4, "label": "橙黄色"}, {"key": 5, "label": "青色"}, {"key": 6, "label": "紫色"}, {"key": 7, "label": "黑色"}, {"key": 8, "label": "白色"}, {"key": 9, "label": "灰色"}]}, "drawToOsd": false}, {"key": "area_thresh", "label": "颜色面积", "dataType": "INTEGER", "defaultValue": 0, "constraints": {"min": 0, "max": 3000, "precision": 10, "required": true}, "drawToOsd": false}, {"key": "area_ratio_thresh", "label": "面积占比", "dataType": "FLOAT", "defaultValue": 0.5, "constraints": {"min": 0, "max": 1, "precision": 0.01, "required": true}, "drawToOsd": false}, {"key": "resultDes", "label": "结果描述", "dataType": "STRING", "value": "", "constraints": {"required": false}, "drawToOsd": true}], "output": [{"key": "liqud_color", "label": "颜色检测结果", "dataType": "STRING"}, {"key": "liqud_colorBoolean", "label": "颜色检测布尔值", "dataType": "BOOLEAN"}]}