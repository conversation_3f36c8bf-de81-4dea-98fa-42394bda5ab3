## 液体颜色识别算法V1.0.0

### 描述

该算法用于识别矩形ROI区域中（输入参数条件下）是否存在指定的颜色

- 是否配置ROI：否

- 是否配置多图输入：否

### 输入参数

| 参数名     | 是否绘制              | 参数类型     | 默认值 | 是否必填 | 数据范围   | 精度   | 描述                                            |
| ------- | ----------------- | -------- | --- | ---- | ------ | ---- | --------------------------------------------- |
| 液体ROI区域 | 是                 | SQUARE   | 无   | 是    | -      | -    |    矩形ROI区域                                           |
| 检测颜色    | 否 | SELECTOR | 无   | 是    | 单选框     | -    | 单选框，指定需检测的颜色                                 |
| 面积占比    | 否                 | FLOAT    | 0.5 | 是    | 0-1    | 0.01 | 颜色面积在ROI中占比阈值，<br>大于该阈值，则认为检测到指定的颜色 |
| 颜色面积    | 否                 | INTEGER  | 0   | 是    | 0-3000 | 10   | 屏蔽小于该值的颜色轮廓                           |
| 结果描述    | 是                 | STRING   | 无   | 否    | -      | -    | 结果描述文字                                              |

### 输出参数

| 参数名         | 参数类型   | 描述               |
| ----------- | ------ | ---------------- |
| 颜色检测结果 | SRTING | 输出值为"检测到颜色"或"未检测到颜色"|
| 颜色检测结果布尔值 | BOOLEAN | 输出值为"检测到颜色":True或"未检测到颜色":False|

### 结果展示

| 名称     | 描述                  |
| ------ | ------------------- |
| 颜色识别结果   | "检测到颜色"或"未检测到颜色"    |
| 颜色区域轮廓 | 绘制颜色区域多边形轮廓               |