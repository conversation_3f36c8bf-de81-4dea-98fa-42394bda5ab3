import cv2
import numpy as np
from backend_common.maths.algorithm.base.algorithm_base import AlgorithmInput, AlgorithmBase
from backend_common.constants.constant import LOWER, UPPER


class LiquidColorReader(AlgorithmBase):

    _name = "液体颜色识别算法"

    def __init__(self, _inputs: AlgorithmInput):
        super(LiquidColorReader, self).__init__(self.__class__._name)

        if _inputs.__eq__(dict()):
            return

        self._local_var._inputs = _inputs
        # 获取图片
        self.origin_imgs = self._get_input_images(first_only=True)
        # 获取参数字典
        input_param = self._get_input_param()

        # 算法输入参数获取
        self.area_thresh = input_param["area_thresh"]
        self.area_ratio_thresh = input_param["area_ratio_thresh"]
        self.color_select = input_param["color_select"][0]

        self.liquild_roi_input = input_param["liquild_roi"]
        self.liquid_roi = [list(k.values()) for k in input_param["liquild_roi"]]

        self.result_des = input_param["resultDes"]

    def get_contours(self, cnt, roi_coor):
        """
        cnt: numpy
        roi_coor: (x1, y1)
        """
        cnt_list = cnt.tolist()

        res = []
        for i in cnt_list:
            info = i[0]
            info[0] = info[0] + roi_coor[0]
            info[1] = info[1] + roi_coor[1]
            res.append(info)

        res = res[::4]
        return res

    def get_roi(self, ori_image):
        x1, y1, x2, y2 = self.x1, self.y1, self.x2, self.y2
        w = x2 - x1
        h = y2 - y1

        self.roi_img = ori_image[y1:y1 + h, x1:x1 + w]
        self.roi_area = w * h

    def det_color(self, img, object_roi, area_ratio_thresh, area_thresh, color_select):
        color_index = int(color_select)

        self.area_ratio_thresh = float(area_ratio_thresh)

        self.x1 = int(object_roi[0][0])
        self.y1 = int(object_roi[0][1])
        self.x2 = int(object_roi[2][0])
        self.y2 = int(object_roi[2][1])

        self.get_roi(img)

        roi_hsv_img = cv2.cvtColor(self.roi_img, cv2.COLOR_BGR2HSV)

        color_mask = cv2.inRange(roi_hsv_img, LOWER[color_index], UPPER[color_index])
        if color_index == 0:
            color_mask_red = cv2.inRange(roi_hsv_img, LOWER[1], UPPER[1])
            color_mask = cv2.add(color_mask, color_mask_red)

        kernel = np.ones((5, 5), dtype=np.uint8)
        mask_open = cv2.morphologyEx(color_mask, cv2.MORPH_OPEN, kernel)

        contours, _ = cv2.findContours(mask_open, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_NONE)

        if len(contours) == 0:
            return ['未检测到颜色', False, []]

        contours_point = []
        area_sum = 0
        for cnt in contours:
            contour_area = cv2.contourArea(cnt)
            if contour_area > area_thresh:
                area_sum += contour_area
                contours_point.append(self.get_contours(cnt, (self.x1, self.y1)))

        area_ratio = area_sum / self.roi_area
        if area_ratio >= self.area_ratio_thresh:
            return ['检测到颜色', True, contours_point]
        else:
            return ['未检测到颜色', False, contours_point]

    def _do_detect(self):
        """
        调用入口函数
        """
        res_name, res_name_boolean, contours_points = self.det_color(self.origin_imgs,
                                                   self.liquid_roi,
                                                   self.area_ratio_thresh,
                                                   self.area_thresh,
                                                   self.color_select)
        osdinfo = []
        for one_contours in contours_points:
            contour = []
            for info in one_contours:
                contour.append({"x": info[0], "y": info[1]})
            osdinfo.append({"dataType": "POLYGON", "coords": contour})

        if self.result_des is None:
            output = {"value": res_name}
        else:
            output = {"value": res_name, "resultDes": self.result_des}
        output_boolean = {"value": res_name_boolean}
        osdinfo.append({"dataType": "SQUARE", "textObj": output, "coords": self.liquild_roi_input})

        return True, {"liqud_color": output, "liqud_colorBoolean": output_boolean}, osdinfo
