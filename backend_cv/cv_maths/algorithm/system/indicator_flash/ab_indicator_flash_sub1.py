from typing import Any

import cv2

from backend_common.maths.algorithm.base.algorithm_base import AlgorithmInput
from backend_common.utils.util import get_mask_by_color
from cv_maths.algorithm.system.indicator_flash.ab_indicator_flash import IndicatorFlashReader, IndicatorStatus


class IndicatorFlashReaderSub1(IndicatorFlashReader):
    """
    指示灯闪烁识别
    """

    # schema_path = os.path.join(os.path.dirname(__file__), "schema.json")

    # _name = "IndicatorFlashReaderSub0.Base.v1"
    _index = 1
    _description = "指示灯闪烁识别基于连通域分析.质量模型.版本v1"
    _cn_name = "指示灯闪烁识别算法(质量模型)"

    # 用以指示灯亮灭状态判断的非零像素统计阈值 !important
    COUNT_THRESHOLD = 0.42

    def __init__(self, _inputs: AlgorithmInput, _id: int = -1):
        super(IndicatorFlashReaderSub1, self).__init__(_inputs, _id)

    def _preprocess(self) -> Any:
        pass

    def _do_detect(self) -> (bool, Any, tuple):
        return super(IndicatorFlashReaderSub1, self)._do_detect()

    def _postprocess(self) -> Any:
        pass

    def get_values(self, param_dict, img, threshold):
        """
        单个指示灯状态识别,
        基于非零像素点统计的识别

        :param param_dict: 指示灯圆心坐标和半径，颜色
        :param img:  单张图片
        :param threshold 阈值灵敏度
        :return: 判断当前灯运行状态
        """
        # 要对比的图片
        #  指示灯 初始用户配置信息
        cir = param_dict['circle']
        color_rgb = param_dict['color_picker']
        colorThreshold = int(param_dict['colorThreshold'])
        roi = self._get_roi(img, cir)

        # 计算提取区域的图片亮度
        # hsv = cv2.cvtColor(roi, cv2.COLOR_BGR2HSV)

        mask = get_mask_by_color(roi, color_rgb, colorThreshold, color_picker=True)
        count = cv2.countNonZero(mask)
        w, h, _ = roi.shape
        # 非零点占全roi比率
        ratio = count / (w * h)

        magic_factor = threshold if threshold is not None else self.COUNT_THRESHOLD
        print("count==", count, f"{w}*{h}={w * h}", "亮" if ratio >= magic_factor else "灭")
        return IndicatorStatus.On.value[2] if ratio >= magic_factor else IndicatorStatus.Off.value[2]
