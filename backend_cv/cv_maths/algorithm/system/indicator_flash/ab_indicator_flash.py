import importlib
import math
import os
import sys
from enum import Enum
from typing import Any, List

import numpy as np

from backend_common.constants.alarm import ExceedLimitAlarmRule
from backend_common.constants.constant import AlgorithmClassifyEnum, AlgorithmStrategyEnum, FAKE_PERCENT
from backend_common.constants.math_constants import ALGORITHM_CV_USER_DEFINE_PATH, ALGORITHM_CV_SYSTEM_INNER_PATH
from backend_common.utils.util import circle2bbox, render_text_on_bbox
from backend_common.exceptions.inspection_exception import InspectionException
from backend_common.maths.algorithm.base.algorithm_base import AlgorithmBase, AlgorithmStrategy, AlgorithmSettings, AlgorithmInput, \
    AlgorithmDetail, AlgorithmBaseInfo

SUFFIX_START = "sub"


class IndicatorStatus(Enum):
    # 常亮
    On = (1, '常亮', 'on')
    # 常灭
    Off = (0, '常灭', 'off')
    # 闪烁 TODO 暂不支持
    Flashing = (2, '闪烁', 'flashing')
    # 未知
    Unknown = (3, '未知', 'unknown')

    @classmethod
    def from_str(cls, txt):
        for k, v in cls.__members__.items():
            if k.casefold() == txt.casefold():
                return v
        else:
            raise ValueError(f"'{cls.__name__}' enum not found for '{txt}'")

    @classmethod
    def value_of(cls, value):
        for k, v in cls.__members__.items():
            if v.value[0] == value:
                return v
        else:
            raise ValueError(f"'{cls.__name__}' enum not found for '{value}'")


class IndicatorFlashReader(AlgorithmBase):
    """
    指示灯闪烁识别
    """

    schema_path = os.path.join(os.path.dirname(__file__), "schema.json")

    _name = "indicator_flash"  # 此名称同算法文件夹名
    _description = "指示灯闪烁识别.基础.版本v1"
    _cn_name = "指示灯闪烁识别算法"

    # 支持的算法分类个数
    __supported_classify = (AlgorithmClassifyEnum.Primary, AlgorithmClassifyEnum.Secondary)
    # 支持的算法策略，primary -> main & secondary -> main
    __supported_strategies = [
        AlgorithmStrategy(__supported_classify[0], AlgorithmStrategyEnum.Main, "指示灯闪烁识别策略1",
                          "基于颜色值统计的指示灯闪烁识别主策略", kv_param={}),
        AlgorithmStrategy(__supported_classify[1], AlgorithmStrategyEnum.Main, "指示灯闪烁识别策略2",
                          "基于连通域分析的指示灯闪烁识别主策略", kv_param={})
    ]

    # 支持的算法参数，若干
    __supported_params = AlgorithmBase.json_schema_2_algorithm_params(schema_path)

    def __init__(self, _inputs: AlgorithmInput, _id: int = -1):
        super(IndicatorFlashReader, self).__init__(self.__class__._name, self.__class__._description,
                                                   self.__class__._cn_name,
                                                   self.__class__.__supported_strategies,
                                                   self.__class__.__supported_params)
        if _inputs.__eq__(dict()):
            return

        self._local_var._inputs = _inputs
        # 算法实例参数
        self._local_var._settings = None

        self._inputImages = self._get_input_images()
        self._indicatorList = self._get_input_value_by_name("indicatorList")
        self._threshold = self._get_input_value_by_name("threshold")
        self._indicatorCount = len(self._indicatorList)

        # self._processCutTarget = None  # TODO
        # self._processFinalArea = None
        self._processFinalRet = None
        self._processOutputDir = os.getcwd()

        output_define = AlgorithmBase._get_algorithm_schema_info(self.schema_path, "outputDefine")
        self._val_enum = output_define['val_enum']

    def _supported_classify(self):
        return self.__supported_classify

    def _supported_strategy(self):
        return self.__supported_strategies

    def _supported_params(self):
        return self.__supported_params

    def _preprocess(self) -> Any:
        # for image in self._inputImages:
        #     pass
        pass

    def _do_detect(self) -> (bool, Any, tuple):
        status = [[self.get_values(param_dict, img, self._threshold) for param_dict in self._indicatorList] for img in
                  self._inputImages]
        # 二维数组 依次按每张图每盏灯输出灯的状态
        self._processFinalRet = status

        return True, self._processFinalRet, [
            [[circle2bbox(indicator['circle']), FAKE_PERCENT] for indicator in self._indicatorList] for _ in
            self._inputImages]

    def _postprocess(self) -> Any:
        pass

    def _gen_result_img(self) -> List[np.ndarray]:
        assert self._processFinalRet is not None

        ret_images = []
        for img_idx, one_img_ret in enumerate(self._processFinalRet):
            ret_img = self._inputImages[img_idx]
            for idx, indicator_status in enumerate(one_img_ret):
                txt = IndicatorStatus.from_str(indicator_status).value[2]
                circle = self._indicatorList[idx]['circle']
                # cv2.putText(self._inputImages[img_idx], txt, circle['center'], cv2.FONT_HERSHEY_COMPLEX, 0.5,
                #             (60, 255, 0), 1)
                bbox = circle2bbox(circle)
                # x,y to min-max
                bbox = [bbox[0][0], bbox[0][1], bbox[2][0], bbox[2][1]]

                text = f'{txt} {"%.2f" % FAKE_PERCENT}'
                ret_img = render_text_on_bbox(ret_img, bbox, text)

            ret_images.append(ret_img)
        return ret_images

    def get_algorithm_detail(self, a_type) -> AlgorithmDetail:
        schema_base = AlgorithmBase._get_algorithm_schema_info(self.schema_path, "algorithm")
        info = AlgorithmBaseInfo(self._id, self._name, self._name, self.cn_name, self._description,
                                 batch_support=schema_base.get("batchSupport"),
                                 alarm_support=schema_base.get("alarmSupport"),
                                 is_override_alarm=schema_base.get("isOverrideAlarm"))
        classifies = [self._classify2info(c) for c in self.__supported_classify]
        strategies = [self._strategy2info(s) for s in self.__supported_strategies]
        roi = self._get_roi_define(self.schema_path)
        scene_image = self._get_scene_image(self.schema_path)
        params = AlgorithmBase.json_schema_2_algorithm_params(self.schema_path)  # 重新加载一下, 可能会有刷新
        sub_list = self.load_algorithm_sublist(self._name, a_type, 'cv')
        output_define = self._output_define2info(
            AlgorithmBase._get_algorithm_schema_info(self.schema_path, "outputDefine"))
        return AlgorithmDetail(info, classifies, strategies, roi, scene_image, params, output_define, sub_list)

    def determine_algorithm_instance(self, settings: AlgorithmSettings = None, index: int = 0,
                                     a_type: str = "system_inner"):
        suffix = f"{SUFFIX_START}{index}"
        module_name = f".{self._name}.ab_{self._name}_{suffix}"

        base_path = ALGORITHM_CV_SYSTEM_INNER_PATH
        if a_type == "user_define":
            base_path = ALGORITHM_CV_USER_DEFINE_PATH

        try:
            M = importlib.import_module(module_name, base_path)
        except ModuleNotFoundError:
            raise InspectionException(f"Auto-load module '{module_name}' from '{base_path}' \
                                failure, please check it manually")

        # !!!!  找出子类中名称以当前 suffix.capitalize()  为 END 的作为 target 算法类  !!!!
        target_clazz_ = [clazz for clazz in IndicatorFlashReader.__subclasses__()
                         if clazz.__name__.endswith(suffix.capitalize())][0]

        algorithm_instance: IndicatorFlashReader = target_clazz_(self._local_var._inputs, self._id)
        algorithm_instance._settings = settings

        return algorithm_instance

    def _get_roi(self, img, cir):
        """
        按圆心和半径抠出ROI区域, 取圆内接正方形
        :param img:  图片
        :param cir:  圆心坐标，半径
        :return:
        """
        x, y, r = cir['center'][0], cir['center'][1], cir['radius']

        offset = int(r / math.sqrt(2))
        # offset = r
        rect_x = (x - offset)
        rect_y = (y - offset)
        self.img_cut_check(img, x + offset, y + offset)
        part_img = img[rect_y:(y + offset), rect_x:(x + offset)]
        return part_img

    # 通过预先设置的文件获取
    def get_values(self, param_dict, img, threshold):
        """
        单个指示灯状态识别

        :param param_dict: 指示灯圆心坐标和半径，颜色
        :param img:  单张图片
        :param threshold 阈值灵敏度
        :return: 判断当前灯运行状态
        """
        raise InspectionException("you must implement this function by yourself!")

    def _alarm_trig(self, detect_ret) -> List[List]:
        """
        根据报警规则触发报警
        return:
            ret : 是否报警
            level: 报警等级
            desc: 报警描述
        """
        status, val, _ = detect_ret
        alarms = []
        if self._local_var._settings.alarm_rules is None or len(self._local_var._settings.alarm_rules) == 0:
            return alarms

        for ret_of_each_img in val:
            alm_of_img = []
            for v_idx, v_txt in enumerate(ret_of_each_img):
                # 文本 转 数值, 默认 sys.maxsize 此时按不放过异常处理
                a_v = self._val_enum.get(v_txt, sys.maxsize)
                # 对于一张图仅产生一条汇总报警的，报警逻辑应当走自己的逻辑 比如 安全帽，明火等
                # 此时报警个数 和检测结果数是不一致的, 无法直接 [v_idx]
                rule = self._local_var._settings.alarm_rules[v_idx]
                ret, level, desc = False, None, None
                if isinstance(rule, ExceedLimitAlarmRule):
                    if rule.hh_is_bind and a_v == rule.hh_limit:
                        ret, level, desc = True, "HH", "指示灯状态异常" if rule.hh_txt is None else rule.hh_txt
                    elif rule.h_is_bind and a_v == rule.h_limit:
                        ret, level, desc = True, "H", "指示灯状态异常" if rule.h_txt is None else rule.h_txt
                    elif rule.l_is_bind and a_v == rule.l_limit:
                        ret, level, desc = True, "L", "指示灯状态异常" if rule.l_txt is None else rule.l_txt
                    elif rule.ll_is_bind and a_v == rule.ll_limit:
                        ret, level, desc = True, "LL", "指示灯状态异常" if rule.ll_txt is None else rule.ll_txt
                # TODO support more type AlarmRules
                # alm_of_av.append((ret, level, desc))
                alm_of_img.append((ret, level, desc))
            alarms.append(alm_of_img)
        return alarms
