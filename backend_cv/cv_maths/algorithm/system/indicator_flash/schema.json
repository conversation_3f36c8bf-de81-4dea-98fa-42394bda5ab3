{"algorithm": {"name": "指示灯闪烁识别", "code": "indicator_flash", "des": "指示灯闪烁识别.V1", "type": "openCV", "alarmSupport": true, "isOverrideAlarm": true, "batchSupport": true}, "images": ["e:/images/test.jpg"], "roi": {"name": "ROI区域", "type": "polygon", "des": "支持绘制的ROI区域类型, polygon:多边形, circle:圆, ellipse:椭圆", "value": [[325, 116], [331, 115], [346, 310], [337, 310]], "nullable": true}, "targetSceneImage": {"in_use": false, "uri": "", "name": "场景基准图"}, "inputParam": {"indicatorList": {"name": "指示灯列表", "type": "array", "value": [], "val_range": [{"circle": {"name": "指示灯截取圆", "type": "circle", "des": "指示灯所代表的圆", "value": {"center": [100, 200], "radius": 20}, "showable": true}, "color_picker": {"name": "灯亮颜色", "type": "color_picker", "value": [0, 255, 0], "des": "从图像中提取到的液体的颜色值(RGB)"}, "colorThreshold": {"name": "颜色偏差", "type": "number", "des": "颜色偏差,值越大颜色区间越大,可根据实际情况酌情调节", "value": 55, "val_range": [0, 255, 1, 55]}}], "desc": "指示灯列表"}, "threshold": {"name": "识别灵敏度", "type": "number", "des": "算识别灵敏度,灵敏度越高识别准确率越低,可根据实际情况酌情调节", "value": 0.42, "val_range": [0, 1, 0.01, 0.42], "nullable": true}}, "outputDefine": {"type": "switch", "desc": "指示灯亮灭", "val_enum": {"on": 1, "off": 0}, "ruleInfo": [{"desc": "指示灯状态异常", "isBind": false, "type": "HH", "val": null, "placeHolder": "大于等于时报警,1:亮,0:灭"}, {"desc": "指示灯状态异常", "isBind": false, "type": "H", "val": null, "placeHolder": "大于等于时报警,1:亮,0:灭"}, {"desc": "指示灯状态异常", "isBind": false, "type": "L", "val": null, "placeHolder": "小于等于时报警,1:亮,0:灭"}, {"desc": "指示灯状态异常", "isBind": false, "type": "LL", "val": null, "placeHolder": "小于等于时报警,1:亮,0:灭"}]}}