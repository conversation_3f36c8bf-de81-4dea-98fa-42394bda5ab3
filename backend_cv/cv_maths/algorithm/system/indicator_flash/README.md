## 指示灯闪烁识别算法
## 版本:

 v 1.0.0

## 描述:

 该算法用于指示灯闪烁识别，支持工厂以及实验室大多数指示灯闪烁识别。使用建议:

1) 指示灯颜色参数建议选取图片中灯亮后指示灯的颜色。
2) 颜色偏差不建议调整。

## 输入:

| 名称 | 类型 | 描述 |
|-------|--------------|----------------------|
| 指示灯截取圆 | circle | 圆，仪表包含指示灯在内的识别区域 |
| 灯亮颜色 | color_picker | 取色笔，指示灯亮的颜色 |
| 颜色偏差 | number | 数值，指的是指示灯颜色可变幅度 |
| 识别灵敏度 | number | 0-1之间的数值， 指的是算法识别的灵敏度，灵敏度越高识别准确率越低,可根据实际情况酌情调节 |

* 是否支持单图批量识别：是

## 输出：

| 名称 | 类型 | 描述 |
|-------|--------------|----------------------|
| 指示灯状态 | string | 指示灯是否亮着，亮为on， 关为off |
| 置信度 | number | 数值，0-1之间，代表识别出来指示灯状态的可信度 |