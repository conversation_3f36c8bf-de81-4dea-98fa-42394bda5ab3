from typing import Any

import cv2

from backend_common.maths.algorithm.base.algorithm_base import AlgorithmInput
from backend_common.utils.util import get_median_val_by_color, get_mask_by_color
from cv_maths.algorithm.system.indicator_flash.ab_indicator_flash import IndicatorFlashReader, IndicatorStatus


class IndicatorFlashReaderSub0(IndicatorFlashReader):
    """
    指示灯闪烁识别
    """

    # schema_path = os.path.join(os.path.dirname(__file__), "schema.json")

    # _name = "IndicatorFlashReaderSub0.Base.v1"
    _index = 0
    _description = "指示灯闪烁识别基于颜色值统计.主模型.版本v1"
    _cn_name = "指示灯闪烁识别算法(主模型)"

    AREA_THRESHOLD = 0.42

    def __init__(self, _inputs: AlgorithmInput, _id: int = -1):
        super(IndicatorFlashReaderSub0, self).__init__(_inputs, _id)

    def _preprocess(self) -> Any:  # TODO
        pass

    def _do_detect(self) -> (bool, Any, tuple):
        return super(IndicatorFlashReaderSub0, self)._do_detect()

    def _postprocess(self) -> Any:
        pass

    def get_values(self, param_dict, img, threshold):
        """
        基于连通域分析的指示灯状态识别

        基于观察到的基本事实： 亮灯状态下最大连通域面积大于灭灯状态下最大连通域面积
        """
        #  指示灯 初始用户配置信息
        cir = param_dict['circle']
        color_rgb = param_dict['color_picker']
        colorThreshold = int(param_dict['colorThreshold'])
        roi = self._get_roi(img, cir)

        # roi_hsv = cv2.cvtColor(roi, cv2.COLOR_BGR2HSV)

        # # 对图像进行直方图均衡化
        # roi = cv2.cvtColor(roi, cv2.COLOR_BGR2GRAY)
        # roi = cv2.equalizeHist(roi)

        # # 去对应颜色的中值
        # median_val = get_median_val_by_color(roi, roi_hsv, color_rgb)
        # # 阈值化
        # ret, binary = cv2.threshold(roi, median_val, 255,  cv2.THRESH_BINARY)

        # cv2.imshow("binary", binary)
        # cv2.waitKey()
        binary = get_mask_by_color(roi, color_rgb, colorThreshold, color_picker=True)
        # 连通区域分析
        # 连通性，可以取值为 4 或 8，表示分析时考虑像素的上下左右或上下左右和对角线方向上的连通关系。
        # stats 每个连通区域的统计信息，包括面积、左上角坐标、宽度、高度和中心点坐标等，格式为 numpy 数组，每一行对应一个连通区域。
        num_labels, labels, stats, _ = cv2.connectedComponentsWithStats(binary, connectivity=4, ltype=cv2.CV_32S)

        # 获取指示灯区域的统计信息
        max_area, (w, h) = 0, roi.shape[:2]
        # max_label = 0
        for i in range(1, num_labels):
            # 取其面积
            area = stats[i, cv2.CC_STAT_AREA]
            if area > max_area:
                max_area = area

        magic_factor = threshold if threshold is not None else self.AREA_THRESHOLD
        print("max_area==", max_area, f"{w}*{h}={w * h}", "亮" if max_area / (w * h) >= magic_factor else "灭")

        return IndicatorStatus.On.value[2] if max_area / (w * h) >= magic_factor else IndicatorStatus.Off.value[2]
