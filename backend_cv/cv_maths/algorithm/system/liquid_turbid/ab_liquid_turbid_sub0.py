from typing import Any

import cv2
import numpy as np

from backend_common.exceptions.inspection_exception import AlgorithmProcessException
from backend_common.maths.algorithm.base.algorithm_base import AlgorithmInput

from backend_common.constants.constant import LOWER, UPPER

from cv_maths.algorithm.system.liquid_turbid import LiquidTurbidReader


class LiquidTurbidReaderSub0(LiquidTurbidReader):
    """
    液体浑浊度检测算法
    """

    _index = 0
    _description = "液体浑浊度识别.主模型.版本v1"
    _cn_name = "液体浑浊度识别算法"

    def __init__(self, _inputs: AlgorithmInput, _id: int = -1):
        super(LiquidTurbidReaderSub0, self).__init__(_inputs, _id)

    def _preprocess(self) -> Any:  # TODO
        pass

    def _postprocess(self) -> Any:
        pass

    def _do_detect(self) -> (bool, Any, tuple):
        return super(LiquidTurbidReaderSub0, self)._do_detect()

    def get_circle_roi(self, img, center_coordinates, radius):
        # 黑色的背景图像
        circle = np.zeros(img.shape, dtype="uint8")

        # 背景黑色，白色圆形mask
        circle_mask = cv2.circle(circle, center_coordinates, radius, (255, 255, 255), -1)

        # 获取圆形ROI原始图像
        black_img = cv2.bitwise_and(img, circle_mask)

        x = center_coordinates[0] - radius
        y = center_coordinates[1] - radius
        w = radius * 2
        h = radius * 2

        # 获取圆外接矩形区域
        roi_img = black_img[y:y + h, x:x + w]
        return roi_img

    def get_turbid(self, roi_img):
        roi_hsv_img = cv2.cvtColor(roi_img, cv2.COLOR_BGR2HSV)

        color_mask = cv2.inRange(roi_hsv_img, LOWER[self.color_index], UPPER[self.color_index])

        if self.color_index == 0:
            color_mask_red = cv2.inRange(roi_hsv_img, LOWER[1], UPPER[1])
            color_mask = cv2.add(color_mask, color_mask_red)

        color_mask_number = len(color_mask[color_mask == 255])
        roi_area = 3.14 * int(self.radius) ** 2

        area_ratio = color_mask_number / int(roi_area)

        # 未检测到颜色区域
        if color_mask_number == 0:
            return -1

        roi_h_value = roi_hsv_img[:, :, 0][color_mask == 255].mean()
        roi_s_value = roi_hsv_img[:, :, 1][color_mask == 255].mean()
        roi_v_value = roi_hsv_img[:, :, 2][color_mask == 255].mean()

        hsv_value = roi_h_value + roi_s_value + roi_v_value
        tubid_value = int(hsv_value * area_ratio)
        return tubid_value

    def det_turbid(self, img, roi_square, color_select):
        center = roi_square['center']
        self.radius = int(roi_square['radius'])
        self.color_index = int(color_select)

        roi_img = self.get_circle_roi(img, center, self.radius)

        tubid = self.get_turbid(roi_img)
        return tubid
