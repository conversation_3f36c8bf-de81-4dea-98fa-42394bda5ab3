{"algorithm": {"name": "液体浑浊度", "code": "liquid_turbid", "des": "液体浑浊度.V1", "type": "openCV", "alarmSupport": true, "isOverrideAlarm": true, "batchSupport": false}, "roi": {"name": "ROI区域", "type": "polygon", "des": "支持绘制的ROI区域类型, polygon:多边形, circle:圆, ellipse:椭圆", "value": [[325, 116], [331, 115], [346, 310], [337, 310]], "nullable": true}, "targetSceneImage": {"in_use": false, "uri": "", "name": "场景基准图"}, "images": ["e:/images/test.jpg"], "inputParam": {"roiSquare": {"name": "圆心坐标及半径", "type": "circle", "value": {"center": [100, 200], "radius": 20}, "showable": true, "desc": "圆心坐标及半径"}, "color_select": {"name": "识别颜色", "type": "select", "des": "需识别的液体颜色", "val_range": {"0": "红色", "2": "绿色", "3": "蓝色", "4": "橙黄色", "5": "青色", "6": "紫色", "7": "黑色", "8": "白色", "9": "灰色"}}}, "outputDefine": {"type": "analogue", "desc": "液体浑浊度-模拟量", "ruleInfo": [{"desc": "液体浑浊度越高高限", "isBind": false, "type": "HH", "val": 0.0, "placeHolder": "数值;液体浑浊度识别结果"}, {"desc": "液体浑浊度越高限", "isBind": false, "type": "H", "val": 0.0, "placeHolder": "数值;液体浑浊度识别结果"}, {"desc": "液体浑浊度越低限", "isBind": false, "type": "L", "val": 0.0, "placeHolder": "数值;液体浑浊度识别结果"}, {"desc": "液体浑浊度越低低限", "isBind": false, "type": "LL", "val": 0.0, "placeHolder": "数值;液体浑浊度识别结果"}]}}