import importlib
import os
from typing import Any, List

from backend_common.constants.constant import *
from backend_common.constants.math_constants import ALGORITHM_CV_USER_DEFINE_PATH, ALGORITHM_CV_SYSTEM_INNER_PATH
from backend_common.exceptions.inspection_exception import AlgorithmProcessException, InspectionException
from backend_common.maths.algorithm.base.algorithm_base import AlgorithmInput, AlgorithmBase, AlgorithmStrategy, \
    AlgorithmSettings, AlgorithmDetail, AlgorithmBaseInfo
from backend_common.utils.util import circle2bbox


# 是否输出中间过程图片
WRITE_PROCESS_IMAGE = False
# 是否show中间过程图片
SHOW_PROCESS_IMAGE = False

SUFFIX_START = "sub"


class LiquidTurbidReader(AlgorithmBase):
    """
    液体浑浊度识别算法
    """
    schema_path = os.path.join(os.path.dirname(__file__), "schema.json")

    # _name算法块唯一标识名,须与算法块文件夹名称一致
    _name = "liquid_turbid"
    _description = "液体浑浊度识别算法.基础.版本v1"
    _cn_name = "液体浑浊度识别算法"

    # 支持的算法分类个数
    __supported_classify = (AlgorithmClassifyEnum.Primary,)

    # 支持的算法策略，primary -> main & secondary -> main
    __supported_strategies = [
        AlgorithmStrategy(__supported_classify[0], AlgorithmStrategyEnum.Main, "液体浑浊度识别策略",
                          "液体浑浊度识别主策略", kv_param={})
    ]

    # 支持的算法参数，若干
    __supported_params = AlgorithmBase.json_schema_2_algorithm_params(schema_path)

    def __init__(self, _inputs: AlgorithmInput, _id: int = -1):
        super(LiquidTurbidReader, self).__init__(self.__class__._name, self.__class__._description,
                                                self.__class__._cn_name,
                                                self.__class__.__supported_strategies,
                                                self.__class__.__supported_params)

        if _inputs.__eq__(dict()):
            return

        self._local_var._inputs = _inputs
        self._local_var._settings = None

        # 支持获取多张输入图片
        self.origin_imgs = self._get_input_images()

        # 算法输入参数
        self.roi_square = self._get_input_value_by_name("roiSquare")
        self.color_select = self._get_input_value_by_name("color_select")

        # 结果展示参数
        self._local_var._processFinalRet = [[] for _ in self.origin_imgs]
        self._local_var._processFinalRect = [[] for _ in self.origin_imgs]

    def _supported_classify(self):
        return self.__supported_classify

    def _supported_strategy(self):
        return self.__supported_strategies

    def _supported_params(self):
        return self.__supported_params

    def get_algorithm_detail(self, a_type) -> AlgorithmDetail:

        schema_base = AlgorithmBase._get_algorithm_schema_info(self.schema_path, "algorithm")
        info = AlgorithmBaseInfo(self._id, self._name, self._name, self.cn_name, self._description,
                                 batch_support=schema_base.get("batchSupport"),
                                 alarm_support=schema_base.get("alarmSupport"),
                                 is_override_alarm=schema_base.get("isOverrideAlarm"))

        classifies = [self._classify2info(c) for c in self.__supported_classify]
        strategies = [self._strategy2info(s) for s in self.__supported_strategies]
        roi = self._get_roi_define(self.schema_path)
        scene_image = self._get_scene_image(self.schema_path)
        params = AlgorithmBase.json_schema_2_algorithm_params(self.schema_path)
        output_define = self._output_define2info(
            AlgorithmBase._get_algorithm_schema_info(self.schema_path, "outputDefine"))
        sub_list = self.load_algorithm_sublist(self._name, a_type, 'cv')

        return AlgorithmDetail(info, classifies, strategies, roi, scene_image, params, output_define, sub_list)

    def determine_algorithm_instance(self, settings: AlgorithmSettings = None, index: int = 0,
                                     a_type: str = "system_inner"):
        suffix = f"{SUFFIX_START}{index}"
        module_name = f".{self._name}.ab_{self._name}_{suffix}"

        base_path = ALGORITHM_CV_SYSTEM_INNER_PATH
        if a_type == "user_define":
            base_path = ALGORITHM_CV_USER_DEFINE_PATH

        try:
            M = importlib.import_module(module_name, base_path)
        except ModuleNotFoundError:
            raise InspectionException(f"Auto-load module '{module_name}' from '{base_path}' \
                                failure, please check it manually")

        # !!!!  找出子类中名称以当前 suffix.capitalize()  为 END 的作为 target 算法类  !!!!
        target_clazz_ = [clazz for clazz in LiquidTurbidReader.__subclasses__()
                         if clazz.__name__.endswith(suffix.capitalize())][0]

        algorithm_instance: LiquidTurbidReader = target_clazz_(self._local_var._inputs, self._id)
        algorithm_instance._settings = settings

        return algorithm_instance

    def _preprocess(self) -> Any:
        pass

    def _postprocess(self) -> Any:
        pass

    def _do_detect(self) -> (bool, Any, tuple):
        """
        调用入口函数
        return:
            ret: 检测是否成功
            val: 数值化结果（颜色浑浊度）
            points: 绘制输入圆形
        """

        temp, bboxs = [], []
        for img_id, img in enumerate(self.origin_imgs):
            res = self.det_turbid(img, self.roi_square, self.color_select)
            temp.append(res)
            bboxs.append([circle2bbox(self.roi_square), FAKE_PERCENT])

            self._local_var._processFinalRet[img_id] = temp
            self._local_var._processFinalRect[img_id] = bboxs

        return True, self._local_var._processFinalRet, self._local_var._processFinalRect

    def _gen_result_img(self) -> List[np.ndarray]:
        pass

    def det_turbid(self, img, roi_square, color_select):
        """
        检测液体浑浊度
        """
        raise InspectionException("you must implement this function by yourself!")
