{"algorithm": {"name": "二维码识别算法", "code": "qrcode", "des": "二维码识别算法.V1", "type": "openCV", "alarmSupport": false, "isOverrideAlarm": false, "batchSupport": false}, "images": ["e:/images/test.jpg"], "roi": {"name": "ROI区域", "type": "polygon", "des": "支持绘制的ROI区域类型, polygon:多边形, circle:圆, ellipse:椭圆", "value": [[325, 116], [331, 115], [346, 310], [337, 310]], "nullable": true}, "targetSceneImage": {"in_use": false, "uri": "", "name": "场景基准图"}, "inputParam": {"bar": {"name": "二维码区域", "type": "square", "value": [[325, 116], [331, 115], [346, 310], [337, 310]], "showable": false, "des": "二维码区域,[左上，左下，右下、右上]"}}, "outputDefine": {"type": "string", "desc": "可读文本", "ruleInfo": [{"desc": "", "isBind": false, "type": "HH", "val": 0.0, "placeHolder": "数值"}, {"desc": "", "isBind": false, "type": "H", "val": 0.0, "placeHolder": "数值"}, {"desc": "", "isBind": false, "type": "L", "val": 0.0, "placeHolder": "数值"}, {"desc": "", "isBind": false, "type": "LL", "val": 0.0, "placeHolder": "数值"}]}}