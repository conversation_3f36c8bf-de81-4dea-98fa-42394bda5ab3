{"algorithmMetadata": {"name": "二维码", "code": "qrcode", "version": "1.0.0", "description": "二维码识别算法.V1", "type": "openCV", "isBatchProcessing": false, "roiDrawType": "SQUARE", "roiRequired": false, "classification": "DEVICE_METER"}, "input": [{"key": "bar", "label": "二维码区域", "dataType": "SQUARE", "constraints": {"required": true}, "drawToOsd": false}, {"key": "resultDes", "label": "结果描述", "dataType": "STRING", "constraints": {"required": false}, "drawToOsd": true}], "output": [{"key": "qrcodeResult", "label": "二维码", "dataType": "STRING"}]}