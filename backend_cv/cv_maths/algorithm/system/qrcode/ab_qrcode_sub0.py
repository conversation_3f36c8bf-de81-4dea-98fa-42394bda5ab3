from typing import Any, List

import numpy as np

from backend_common.maths.algorithm.base.algorithm_base import AlgorithmInput
from cv_maths.algorithm.system.qrcode import QrcodeReader


class QrcodeReaderSub0(QrcodeReader):
    """
    二维码识别算法
    """

    def _alarm_trig(self, detect_ret) -> List[List]:
        return [[[False, None, None]]]

    _index = 0
    _description = "二维码识别.主模型.版本v1"
    _cn_name = "二维码识别算法"

    def __init__(self, _inputs: AlgorithmInput, _id: int = -1):
        super(QrcodeReaderSub0, self).__init__(_inputs, _id)

    def _preprocess(self) -> Any:  # TODO
        super(QrcodeReaderSub0, self)._preprocess()

    def _do_detect(self) -> (bool, Any, tuple):
        return super(QrcodeReaderSub0, self)._do_detect()

    def _postprocess(self) -> Any:
        pass

    def _gen_result_img(self) -> List[np.ndarray]:
        return super(QrcodeReaderSub0, self)._gen_result_img()
