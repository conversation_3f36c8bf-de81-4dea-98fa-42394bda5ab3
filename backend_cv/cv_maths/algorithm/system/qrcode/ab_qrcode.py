from typing import Any

import cv2
import numpy as np
from pyzbar import pyzbar

from backend_common.exceptions.inspection_exception import AlgorithmProcessException
from backend_common.maths.algorithm.base.algorithm_base import AlgorithmBase, AlgorithmInput
from backend_common.utils.util import RectUtils


class QrcodeReader(AlgorithmBase):
    """
    二维码识别算法
    """
    _name = "qrcode"

    def __init__(self, _inputs: AlgorithmInput, _id: int = -1):
        super(QrcodeReader, self).__init__(self.__class__._name)

        if _inputs.__eq__(dict()):
            return

        self._local_var._inputs = _inputs
        # 算法实例参数
        self._local_var._settings = None
        self.originImg = self._get_input_images()[0]
        self.roi = self._get_roi()
        input_param = self._get_input_param()

        # 划定的识别矩形区域
        self._barSquare = [list(k.values()) for k in input_param["bar"]]
        self.resultDes = input_param['resultDes']

    def _do_detect(self) -> (bool, Any, tuple):
        # ret = [self.barcode(gray) for gray in self._processImagesGray]
        gray = self._preprocess(self.originImg)
        ret = self.barcode(gray)[0]
        records = ret.data.decode()
        points = RectUtils.rect_to_4_points_dict(ret.rect)
        if self.resultDes is not None:
            result = {"value":records, "resultDes": self.resultDes}
        else:
            result = {"value":records}
        return True, {"qrcodeResult": result}, [{"dataType": "SQUARE","textObj": result,"coords": points}]

    def _preprocess(self, image) -> Any:  # TODO
        # def do_(image):
        mask = np.ones(image.shape, dtype="uint8") * 255
        roi_corners = np.array([self._barSquare], dtype=np.int32)
        cv2.fillPoly(mask, roi_corners, (0, 0, 0))
        image = cv2.bitwise_or(image, mask)

        return cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

    def _decode(self, gray):
        # 先尝试直接识别
        codes = pyzbar.decode(gray)
        if not codes:
            # 可能识别不到，为了提高识别率，调整图像二值化阈值, 多试几次
            thre = 30
            while len(codes) == 0 and thre < 200:
                ret, thresh = cv2.threshold(gray, thre, 255, cv2.THRESH_BINARY)
                codes = pyzbar.decode(thresh)
                thre = thre + 10
        return codes

    def barcode(self, gray):
        """
        二维码识别入口
        """
        codes = self._decode(gray)
        if not codes:
            angle0 = self.barcode_angle(gray)
            if angle0 < -45:
                angle = -90 - angle0
                # 旋转后再次识别
                codes = self.bar(gray, angle)

                if not codes:
                    # 还不能识别就 对灰度图进行一定程度修剪然后再识别
                    gray = np.clip((1.1 * gray + 10), 0, 255)  # np.uint8 ??
                    codes = self.bar(gray, angle)
        if len(codes)==0:
            raise AlgorithmProcessException("抱歉，没有识别到二维码结果，请再次尝试")
        return codes

    def bar(self, gray, angle):
        """
        先仿射变换而后识别
        """
        bar = self.rotate_bound(gray, 0 - angle)
        texts = self._decode(bar)
        return texts

    def barcode_angle(self, gray):
        """
        获取barcode 的 最小外接矩形， 并返回其旋转角度
        :param gray 灰度图
        """
        ret, binary = cv2.threshold(gray, 220, 255, cv2.THRESH_BINARY_INV)
        kernel = np.ones((8, 8), np.uint8)
        dilation = cv2.dilate(binary, kernel, iterations=1)
        erosion = cv2.erode(dilation, kernel, iterations=3)
        contours, hierarchy = cv2.findContours(erosion, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_NONE)
        if len(contours) == 0:
            rect = [0, 0, 0]
        else:
            rect = cv2.minAreaRect(contours[0])
        return rect[2]

    def rotate_bound(self, image, angle):
        """
        将图像按照角度进行仿射变换, 方便进一步识别
        :param image 图像
        :param angle 旋转角度
        """

        (h, w) = image.shape[:2]
        (cX, cY) = (w // 2, h // 2)
        M = cv2.getRotationMatrix2D((cX, cY), -angle, 1.0)
        cos = np.abs(M[0, 0])
        sin = np.abs(M[0, 1])
        nW = int((h * sin) + (w * cos))
        nH = int((h * cos) + (w * sin))
        M[0, 2] += (nW / 2) - cX
        M[1, 2] += (nH / 2) - cY
        return cv2.warpAffine(image, M, (nW, nH))
