import importlib
import os
from typing import Any, List

import cv2
import numpy as np
from pyzbar import pyzbar

from backend_common.constants.constant import AlgorithmClassifyEnum, AlgorithmStrategyEnum, FAKE_PERCENT
from backend_common.constants.math_constants import ALGORITHM_CV_USER_DEFINE_PATH, ALGORITHM_CV_SYSTEM_INNER_PATH
from backend_common.utils.util import RectUtils, render_text_on_bbox
from backend_common.exceptions.inspection_exception import InspectionException
from backend_common.maths.algorithm.base.algorithm_base import AlgorithmBase, AlgorithmStrategy, AlgorithmSettings, AlgorithmDetail, \
    AlgorithmBaseInfo, AlgorithmInput

SUFFIX_START = "sub"


class QrcodeReader(AlgorithmBase):
    """
    二维码识别算法
    """

    schema_path = os.path.join(os.path.dirname(__file__), "schema.json")

    _name = "qrcode"
    _description = "二维码识别.基础.版本v1"
    _cn_name = "二维码识别算法"

    # 支持的算法分类个数
    __supported_classify = (AlgorithmClassifyEnum.Primary,)
    # 支持的算法策略，primary -> main & secondary -> main
    __supported_strategies = [
        AlgorithmStrategy(__supported_classify[0], AlgorithmStrategyEnum.Main, "二维码主识别策略",
                          "二维码识别识别主策略", kv_param={}),
        # AlgorithmStrategy(__supported_classify[1], AlgorithmStrategyEnum.Main, "二维码主识别策略",
        #                   "二维码识别识别主策略", kv_param={})
    ]

    # 支持的算法参数，若干
    __supported_params = AlgorithmBase.json_schema_2_algorithm_params(schema_path)

    def __init__(self, _inputs: AlgorithmInput, _id: int = -1):
        super(QrcodeReader, self).__init__(self.__class__._name, self.__class__._description,
                                           self.__class__._cn_name,
                                           self.__class__.__supported_strategies,
                                           self.__class__.__supported_params)

        if _inputs.__eq__(dict()):
            return

        self._local_var._inputs = _inputs
        # 算法实例参数
        self._local_var._settings = None

        self._inputImages = self._get_input_images()
        # 划定的识别矩形区域
        self._barSquare = self._get_input_value_by_name("bar")

        self._processImagesGray = None
        self._processFinalRet = []
        self._processFinalRect = []
        self._processOutputDir = os.getcwd()

    def _supported_classify(self):
        return self.__supported_classify

    def _supported_strategy(self):
        return self.__supported_strategies

    def _supported_params(self):
        return self.__supported_params

    def _preprocess(self) -> Any:  # TODO
        def do_(image):
            mask = np.ones(image.shape, dtype="uint8") * 255
            roi_corners = np.array([self._barSquare], dtype=np.int32)
            # 黑
            cv2.fillPoly(mask, roi_corners, (0, 0, 0))
            image = cv2.bitwise_or(image, mask)

            return cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

        self._processImagesGray = [do_(image) for image in self._inputImages]

    def _do_detect(self) -> (bool, Any, tuple):
        ret = [self.barcode(gray) for gray in self._processImagesGray]

        # # 二维数组 依次按每张图输出识别结果
        # for img in self._processImagesGray:
        #     cv2.imwrite("ss.jpg", img)

        for decodes_of_each_img in ret:
            records = [decode.data.decode() for decode in decodes_of_each_img]
            # zbar 的 zbar_symbol_get_quality 返回的 质量位 不可信且可能位0，所以不用 decode.quality
            points = [[RectUtils.rect_to_4_points(decode.rect), FAKE_PERCENT] for decode in decodes_of_each_img]
            self._processFinalRet.append(records)
            self._processFinalRect.append(points)
        # TODO 仅支持一张图一个码
        return True, self._processFinalRet, self._processFinalRect

    def _postprocess(self) -> Any:
        pass

    def _gen_result_img(self) -> List[np.ndarray]:
        assert self._processFinalRet is not None
        assert len(self._processFinalRet) == len(self._processFinalRect)

        ret_images = []
        for img_idx, img in enumerate(self._processImagesGray):
            ret_img = self._inputImages[img_idx]
            for code_idx, code in enumerate(self._processFinalRet[img_idx]):
                bbox = self._processFinalRect[img_idx][code_idx][0]  # [bbox, percent]
                # 取 bbox 左上角点
                # ret_img = cv2_put_chinese_txt(ret_img, code, bbox[0], (60, 255, 0))
                box = (bbox[0][0], bbox[0][1], bbox[2][0], bbox[2][1])
                text = f'{code} {"%.2f" % FAKE_PERCENT}'
                ret_img = render_text_on_bbox(ret_img, box, text)
                ret_images.append(ret_img)
        return ret_images

    def determine_algorithm_instance(self, settings: AlgorithmSettings = None, index: int = 0,
                                     a_type: str = "system_inner"):
        suffix = f"{SUFFIX_START}{index}"
        module_name = f".{self._name}.ab_{self._name}_{suffix}"

        base_path = ALGORITHM_CV_SYSTEM_INNER_PATH
        if a_type == "user_define":
            base_path = ALGORITHM_CV_USER_DEFINE_PATH

        try:
            M = importlib.import_module(module_name, base_path)
        except ModuleNotFoundError:
            raise InspectionException(f"Auto-load module '{module_name}' from '{base_path}' \
                                failure, please check it manually")

        # !!!!  找出子类中名称以当前 suffix.capitalize()  为 END 的作为 target 算法类  !!!!
        target_clazz_ = [clazz for clazz in QrcodeReader.__subclasses__()
                         if clazz.__name__.endswith(suffix.capitalize())][0]

        algorithm_instance: QrcodeReader = target_clazz_(self._local_var._inputs, self._id)
        algorithm_instance._settings = settings

        return algorithm_instance

    def get_algorithm_detail(self, a_type) -> AlgorithmDetail:

        schema_base = AlgorithmBase._get_algorithm_schema_info(self.schema_path, "algorithm")
        info = AlgorithmBaseInfo(self._id, self._name, self._name, self.cn_name, self._description,
                                 batch_support=schema_base.get("batchSupport"),
                                 alarm_support=schema_base.get("alarmSupport"),
                                 is_override_alarm=schema_base.get("isOverrideAlarm"))
        classifies = [self._classify2info(c) for c in self.__supported_classify]
        strategies = [self._strategy2info(s) for s in self.__supported_strategies]
        roi = self._get_roi_define(self.schema_path)
        scene_image = self._get_scene_image(self.schema_path)
        params = AlgorithmBase.json_schema_2_algorithm_params(self.schema_path)  # 重新加载一下, 可能会有刷新
        output_define = self._output_define2info(
            AlgorithmBase._get_algorithm_schema_info(self.schema_path, "outputDefine"))
        sub_list = self.load_algorithm_sublist(self._name, a_type, 'cv')

        return AlgorithmDetail(info, classifies, strategies, roi, scene_image, params, output_define, sub_list)

    def _decode(self, gray):
        # 先尝试直接识别
        codes = pyzbar.decode(gray)
        if not codes:
            # 可能识别不到，为了提高识别率，调整图像二值化阈值, 多试几次
            thre = 30
            while len(codes) == 0 and thre < 200:
                ret, thresh = cv2.threshold(gray, thre, 255, cv2.THRESH_BINARY)
                codes = pyzbar.decode(thresh)
                thre = thre + 10
        return codes

    def barcode(self, gray):
        """
        二维码识别入口
        """
        codes = self._decode(gray)
        if not codes:
            angle0 = self.barcode_angle(gray)
            if angle0 < -45:
                angle = -90 - angle0
                # 旋转后再次识别
                codes = self.bar(gray, angle)

                if not codes:
                    # 还不能识别就 对灰度图进行一定程度修剪然后再识别
                    gray = np.clip((1.1 * gray + 10), 0, 255)  # np.uint8 ??
                    codes = self.bar(gray, angle)
        return codes

    def bar(self, gray, angle):
        """
        先仿射变换而后识别
        """
        bar = self.rotate_bound(gray, 0 - angle)
        # roi = cv2.cvtColor(bar, cv2.COLOR_BGR2RGB)
        texts = self._decode(bar)
        return texts

    def barcode_angle(self, gray):
        """
        获取barcode 的 最小外接矩形， 并返回其旋转角度
        :param gray 灰度图
        """
        ret, binary = cv2.threshold(gray, 220, 255, cv2.THRESH_BINARY_INV)
        kernel = np.ones((8, 8), np.uint8)
        dilation = cv2.dilate(binary, kernel, iterations=1)
        erosion = cv2.erode(dilation, kernel, iterations=3)
        contours, hierarchy = cv2.findContours(erosion, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_NONE)
        if len(contours) == 0:
            rect = [0, 0, 0]
        else:
            rect = cv2.minAreaRect(contours[0])
        return rect[2]

    def rotate_bound(self, image, angle):
        """
        将图像按照角度进行仿射变换, 方便进一步识别
        :param image 图像
        :param angle 旋转角度
        """

        (h, w) = image.shape[:2]
        (cX, cY) = (w // 2, h // 2)
        M = cv2.getRotationMatrix2D((cX, cY), -angle, 1.0)
        cos = np.abs(M[0, 0])
        sin = np.abs(M[0, 1])
        nW = int((h * sin) + (w * cos))
        nH = int((h * cos) + (w * sin))
        M[0, 2] += (nW / 2) - cX
        M[1, 2] += (nH / 2) - cY
        return cv2.warpAffine(image, M, (nW, nH))

    # def _alarm_trig(self, detect_ret) -> List[List]:
    #     raise InspectionException("Alarm unsupported")
