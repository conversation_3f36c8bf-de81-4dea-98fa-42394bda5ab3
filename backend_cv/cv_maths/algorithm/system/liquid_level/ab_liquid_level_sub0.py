from typing import Any, List
from typing import final

import cv2
from loguru import logger

from backend_common.constants.constant import *
from backend_common.constants.math_constants import DEFAULT_DECIMAL_PLACES
from backend_common.exceptions.inspection_exception import AlgorithmProcessException
from backend_common.maths.algorithm.base.algorithm_base import AlgorithmInput
from backend_common.utils.util import Functions, get_mask_by_color
from cv_maths.algorithm.system.liquid_level import <PERSON>quidLevelReader
# from utils import NumberUtil


@final
class LiquidLevelReaderSub0(LiquidLevelReader):
    """
    液位识别
    """

    # _name = "LiquidLevelReader.primary.v1"
    _index = 0
    _description = "识别算法.主模型.版本v1"
    _cn_name = "液位识别算法(主模型)"

    def __init__(self, _inputs: AlgorithmInput, _id: int = -1):
        super(LiquidLevelReaderSub0, self).__init__(_inputs, _id)

        self._originImg = self._get_input_images(first_only=True)
        self._rangeDown = self._get_input_value_by_name("range")[0]
        self._rangeUp = self._get_input_value_by_name("range")[1]
        self.liquid_color_rgb = self._get_input_value_by_name("color_picker")
        self.colorThreshold = self._get_input_value_by_name("colorThreshold")
        self.liquid_roi = np.array(self._get_input_value_by_name("column"))

        self.resultScale = int(self._get_input_value_by_name("resultScale"))

        self._columnLeftTop = self.liquid_roi[0]
        self._columnLeftBottom = self.liquid_roi[1]
        self._columnRightBottom = self.liquid_roi[2]
        self._columnRightTop = self.liquid_roi[3]

    def _do_detect(self) -> (bool, Any, tuple):
        """
        核心检测算法
        return:
            ret: 检测是否成功
            val: 数值化结果(如果有,若没有留空)
            points: 检测到的关键结果点集(具体每个点含义由各算法自行约定)

        """
        self._processFinalNum, self._processFinalLiquid = self.liquid_detect()
        if self._processFinalNum is None:
            logger.info(f"检测异常")
            return False, None, None
        else:
            if self.resultScale == 0:
                self._processFinalNum = int(self._processFinalNum)
            else:
                self._processFinalNum = round(self._processFinalNum, self.resultScale)
            points = [self._get_input_value_by_name("column"), FAKE_PERCENT,
                      [['square'] + [self._processFinalLiquid.tolist()]]]
            return True, [[self._processFinalNum]], [[points]]

    def _gen_result_img(self) -> List[np.ndarray]:
        """
          生成结果图像
          return:
            结果图像
        """
        assert self._processFinalLiquid is not None

        temp = self._originImg.copy()
        p_color, t_color, a_color = (0, 0, 255), (60, 255, 0), (0, 255, 255)

        # box = cv2.boxPoints(cv2.minAreaRect(self._processFinalArea))
        # cv2.fillPoly(temp, np.array([self._processFinalLiquid], dtype=np.int32), a_color)

        # TODO 只能支持一张图
        return [cv2.putText(temp, "{:.1f}".format(self._processFinalNum), self._columnLeftTop,
                            cv2.FONT_HERSHEY_PLAIN, 3,
                            t_color, thickness=3)]

    def liquid_detect(self):
        # 仪表盘切分
        roi_img = self.img_cut_circle(self._originImg)
        # cv2.imwrite('/opt/tjh/model_server/cv_device_img/仪表盘.jpg', roi_img)
        # 根据指针颜色mask
        # color_mask = self.edgeFilter(roi_img)
        color_mask = get_mask_by_color(roi_img, self.liquid_color_rgb, self.colorThreshold, color_picker=True)
        color_mask = cv2.dilate(color_mask, kernel=np.ones((8, 8), np.uint8), iterations=1)
        # cv2.imwrite('/opt/tjh/model_server/cv_device_img/mask.jpg', color_mask)
        # 寻找最大连通域并返回液位坐标
        liquid_box, res = self.largest_connect_component(color_mask)
        if not res:
            print("抱歉，没有识别到液位，请再次尝试")
            raise AlgorithmProcessException("没有识别到液位，请调整参数后再试")

        liquid_res = self.get_liquid_num(liquid_box)
        if liquid_res < self._rangeDown or liquid_res > self._rangeUp:
            print("抱歉，识别到的液位不在量程范围，请调整参数后再试")
            raise AlgorithmProcessException("抱歉，识别到的液位不在量程范围，请调整参数后再试")
        return liquid_res, liquid_box

    def img_cut_circle(self, img):
        # 图白圆黑
        rectangle = np.ones(img.shape, dtype="uint8")
        rectangle = rectangle * 255
        # cv2.circle(circle, self.circleCent, self.circleRadius, 0, -1)
        # cv2.rectangle(rectangle, self.roi[0], self.roi[2], 0, -1)
        cv2.polylines(rectangle, [self.liquid_roi], True, 0, 1)
        cv2.fillPoly(rectangle, [self.liquid_roi], 0)
        self.bitwiseOr = cv2.bitwise_or(img, rectangle)
        return self.bitwiseOr

    @staticmethod
    def largest_connect_component(mask_edge):
        from skimage import measure
        labeled_img, num = measure.label(mask_edge, background=0, return_num=True)
        if num == 0:
            return [], 0
        # visual_image= color.label2rgb(labeled_img)
        # cv2.imwrite(os.path.join(self.out_dir, 'ConnectComponent.jpg'), visual_image)
        props = measure.regionprops(labeled_img)

        numPix = []
        for ia in range(len(props)):
            numPix += [props[ia].area]

        # 像素最多的连通区域及其指引
        maxnum = max(numPix)
        index = numPix.index(maxnum)

        largestConnectComponent = mask_edge.copy()
        largestConnectComponent[labeled_img != props[index].label] = 0
        # cv2.imwrite('/opt/tjh/model_server/cv_device_img/ConnectComponent.jpg', largestConnectComponent)
        # bbox = props[index].bbox
        minRect = cv2.minAreaRect(props[index].coords)
        ##最小矩形框的四个点坐标（左上，左下，右下，右上）
        box = np.int0(cv2.boxPoints(minRect))
        box[:, [0, 1]] = box[:, [1, 0]]
        xmin = int(np.min(box[:, 0]))
        xmax = int(np.max(box[:, 0]))
        ymin = int(np.min(box[:, 1]))
        ymax = int(np.max(box[:, 1]))
        box = np.array([[xmin, ymin], [xmin, ymax], [xmax, ymax], [xmax, ymin]])

        '''
        bbox = props[index].bbox
        bbox_left_top_point = (bbox[1], bbox[0])
        def intersection_point(point1, point2, bbox_left_top_point):
            a = (point1[1] - point2[1]) / (point1[0] - point2[0])
            b = point1[1] - a * point1[0]
            y = a * bbox_left_top_point[0] + b
            return (bbox_left_top_point[0], int(y))
        liquid_point = intersection_point(self.endPoint, self.startPoint, bbox_left_top_point)
        '''
        return box, 1

    def get_liquid_num(self, liquid_box):
        self.top_middle_point = Functions.get_middle_point(self._columnLeftTop, self._columnRightTop)
        self.bottom_middle_point = Functions.get_middle_point(self._columnLeftBottom, self._columnRightBottom)
        level_length_range = Functions.distances(self.top_middle_point, self.bottom_middle_point)

        liquid_middle_point = Functions.get_middle_point(liquid_box[0], liquid_box[3])
        liquid_length = Functions.distances(self.bottom_middle_point, liquid_middle_point)

        full_range = abs(self._rangeDown - self._rangeUp)
        # liquid_res = liquid_length * self.divisionValue
        liquid_res = liquid_length / level_length_range * full_range + self._rangeDown

        return liquid_res
