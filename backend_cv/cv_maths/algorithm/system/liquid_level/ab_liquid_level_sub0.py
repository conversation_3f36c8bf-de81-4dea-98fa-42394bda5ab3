from typing import Any, List
from typing import final

import cv2
from loguru import logger

from backend_common.constants.constant import *
from backend_common.exceptions.inspection_exception import AlgorithmProcessException
from backend_common.utils.util import Functions, get_mask_by_color


@final
class LiquidLevelReaderSub0():
    """
    液位识别1
    """

    def _do_detect(self, originImg, liquid_roi, liquid_color_rgb, colorThreshold, columnLeftTop, columnRightTop, columnLeftBottom, columnRightBottom, rangeDown, rangeUp, resultDes, unit, resultPrecision):
        """
        核心检测算法
        return:
            isSuccess: 检测是否成功
            output: 数值化结果(如果有,若没有留空)
            osdInfo: 需要绘制到osd上面的参数(具体由各算法自行约定)

        """
        processFinalNum, processFinalLiquid = self.liquid_detect(originImg, liquid_roi, liquid_color_rgb, colorThreshold, columnLeftTop, columnRightTop, columnLeftBottom, columnRightBottom, rangeDown, rangeUp)
        # points = [liquid_roi.tolist(), FAKE_PERCENT, [['square'] + [processFinalLiquid.tolist()]]]
        liquid_roi = self.box2points(liquid_roi.tolist())
        processFinalLiquid = self.box2points(processFinalLiquid.tolist())
        result = {"value": processFinalNum}
        textObj = {"value": processFinalNum}
        if resultPrecision is not None:
            result.update({"resultPrecision":resultPrecision})
            textObj.update({"resultPrecision":resultPrecision})
        if resultDes is not None:
            result.update({"resultDes":resultDes})
        if unit is not None:
            result.update({"unit":unit})
            textObj.update({"unit":unit})
        osdCoor = {"dataType": "SQUARE","textObj": textObj, "coords": liquid_roi}
        return True, {"liquidLevel": result}, [osdCoor,{"dataType": "SQUARE","coords": processFinalLiquid}]

    def liquid_detect(self, originImg, liquid_roi, liquid_color_rgb, colorThreshold, columnLeftTop, columnRightTop, columnLeftBottom, columnRightBottom, rangeDown, rangeUp):
        # 仪表盘切分
        roi_img = self.img_cut_circle(originImg, liquid_roi)
        # cv2.imwrite('/opt/tjh/model_server/cv_device_img/仪表盘.jpg', roi_img)
        # 根据指针颜色mask
        # color_mask = self.edgeFilter(roi_img)
        color_mask = get_mask_by_color(roi_img, liquid_color_rgb, colorThreshold, color_picker=True)
        color_mask = cv2.dilate(color_mask, kernel=np.ones((8, 8), np.uint8), iterations=1)
        # cv2.imwrite('/opt/tjh/model_server/cv_device_img/mask.jpg', color_mask)
        # 寻找最大连通域并返回液位坐标
        liquid_box, res = self.largest_connect_component(color_mask)
        if not res:
            logger.error("算法一没有识别到液位，请再次尝试")
            raise AlgorithmProcessException("没有识别到液位，请调整参数后再试")

        liquid_res = self.get_liquid_num(liquid_box, columnLeftTop, columnRightTop, columnLeftBottom, columnRightBottom, rangeDown, rangeUp)
        if liquid_res < rangeDown or liquid_res > rangeUp:
            logger.error("算法一识别到的液位不在量程范围，请调整参数后再试")
            raise AlgorithmProcessException("识别到的液位不在量程范围，请调整参数后再试")
        return liquid_res, liquid_box

    def img_cut_circle(self, img, liquid_roi):
        # 图白圆黑
        rectangle = np.ones(img.shape, dtype="uint8")
        rectangle = rectangle * 255
        # cv2.circle(circle, self.circleCent, self.circleRadius, 0, -1)
        # cv2.rectangle(rectangle, self.roi[0], self.roi[2], 0, -1)
        cv2.polylines(rectangle, [liquid_roi], True, 0, 1)
        cv2.fillPoly(rectangle, [liquid_roi], 0)
        bitwiseOr = cv2.bitwise_or(img, rectangle)
        return bitwiseOr

    @staticmethod
    def largest_connect_component(mask_edge):
        from skimage import measure
        labeled_img, num = measure.label(mask_edge, background=0, return_num=True)
        if num == 0:
            return [], 0
        # visual_image= color.label2rgb(labeled_img)
        # cv2.imwrite(os.path.join(self.out_dir, 'ConnectComponent.jpg'), visual_image)
        props = measure.regionprops(labeled_img)

        numPix = []
        for ia in range(len(props)):
            numPix += [props[ia].area]

        # 像素最多的连通区域及其指引
        maxnum = max(numPix)
        index = numPix.index(maxnum)

        largestConnectComponent = mask_edge.copy()
        largestConnectComponent[labeled_img != props[index].label] = 0
        # cv2.imwrite('/opt/tjh/model_server/cv_device_img/ConnectComponent.jpg', largestConnectComponent)
        # bbox = props[index].bbox
        minRect = cv2.minAreaRect(props[index].coords)
        ##最小矩形框的四个点坐标（左上，左下，右下，右上）
        box = np.int0(cv2.boxPoints(minRect))
        box[:, [0, 1]] = box[:, [1, 0]]
        xmin = int(np.min(box[:, 0]))
        xmax = int(np.max(box[:, 0]))
        ymin = int(np.min(box[:, 1]))
        ymax = int(np.max(box[:, 1]))
        box = np.array([[xmin, ymin], [xmin, ymax], [xmax, ymax], [xmax, ymin]])
        return box, 1

    def get_liquid_num(self, liquid_box, columnLeftTop, columnRightTop, columnLeftBottom, columnRightBottom, rangeDown, rangeUp):
        top_middle_point = Functions.get_middle_point(columnLeftTop, columnRightTop)
        bottom_middle_point = Functions.get_middle_point(columnLeftBottom, columnRightBottom)
        level_length_range = Functions.distances(top_middle_point, bottom_middle_point)

        liquid_middle_point = Functions.get_middle_point(liquid_box[0], liquid_box[3])
        liquid_length = Functions.distances(bottom_middle_point, liquid_middle_point)

        full_range = abs(rangeDown - rangeUp)
        # liquid_res = liquid_length * self.divisionValue
        liquid_res = liquid_length / level_length_range * full_range + rangeDown

        return liquid_res

    def box2points(self, box):
        if len(box) == 4:
            return [{"x": box[0][0], "y": box[0][1]}, {"x": box[1][0], "y": box[1][1]}, {"x": box[2][0], "y": box[2][1]}, {"x": box[3][0], "y": box[3][1]}]
        elif len(box) == 2:
            return [{"x": box[0][0], "y": box[0][1]}, {"x": box[0][0], "y": box[1][1]}, {"x": box[1][0], "y": box[1][1]}, {"x": box[1][0], "y": box[0][1]}]