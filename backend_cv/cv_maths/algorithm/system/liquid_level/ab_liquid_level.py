import importlib
import os
from abc import abstractmethod
from typing import Any, List

import cv2
from loguru import logger

from backend_common.constants.constant import *
from backend_common.constants.math_constants import ALGORITHM_CV_USER_DEFINE_PATH, DEFAULT_DECIMAL_PLACES, \
    ALGORITHM_CV_SYSTEM_INNER_PATH
from backend_common.exceptions.inspection_exception import AlgorithmProcessException, InspectionException
from backend_common.maths.algorithm.base.algorithm_base import AlgorithmInput, AlgorithmBase, AlgorithmStrategy, \
    AlgorithmSettings, \
    AlgorithmDetail, AlgorithmBaseInfo
from backend_common.utils.util import RectUtils, render_text_on_bbox

# 是否输出中间过程图片
WRITE_PROCESS_IMAGE = False
# 是否show中间过程图片
SHOW_PROCESS_IMAGE = False
# 透视变化阈值角度
# theta = 15
# THRESHOLD = math.tan(theta * math.pi / 180)
# 最小量程精度百分比
MIN_PRECISION_PERCENT = 0.01

SUFFIX_START = "sub"


class LiquidLevelReader(AlgorithmBase):
    """
    液位识别
    """
    schema_path = os.path.join(os.path.dirname(__file__), "schema.json")

    # _name  算法块唯一标识名,须与算法块文件夹名称一致，比如 liquid_level
    _name = "liquid_level"
    _description = "识别算法.基础.版本v1"
    _cn_name = "液位识别算法"

    # 支持的算法分类个数
    __supported_classify = (AlgorithmClassifyEnum.Primary, AlgorithmClassifyEnum.Secondary)
    # 支持的算法策略，primary -> main & secondary -> main
    __supported_strategies = [
        AlgorithmStrategy(__supported_classify[0], AlgorithmStrategyEnum.Main, "液位识别策略1",
                          "基于连通域的液位识别主策略", kv_param={}),
        AlgorithmStrategy(__supported_classify[1], AlgorithmStrategyEnum.Main, "液位识别策略2",
                          "基于颜色的液位识别主策略 ", kv_param={})
    ]


    # 支持的算法参数，若干
    __supported_params = AlgorithmBase.json_schema_2_algorithm_params(schema_path)

    def __init__(self, _inputs: AlgorithmInput, _id: int = -1):
        super(LiquidLevelReader, self).__init__(self.__class__._name, self.__class__._description,
                                                self.__class__._cn_name,
                                                self.__class__.__supported_strategies,
                                                self.__class__.__supported_params)

        if _inputs.__eq__(dict()):
            return

        self._local_var._inputs = _inputs
        # 算法实例参数
        self._local_var._settings = None

        self._originImg = self._get_input_images(first_only=True)
        self._rangeDown = self._get_input_value_by_name("range")[0]
        self._rangeUp = self._get_input_value_by_name("range")[1]

        self._columnLeftTop = self._get_input_value_by_name("column")[0]
        self._columnLeftBottom = self._get_input_value_by_name("column")[1]
        self._columnRightBottom = self._get_input_value_by_name("column")[2]
        self._columnRightTop = self._get_input_value_by_name("column")[3]
        self.resultScale = int(self._get_input_value_by_name("resultScale"))

        self._processCutTarget = None
        self._processBinaryTarget = None
        self._processFinalArea = None
        self._processFinalNum = None
        self._processOutputDir = os.getcwd()  # TODO

    def _supported_classify(self):
        return self.__supported_classify

    def _supported_strategy(self):
        return self.__supported_strategies

    def _supported_params(self):
        return self.__supported_params

    def get_algorithm_detail(self, a_type) -> AlgorithmDetail:

        schema_base = AlgorithmBase._get_algorithm_schema_info(self.schema_path, "algorithm")
        info = AlgorithmBaseInfo(self._id, self._name, self._name, self.cn_name, self._description,
                                 batch_support=schema_base.get("batchSupport"),
                                 alarm_support=schema_base.get("alarmSupport"),
                                 is_override_alarm=schema_base.get("isOverrideAlarm"),
                                 result_show_type=schema_base.get("resultShowType"))

        classifies = [self._classify2info(c) for c in self.__supported_classify]
        strategies = [self._strategy2info(s) for s in self.__supported_strategies]
        roi = self._get_roi_define(self.schema_path)
        scene_image = self._get_scene_image(self.schema_path)
        params = AlgorithmBase.json_schema_2_algorithm_params(self.schema_path)  # 重新加载一下, 可能会有刷新
        output_define = self._output_define2info(
            AlgorithmBase._get_algorithm_schema_info(self.schema_path, "outputDefine"))
        sub_list = self.load_algorithm_sublist(self._name, a_type, 'cv')

        return AlgorithmDetail(info, classifies, strategies, roi, scene_image, params, output_define, sub_list)

    def _temp_show_contour(self, contour, area=None):
        temp = self._processCutTarget.copy()
        if area is not None:
            print("__area,", area)

        cv2.drawContours(temp, contour, -1, (0, 255, 255), 3)
        cv2.imshow("__process_img", temp)
        cv2.waitKey()

    def _img_cut(self):
        """
        图像预处理,根据给定坐标直接切图
        """
        img = cv2.GaussianBlur(self._originImg, (3, 3), 0)

        box = [self._columnLeftTop, self._columnLeftBottom, self._columnRightBottom, self._columnRightTop]
        if WRITE_PROCESS_IMAGE:
            cv2.imwrite(self._processOutputDir + "/11_origin.jpg", self._originImg)

        # self.mask = np.zeros(img.shape, dtype=np.uint8)
        # roi_corners = np.array([points], dtype=np.int32)
        # # # 创建mask层
        # cv2.fillPoly(self.mask, roi_corners, (255, 255, 255))
        # # # 为每个像素进行or操作，除mask区域外，全为0
        # self._processCutTarget = cv2.bitwise_and(img, self.mask)
        # 白
        mask = np.ones(img.shape, dtype="uint8") * 255
        roi_corners = np.array([box], dtype=np.int32)
        # 黑
        cv2.fillPoly(mask, roi_corners, (0, 0, 0))
        self._processCutTarget = cv2.bitwise_or(self._originImg, mask)

        if WRITE_PROCESS_IMAGE:
            cv2.imwrite(self._processOutputDir + "/11_origin.jpg", self._originImg)
            cv2.imwrite(self._processOutputDir + "/12_matchRet.jpg", self._processCutTarget)

    def _img_contours(self):
        """
        图像边界
        :return:
        """
        contours, hierarchy = cv2.findContours(self._processBinaryTarget, cv2.RETR_LIST, cv2.CHAIN_APPROX_NONE)
        min_area, min_contour = None, None
        full_area = RectUtils.rect_area(self._columnLeftTop, self._columnRightBottom)
        print("full_area, ", full_area)
        for cnt in contours:
            # 最小矩形框
            rect = cv2.minAreaRect(cnt)
            center, (w, h), c = rect
            area = RectUtils.rect_area2(w, h)
            if SHOW_PROCESS_IMAGE:
                self._temp_show_contour(cnt, area)

            # rect面积大于整个图形面积的10%(防止面积过小的燥点) and  rect中心应当低于整个检测区域中心
            # if full_area * MIN_PRECISION_PERCENT < area and center[1] >= (
            #         (self._columnRightBottom[1] - self._columnLeftTop[1]) / 2 + self._columnRightTop[1]):
            if center[1] >= ((self._columnRightBottom[1] - self._columnLeftTop[1]) / 2 + self._columnRightTop[1]):
                # 首次 or 面积最小
                if min_area is None or area > min_area:
                    min_area, min_contour = area, cnt
        if min_contour is not None:
            self._processFinalArea = min_contour
            print("target_area,", min_area)
            if SHOW_PROCESS_IMAGE:
                self._temp_show_contour(min_contour, min_area)
        else:
            raise AlgorithmProcessException('没有识别到符合条件的液位区域，请调整参数后再试')

    def _calc_numerical(self):
        """按液柱占全柱比例计算读数"""
        full_range = abs(self._rangeDown - self._rangeUp)
        ch = self._columnRightBottom[1] - self._processFinalLiquid[1]
        h = self._columnRightBottom[1] - self._columnRightTop[1]
        self._processFinalNum = ch / h * full_range + self._rangeDown
        if self.resultScale == 0:
            self._processFinalNum = int(self._processFinalNum)
        else:
            self._processFinalNum = round(self._processFinalNum, self.resultScale)

    @abstractmethod
    def _preprocess(self) -> Any:
        """
        本算法的预处理由不同分类不同策略重写
        """
        pass

    def _do_detect(self) -> (bool, Any, tuple):
        """
        核心检测算法
        return:
            ret: 检测是否成功
            val: 数值化结果(如果有,若没有留空)
            points: 检测到的关键结果点集(具体每个点含义由各算法自行约定)

        """
        assert self._processFinalArea is not None

        min_w, min_h = None, None
        # ret, val, points = False, None, None
        for contour in self._processFinalArea:
            #  寻找最高点
            if min_h is None or contour[0][1] < min_h:
                min_w, min_h = contour[0]

        self._processFinalLiquid = (min_w, min_h)
        box = cv2.boxPoints(cv2.minAreaRect(self._processFinalArea))
        box = np.array(box, dtype=np.int32)
        try:
            self._calc_numerical()
        except Exception as e:
            logger.error(f"检测异常 {e}")
            return False, None, None
        #  TODO 当前只支持一张图中有一个液位计
        if self._processFinalNum < self._rangeDown or self._processFinalNum > self._rangeUp:
            print("抱歉，识别到的液位不在量程范围，请调整参数后再试")
            raise AlgorithmProcessException("抱歉，识别到的液位不在量程范围，请调整参数后再试")
        points = [self._get_input_value_by_name("column"), FAKE_PERCENT, [['square'] + [box.tolist()]]]
        return True, [[self._processFinalNum]], [[points]]

    @abstractmethod
    def _postprocess(self) -> Any:
        """
        本算法的后置处理可由不同分类不同策略重写
        """
        pass

    def _gen_result_img(self) -> List[np.ndarray]:
        """
          生成结果图像
          return:
            结果图像
        """
        assert self._processFinalArea is not None
        assert self._processFinalLiquid is not None

        temp = self._originImg.copy()
        p_color, t_color, a_color = (0, 0, 255), (60, 255, 0), (0, 255, 255)

        box = cv2.boxPoints(cv2.minAreaRect(self._processFinalArea))
        bbox = np.array(box, dtype=np.int32)
        #cv2.fillPoly(temp, [bbox], a_color)

        # TODO 只能支持一张图
        # return [cv2.putText(temp, "{:.1f}".format(self._processFinalNum), self._processFinalLiquid,
        #                     cv2.FONT_HERSHEY_PLAIN, 1.0,
        #                     t_color, thickness=2)]
        text = f'{self._processFinalNum} {"%.2f" % FAKE_PERCENT}'
        bbox = (bbox[0][0], bbox[0][1], bbox[2][0], bbox[2][1])
        return [render_text_on_bbox(temp, bbox, text)]

    def determine_algorithm_instance(self, settings: AlgorithmSettings = None, index: int = 0,
                                     a_type: str = "system_inner"):
        suffix = f"{SUFFIX_START}{index}"
        module_name = f".{self._name}.ab_{self._name}_{suffix}"

        base_path = ALGORITHM_CV_SYSTEM_INNER_PATH
        if a_type == "user_define":
            base_path = ALGORITHM_CV_USER_DEFINE_PATH

        try:
            M = importlib.import_module(module_name, base_path)
        except ModuleNotFoundError:
            raise InspectionException(f"Auto-load module '{module_name}' from '{base_path}' \
                                failure, please check it manually")

        # !!!!  找出子类中名称以当前 suffix.capitalize()  为 END 的作为 target 算法类  !!!!
        target_clazz_ = [clazz for clazz in LiquidLevelReader.__subclasses__()
                         if clazz.__name__.endswith(suffix.capitalize())][0]

        algorithm_instance: LiquidLevelReader = target_clazz_(self._local_var._inputs, self._id)
        algorithm_instance._settings = settings

        return algorithm_instance
