## 液位读数识别算法
## 版本:

 v 1.0.0

## 描述:

 该算法用于液位读数识别，支持工厂以及实验室大多数液位识别。使用建议:

1) 液体颜色参数建议选取图片中液柱内大多数相同的颜色。
2) 颜色偏差不建议调整。

## 输入:

| 名称 | 类型 | 描述 |
|-------|--------------|----------------------|
| 液体颜色 | color_picker | 取色笔，液体的颜色 |
| 颜色偏差 | number | 数值，指的是液体颜色可变幅度 |
| 矩形液柱 | square | 矩形， item为4个point，具体表现为用矩形框代表液柱区域 |
| 量程 | range | 量程，item为number，液位仪器的量程 |

* 是否支持单图批量识别：否

## 输出：

| 名称 | 类型 | 描述 |
|-------|--------------|----------------------|
| 液位位置 | square | 用矩形代表识别出的液位区域 |
| 液位读数 | number | 数值，液位的读数识别，例如液位读数识别为75.0 |
| 置信度 | number | 数值，0-1之间，代表识别出来液位读数的可信度 |