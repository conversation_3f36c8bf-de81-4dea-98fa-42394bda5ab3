from typing import final, Any

import cv2
from loguru import logger

from backend_common.constants.constant import *
from backend_common.exceptions.inspection_exception import AlgorithmProcessException
from backend_common.utils.util import RectUtils
from backend_common.utils.util import get_mask_by_color


@final
class LiquidLevelReaderSub1():
    """
    液位识别2
    """

    def _do_detect(self, originImg, liquid_roi, columnLeftTop, columnLeftBottom, columnRightBottom, columnRightTop, rangeDown, rangeUp, liquid_color_rgb, colorThreshold, resultDes, unit, resultPrecision):
        """
        核心检测算法
        return:
            isSuccess: 检测是否成功
            output: 数值化结果(如果有,若没有留空)
            osdInfo: 需要绘制到osd上面的参数(具体由各算法自行约定)

        """

        # 切图
        processCutTarget = self._img_cut(originImg, columnLeftTop, columnLeftBottom, columnRightBottom, columnRightTop)
        # 二值化-根据颜色
        processBinaryTarget = self._img_threshold_by_color(processCutTarget, columnRightBottom, columnRightTop, liquid_color_rgb, colorThreshold)
        # 液柱轮廓
        processFinalArea = self._img_contours(processBinaryTarget, columnLeftTop, columnRightBottom, columnRightTop)

        assert processFinalArea is not None

        min_w, min_h = None, None
        # ret, val, points = False, None, None
        for contour in processFinalArea:
            #  寻找最高点
            if min_h is None or contour[0][1] < min_h:
                min_w, min_h = contour[0]

        processFinalLiquid = (min_w, min_h)
        box = cv2.boxPoints(cv2.minAreaRect(processFinalArea))
        box = np.array(box, dtype=np.int32)
        try:
            processFinalNum = self._calc_numerical(rangeDown, rangeUp, columnRightBottom, columnRightTop, processFinalLiquid)
        except:
            logger.error("算法二没有识别到液位，请再次尝试")
            return False, None, None
        #  TODO 当前只支持一张图中有一个液位计
        if processFinalNum < rangeDown or processFinalNum > rangeUp:
            logger.error("算法二识别到的液位不在量程范围，请调整参数后再试")
            raise AlgorithmProcessException("识别到的液位不在量程范围，请调整参数后再试")
        # points = [liquid_roi.tolist(), FAKE_PERCENT, [['square'] + [box.tolist()]]]
        liquid_roi = self.box2points(liquid_roi.tolist())
        box = self.box2points(box.tolist())
        result = {"value": processFinalNum}
        textObj = {"value": processFinalNum}
        if resultPrecision is not None:
            result.update({"resultPrecision":resultPrecision})
            textObj.update({"resultPrecision":resultPrecision})
        if resultDes is not None:
            result.update({"resultDes":resultDes})
        if unit is not None:
            result.update({"unit":unit})
            textObj.update({"unit":unit})
        osdCoor = {"dataType": "SQUARE","textObj": textObj,"coords": liquid_roi}
        return True, {"liquidLevel": result}, [osdCoor,{"dataType": "SQUARE","coords": box}]


    def _preprocess(self):
        if self._originImg is None:
            raise AlgorithmProcessException("_originImg can not be None，please check file path")
        # 切图
        self._img_cut()
        # 二值化-根据颜色
        liquid_color_rgb = self._get_input_value_by_name("color_picker")
        colorThreshold = self._get_input_value_by_name("colorThreshold")
        #self._img_threshold_by_color(ColorSeriesEnum.from_str(color_series))
        self._img_threshold_by_color(liquid_color_rgb, colorThreshold)
        # 液柱轮廓
        self._img_contours()

    def _postprocess(self) -> Any:
        pass

    def _img_cut(self, originImg, columnLeftTop, columnLeftBottom, columnRightBottom, columnRightTop):
        """
        图像预处理,根据给定坐标直接切图
        """
        img = cv2.GaussianBlur(originImg, (3, 3), 0)

        box = [columnLeftTop, columnLeftBottom, columnRightBottom, columnRightTop]
        # 白
        mask = np.ones(img.shape, dtype="uint8") * 255
        roi_corners = np.array([box], dtype=np.int32)
        # 黑
        cv2.fillPoly(mask, roi_corners, (0, 0, 0))
        processCutTarget = cv2.bitwise_or(originImg, mask)
        return processCutTarget


    def _calc_numerical(self, rangeDown, rangeUp, columnRightBottom, columnRightTop, processFinalLiquid):
        """按液柱占全柱比例计算读数"""
        full_range = abs(rangeDown - rangeUp)
        ch = columnRightBottom[1] - processFinalLiquid[1]
        h = columnRightBottom[1] - columnRightTop[1]
        processFinalNum = ch / h * full_range + rangeDown

        return processFinalNum

    def _img_threshold_by_color(self, processCutTarget, columnRightBottom, columnRightTop, color=ColorSeriesEnum.Red, colorThreshold=255):
        """
        图像二值化
        :return:
        """
        H = int((columnRightBottom[1] - columnRightTop[1]) * 0.01)
        kernel = np.ones((H, 1), np.uint8)
        # 去除红颜色范围外的其余颜色
        mask = get_mask_by_color(processCutTarget, color, colorThreshold, color_picker=True)
        mask = cv2.dilate(mask, kernel=np.ones((8,8), np.uint8), iterations=1)
        #cv2.imwrite('/opt/tjh/model_server/cv_device_img/mask.jpg', mask)
        reval_t, thre = cv2.threshold(mask, 0, 255, cv2.THRESH_BINARY | cv2.THRESH_OTSU)
        dilate2 = cv2.dilate(thre, kernel, iterations=3)
        processBinaryTarget = cv2.erode(dilate2, kernel, iterations=3)
        return processBinaryTarget

    def _img_contours(self, processBinaryTarget, columnLeftTop, columnRightBottom, columnRightTop):
        """
        图像边界
        :return:
        """
        contours, hierarchy = cv2.findContours(processBinaryTarget, cv2.RETR_LIST, cv2.CHAIN_APPROX_NONE)
        min_area, min_contour = None, None
        full_area = RectUtils.rect_area(columnLeftTop, columnRightBottom)

        for cnt in contours:
            # 最小矩形框
            rect = cv2.minAreaRect(cnt)
            center, (w, h), c = rect
            area = RectUtils.rect_area2(w, h)

            if center[1] >= ((columnRightBottom[1] - columnLeftTop[1]) / 2 + columnRightTop[1]):
                # 首次 or 面积最小
                if min_area is None or area > min_area:
                    min_area, min_contour = area, cnt
        if min_contour is not None:
            processFinalArea = min_contour

        else:
            raise AlgorithmProcessException('没有识别到液位，请调整参数后再试')
        return processFinalArea

    def box2points(self, box):
        if len(box) == 4:
            return [{"x": box[0][0], "y": box[0][1]}, {"x": box[1][0], "y": box[1][1]}, {"x": box[2][0], "y": box[2][1]}, {"x": box[3][0], "y": box[3][1]}]
        elif len(box) == 2:
            return [{"x": box[0][0], "y": box[0][1]}, {"x": box[0][0], "y": box[1][1]}, {"x": box[1][0], "y": box[1][1]}, {"x": box[1][0], "y": box[0][1]}]