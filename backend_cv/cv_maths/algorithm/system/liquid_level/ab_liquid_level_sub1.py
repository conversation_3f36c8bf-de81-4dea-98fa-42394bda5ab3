from typing import final, Any

import cv2

from backend_common.constants.constant import *
from backend_common.exceptions.inspection_exception import AlgorithmProcessException
from backend_common.maths.algorithm.base.algorithm_base import AlgorithmInput
from cv_maths.algorithm.system.liquid_level.ab_liquid_level import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ead<PERSON>, WRITE_PROCESS_IMAGE
from backend_common.utils.util import get_mask_by_color


@final
class LiquidLevelReaderSub1(LiquidLevelReader):
    """
    液位识别
    """

    # _name = "LiquidLevelReader.primary.v1"
    _index = 1
    _description = "识别算法.主模型.版本v1"
    _cn_name = "液位识别算法(主模型)"

    def __init__(self, _inputs: AlgorithmInput, _id: int = -1):
        super(LiquidLevelReaderSub1, self).__init__(_inputs, _id)

    def _preprocess(self):
        if self._originImg is None:
            raise AlgorithmProcessException("__originImg can not be None，please check file path/integrity")
        # 切图
        self._img_cut()
        # 二值化-根据颜色
        liquid_color_rgb = self._get_input_value_by_name("color_picker")
        colorThreshold = self._get_input_value_by_name("colorThreshold")
        #self._img_threshold_by_color(ColorSeriesEnum.from_str(color_series))
        self._img_threshold_by_color(liquid_color_rgb, colorThreshold)
        # 液柱轮廓
        self._img_contours()

    def _postprocess(self) -> Any:
        pass

    def _img_threshold_by_color(self, color=ColorSeriesEnum.Red, colorThreshold=255):
        """
        图像二值化
        :return:
        """
        H = int((self._columnRightBottom[1] - self._columnRightTop[1]) * 0.01)
        # # H1 = H * 2 + 1`
        kernel = np.ones((H, 1), np.uint8)
        # dilate = cv2.dilate(self._processCutTarget, kernel, iterations=8)
        # erode = cv2.erode(dilate, kernel, iterations=8)
        # closing = cv2.morphologyEx(self._processCutTarget, cv2.MORPH_CLOSE, kernel, iterations=30)

        # if WRITE_PROCESS_IMAGE:
        #     cv2.imwrite(self._processOutputDir + "/21_gray.jpg", erode)
        # 将图像转化为HSV格式，便于颜色提取
        #img_hsv = cv2.cvtColor(erode, cv2.COLOR_BGR2HSV)
        # img_hsv = cv2.cvtColor(self._processCutTarget, cv2.COLOR_BGR2HSV)
        #cv2.imwrite('/opt/tjh/model_server/cv_device_img/roi_img.jpg', self._processCutTarget)
        # 去除红颜色范围外的其余颜色
        mask = get_mask_by_color(self._processCutTarget, color, colorThreshold, color_picker=True)
        mask = cv2.dilate(mask, kernel=np.ones((8,8), np.uint8), iterations=1)
        #cv2.imwrite('/opt/tjh/model_server/cv_device_img/mask.jpg', mask)
        if WRITE_PROCESS_IMAGE:
            cv2.imwrite(self._processOutputDir + "/22_mask.jpg", mask)
        reval_t, thre = cv2.threshold(mask, 0, 255, cv2.THRESH_BINARY | cv2.THRESH_OTSU)
        # kernel = np.ones((H, 1), np.uint8)
        dilate2 = cv2.dilate(thre, kernel, iterations=3)
        self._processBinaryTarget = cv2.erode(dilate2, kernel, iterations=3)
        # self._processBinaryTarget = dilate2
        if WRITE_PROCESS_IMAGE:
            cv2.imwrite(self._processOutputDir + "/22_threshold.jpg", self._processBinaryTarget)
