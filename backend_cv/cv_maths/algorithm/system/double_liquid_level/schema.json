{"algorithm": {"name": "双液位读数识别", "code": "double_liquid_level", "des": "双液位读数识别.V1", "type": "openCV", "alarmSupport": true, "isOverrideAlarm": true, "batchSupport": false}, "roi": {"name": "ROI区域", "type": "polygon", "des": "支持绘制的ROI区域类型, polygon:多边形, circle:圆, ellipse:椭圆", "value": [[325, 116], [331, 115], [346, 310], [337, 310]], "nullable": true}, "targetSceneImage": {"in_use": false, "uri": "", "name": "场景基准图"}, "images": ["e:/images/test.jpg"], "inputParam": {"color_picker": {"name": "液体颜色", "type": "color_picker", "value": [0, 255, 0], "des": "从图像中提取到的液体的颜色值(RGB)"}, "colorThreshold": {"name": "颜色偏差", "type": "number", "des": "颜色偏差,值越大颜色区间越大,可根据实际情况酌情调节", "value": 55, "val_range": [0, 255, 1, 55]}, "column": {"name": "矩形液柱", "type": "square", "value": [[325, 116], [331, 115], [346, 310], [337, 310]], "showable": true, "des": "数组形式，元素是一个point对象，长度为4，分别为左上、左下、右下、右上顶点坐标"}, "rangeList": {"name": "液位量程列表", "type": "array", "value": [], "val_range": [{"range": {"name": "量程", "type": "range", "value": [-30, 50], "desc": "取值范围", "val_range": [-100, 200, 1]}, "range_location": {"name": "量程位置", "type": "select", "des": "需配置左侧或右侧量程", "val_range": {"left": "左侧量程", "right": "右侧量程"}}}], "desc": "支持两个液位量程进行输入"}}, "outputDefine": {"type": "analogue", "desc": "液位读数-模拟量", "ruleInfo": [{"desc": "液位读数越高高限", "isBind": false, "type": "HH", "val": null, "placeHolder": "大于等于时报警"}, {"desc": "液位读数越高限", "isBind": false, "type": "H", "val": null, "placeHolder": "大于等于时报警"}, {"desc": "液位读数越低限", "isBind": false, "type": "L", "val": null, "placeHolder": "小于等于时报警"}, {"desc": "液位读数越低低限", "isBind": false, "type": "LL", "val": null, "placeHolder": "小于等于时报警"}]}}