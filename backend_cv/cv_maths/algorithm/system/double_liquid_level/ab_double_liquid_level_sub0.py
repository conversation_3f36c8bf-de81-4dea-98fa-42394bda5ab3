from typing import Any

import cv2
import numpy as np

from backend_common.exceptions.inspection_exception import AlgorithmProcessException
from backend_common.maths.algorithm.base.algorithm_base import AlgorithmInput
from backend_common.utils.util import get_mask_by_color

from cv_maths.algorithm.system.double_liquid_level import DoubleLiquidLevelReader


class DoubleLiquidLevelReaderSub0(DoubleLiquidLevelReader):
    """
    双液位读数识别算法
    """

    _index = 0
    _description = "双液位读数识别.主模型.版本v1"
    _cn_name = "双液位读数识别算法"

    def __init__(self, _inputs: AlgorithmInput, _id: int = -1):
        super(DoubleLiquidLevelReaderSub0, self).__init__(_inputs, _id)

    def _do_detect(self) -> (bool, Any, tuple):
        return super(DoubleLiquidLevelReaderSub0, self)._do_detect()

    def parameter_separation(self, param_dict):

        self.range_down = param_dict['range'][0]
        self.range_up = param_dict['range'][1]
        self.range_location = param_dict['range_location']

    def get_rec_roi(self, img, liquid_roi_coor):

        x1, y1 = liquid_roi_coor[0][0], liquid_roi_coor[0][1]
        x2, y2 = liquid_roi_coor[2][0], liquid_roi_coor[2][1]
        w = x2 - x1
        h = y2 - y1

        roi_img = img[y1:y1 + h, x1:x1 + w]
        self.roi_left_top = (x1, y1)
        self.roi_w_h = (w, h)
        self.roi_w = w
        self.roi_h = h
        return roi_img

    def find_connect_component(self, roi_img, contours):

        white_roi_img = np.zeros(roi_img.shape[0:3])
        # 轮廓面积排序
        cnts = sorted(contours, key=cv2.contourArea, reverse=True)

        division_y = -1
        contours_rect = []

        for i, cnt in enumerate(cnts[0:2]):
            # 图黑轮廓白
            cnt_img = cv2.drawContours(white_roi_img.copy(), cnt, -1, (255, 255, 255), 1)
            # 每个轮廓的外接矩形
            rect = cv2.boundingRect(cnt)
            contours_rect.append(rect)

            cnt_x, cnt_y, cnt_w, cnt_h = rect
            print(rect)

            # 根据每个轮廓的高度生成对应的直线
            index = 0
            for y in range(cnt_y, cnt_y + cnt_h, 4):
                line_img = cv2.line(white_roi_img.copy(), (0, y), (self.roi_w, y), (255, 255, 255), 1)

                # 求直线和轮廓的交点
                intersection = np.logical_and(line_img, cnt_img)
                points = np.argwhere(intersection==True)

                point1 = points[0]
                point2 = points[-1]
                intersection_w = point2[1] - point1[1]

                if index == 0:
                    last_w = intersection_w
                else:
                    difference = intersection_w - last_w
                    if intersection_w > last_w and abs(difference) >= 200:
                        print("find division line in contours: ", i)
                        division_y = y
                        break

                    last_w = intersection_w
                index += 1

        return division_y, contours_rect

    def get_liquid_num(self, division_y, contours_rect):
        list_y = []
        if division_y != -1:
            list_y.append(division_y)

        for rect in contours_rect:
            list_y.append(rect[1])
            list_y.append(rect[1] + rect[3])

        list_y.sort()
        print(list_y)
        if division_y == -1:
            liquid_y_1 = list_y[0]
            liquid_y_2 = list_y[1]
        else:
            liquid_y_1 = list_y[1]
            liquid_y_2 = list_y[2]

        full_range = abs(self.range_down - self.range_up)

        liquid_length_1 = self.roi_h - liquid_y_1
        liquid_length_2 = self.roi_h - liquid_y_2

        if self.range_location == "left":
            liquid_res_1 = liquid_length_1 / self.roi_h * full_range + self.range_down
            liquid_res_1 = round(liquid_res_1, 3)
            return liquid_res_1, liquid_y_1
        if self.range_location == "right":
            liquid_res_2 = liquid_length_2 / self.roi_h * full_range + self.range_down
            liquid_res_2 = round(liquid_res_2, 3)
            return liquid_res_2, liquid_y_2

    def result_postpross(self, liquid_y_coor):

        roi_x = self.roi_left_top[0]
        roi_y = self.roi_left_top[1]

        point1 = (roi_x, roi_y + liquid_y_coor)
        point2 = (roi_x + self.roi_w, roi_y + liquid_y_coor)
        lines_points = [point1[0], point1[1], point2[0], point2[1]]
        return lines_points

    def det_liquid_level(self, img, param_dict, liquid_color_rgb, colorThreshold, liquid_roi_coor):

        self.parameter_separation(param_dict)

        roi_img = self.get_rec_roi(img, liquid_roi_coor)
        # 根据颜色获得mask
        color_mask = get_mask_by_color(roi_img, liquid_color_rgb, colorThreshold)
        # 执行膨胀操作
        color_mask = cv2.dilate(color_mask, kernel=np.ones((5, 5), np.uint8), iterations=1)

        contours, _ = cv2.findContours(color_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_NONE)
        if len(contours) >= 2:
            print("检测到有效液位")
            division_y, contours_rect = self.find_connect_component(roi_img, contours)
            liquid_res, liquid_y_coor = self.get_liquid_num(division_y, contours_rect)

            if liquid_res < self.range_down or liquid_res > self.range_up:
                print("抱歉，识别到的液位不在量程范围，请调整参数后再试")
                raise AlgorithmProcessException("抱歉，识别到的液位不在量程范围，请调整参数后再试")

            lines_points = self.result_postpross(liquid_y_coor)
            return liquid_res, lines_points

        else:
            print("未检测到两个有效液位")
            raise AlgorithmProcessException("未检测到两个有效液位，请调整参数后再试")
