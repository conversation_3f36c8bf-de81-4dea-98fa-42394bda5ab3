from typing import Any

import cv2
import numpy as np
from skimage import measure

from backend_common.constants.constant import color_list, LOWER, UPPER
from backend_common.constants.math_constants import DEFAULT_DECIMAL_PLACES
from backend_common.exceptions.inspection_exception import AlgorithmProcessException
from backend_common.maths.algorithm.base.algorithm_base import AlgorithmInput
from backend_common.utils.util import Functions, get_hsv_color_str, get_mask_by_color, get_angle
from cv_maths.algorithm.system.meter_bool.ab_meter_bool import MeterBoolReader


class MeterBoolReaderSub1(MeterBoolReader):
    """
    仪表识别算法
    """

    _index = 1
    _description = "仪表识别.主模型.版本v1"
    _cn_name = "仪表识别算法"

    def __init__(self, _inputs: AlgorithmInput, _id: int = -1):
        super(MeterBoolReaderSub1, self).__init__(_inputs, _id)

    def _preprocess(self) -> Any:  # TODO
        pass

    def _do_detect(self) -> (bool, Any, tuple):
        return super(MeterBoolReaderSub1, self)._do_detect()

    def _postprocess(self) -> Any:
        pass

    def getAngel(self, point1, point2):
        pose1CentLine = [self.circleCent[0] -
                         point1[0], self.circleCent[1] - point1[1]]
        pose2CentLine = [self.circleCent[0] -
                         point2[0], self.circleCent[1] - point2[1]]
        # 圆心到直线两点的向量夹角
        theta = Functions.get_clock_angle(pose1CentLine, pose2CentLine)
        return theta

    def disttances(self, a, b):
        # 返回两点间的距离
        x1, y1 = a
        x2, y2 = b
        from math import sqrt
        distance = int(sqrt((x1 - x2) ** 2 + (y1 - y2) ** 2))
        return distance

    def parse_parameter(self, param_dict):
        """
        识别并读取仪表读数

        :param param_dict 参数字典
        :param img 输入图片
        :param img_idx 输入图片 index
        :param meter_idx 仪表 index
        """
        # self.zeroPoint = param_dict['zeroPoint']
        self.startPoint = param_dict['startPoint']
        self.endPoint = param_dict['endPoint']
        self.colorThreshold = int(param_dict['colorThreshold'])
        # self.meterRange = param_dict['range']
        roi = param_dict['roiSquare']
        self.circleCent = roi['center']
        self.circleR_ori = roi['radius']
        circleR_clacu = int(0.8 * ((self.disttances(self.circleCent, self.startPoint) +
                                    self.disttances(self.circleCent, self.endPoint)) / 2))
        # print(circleR, circleR_clacu)
        self.circleR = min(self.circleR_ori, circleR_clacu)
        self.meter_color_rgb = param_dict['color_picker']

        # 计算角度单位值，即每角度的刻度值
        self.start_angle = get_angle(self.circleCent, self.startPoint)
        # zero_angle = get_angle(self.circleCent, self.zeroPoint)
        end_angle = get_angle(self.circleCent, self.endPoint)
        range_angel = end_angle - self.start_angle
        if range_angel > 0:
            range_angel = 360 - range_angel
        else:
            range_angel = abs(range_angel)
        self.range_angel = range_angel
        
        #self.divisionValue = (self.meterRange[1]-self.meterRange[0])/angel
        # self.divisionValue = self.meterRange[1]/angel

    def read_meter(self, param_dict, ori_image, img_idx, meter_idx):
        # 解析参数
        self.parse_parameter(param_dict)

        # 截取ROI, 仪表盘切分
        small_roi_img = self.imgCutCircle(ori_image)
        # small_roi_img = self.find_roi_rectangle(ori_image)
        # cv2.imwrite('/opt/tjh/model_server/depl_img/output/roi_img1.jpg', small_roi_img)
        # 根据指针颜色mask
        # color_mask = self.edgeFilter(roi_img)
        color_mask = get_mask_by_color(small_roi_img, self.meter_color_rgb, self.colorThreshold, color_picker=True)
        # cv2.imwrite('/opt/tjh/model_server/cv_device_img/output/mask.jpg', color_mask)
        # 寻找最大连通域并返回质心坐标
        line, res, largestConnectComponent = self.largestConnectComponentAndPoint(color_mask)
        # cv2.imwrite('/opt/tjh/model_server/cv_device_img/output/ConnectComponent.jpg', largestConnectComponent)
        if not res:
            print("抱歉，没有识别到指针，请再次尝试")
            raise AlgorithmProcessException("抱歉，没有识别到指针，请再次尝试")
        # else:
        #    res = self.normlDetect(centroid_point)
        centroid_point = self.extend_point(small_roi_img, line)
        centroid_point[0] += self.coor[0]
        centroid_point[1] += self.coor[1]

        self._processFarPose[img_idx][meter_idx] = centroid_point

        pointer_angel = get_angle(self.circleCent, centroid_point)
        pointer_zero_angel = pointer_angel - self.start_angle
        if pointer_zero_angel > 0:
            pointer_zero_angel = 360 - pointer_zero_angel
        else:
            pointer_zero_angel = abs(pointer_zero_angel)
        
        if pointer_zero_angel > self.range_angel:
            print('out of range')
            return 'False'
        else:
            print('in the range')
            return 'True'
        

        # cv2.line(ori_image, self.circleCent, centroid_point, (0, 0, 255), 2)
        # cv2.imwrite('/opt/tjh/model_server/cv_device_img/output/result1.jpg', ori_image)

    def imgCutCircle(self, img):
        # 图白圆黑
        circle = np.ones(img.shape, dtype="uint8")
        circle = circle * 255
        cv2.circle(circle, self.circleCent, self.circleR, 0, -1)
        self.bitwiseOr = cv2.bitwise_or(img, circle)

        # 修改处
        circleR_ori_expansion = self.circleR_ori + 5
        x = self.circleCent[0] - circleR_ori_expansion
        y = self.circleCent[1] - circleR_ori_expansion
        w = circleR_ori_expansion * 2
        h = circleR_ori_expansion * 2

        if x < 0:
            x = 0
        if x + w > img.shape[1]:
            w = img.shape[1] - x
        if y < 0:
            y = 0
        if y + h > img.shape[0]:
            h = img.shape[0] - y

        self.img_cut_check(self.bitwiseOr, x + w, y + h)
        self.bitwiseOr = self.bitwiseOr[y:y + h, x:x + w]
        self.coor = [x, y]
        self.samll_circleCent = [self.circleCent[0]-self.coor[0], self.circleCent[1]-self.coor[1]]
        # 修改结束
        return self.bitwiseOr
    
    def find_roi_rectangle(self, img):
        # 图黑圆白
        circle = np.zeros(img.shape[:2], dtype="uint8")
        cv2.circle(circle, self.circleCent, self.circleR, 255, -1)
        contours, _ = cv2.findContours(circle,  cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE)
        x, y, w, h = cv2.boundingRect(contours[0])

        # 扩大外接矩形面积, wh设置为xy的两倍，注意如果ROI在图像的边缘，坐标可能会报错，扩大区域可以小一些
        x = x - 10
        y = y - 10
        w = w + 20
        h = h + 20

        new_img = self.bitwiseOr[y:y + h, x:x + w]
        self.coor = [x, y]
        self.samll_circleCent = [self.circleCent[0]-self.coor[0], self.circleCent[1]-self.coor[1]]
        return new_img

    def edgeFilter(self, img):
        meter_color, point_hsv = get_hsv_color_str(self.meter_color_rgb)
        if meter_color == 'Red':
            hsv_index_list = [0, 1]
        else:
            hsv_index_list = [color_list.index(meter_color)]
        img_hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)
        image_add = []
        for index in hsv_index_list:
            if LOWER[index][0] < point_hsv[0] < UPPER[index][0]:
                low = np.where((point_hsv - self.colorThreshold) > LOWER[index], point_hsv - self.colorThreshold,
                               LOWER[index])
                up = np.where((point_hsv + self.colorThreshold) < UPPER[index], point_hsv + self.colorThreshold,
                              UPPER[index])
            else:
                low, up = LOWER[index], UPPER[index]
            print(point_hsv, LOWER[index], UPPER[index])
            print(low, up)
            mask_image = cv2.inRange(img_hsv, low, up)
            # cv2.imwrite(os.path.join(self.out_dir, 'mask.jpg'), mask)
            # reval_t, thre = cv2.threshold(mask, 0, 255, cv2.THRESH_BINARY | cv2.THRESH_OTSU)
            # cv2.imwrite('thre.jpg', thre)
            # kernel = np.ones((5, 1), np.uint8)
            # self.edge = cv2.erode(mask, kernel, iterations=1)
            # mask_image = cv2.morphologyEx(mask_image, cv2.MORPH_CLOSE,kernel=np.ones((5,5),np.uint8))
            image_add.append(mask_image)
            # cv2.imwrite(os.path.join(self.out_dir, 'mask_{}.jpg'.format(index)), mask_image)
            if index == 1:
                mask_image = cv2.add(image_add[0], image_add[1])
        # mask_image = cv2.morphologyEx(mask_image,cv2.MORPH_OPEN,kernel=np.ones((5,5),np.uint8))
        # cv2.imwrite(os.path.join(self.out_dir, 'mask_pre.jpg'), mask_image)
        # mask_image = cv2.dilate(mask_image, kernel=np.ones((2,2), np.uint8), iterations=1)
        # cv2.imwrite(os.path.join(self.out_dir, 'mask.jpg'), mask_image)
        return mask_image

    def largestConnectComponentAndPoint(self, mask_edge):
        def get_min_rect(gray):
            # 找到轮廓
            contours, hierarchy = cv2.findContours(gray, cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE)
            # 计算旋转矩形
            rect = cv2.minAreaRect(contours[0])
            # 获取旋转矩形参数
            center, size, angle = rect
            # 转换为int类型
            center = tuple(map(int, center))
            size = tuple(map(int, size))
            # 画出旋转矩形
            rect = cv2.boxPoints(rect)
            rect = np.int0(rect)
            return rect
        labeled_img, num = measure.label(mask_edge, background=0, return_num=True)
        if num == 0:
            return [], 0, 0
        # visual_image= color.label2rgb(labeled_img)
        # cv2.imwrite(os.path.join(self.out_dir, 'ConnectComponent.jpg'), visual_image)
        props = measure.regionprops(labeled_img)

        numPix = []
        for ia in range(len(props)):
            numPix += [props[ia].area]

        # 像素最多的连通区域及其指引
        # maxnum = max(numPix)
        # index = numPix.index(maxnum)
        areas = []
        out = []
        index = np.argsort(numPix)[::-1]
        for i in index:
            largestConnectComponent = mask_edge.copy()
            largestConnectComponent[labeled_img != props[i].label] = 0
            # cv2.imwrite('/opt/tjh/model_server/cv_device_img/ConnectComponent.jpg', largestConnectComponent)

            contours_ = np.argwhere(largestConnectComponent == 255)
            contours_[:, [0, 1]] = contours_[:, [1, 0]]
            [vx, vy, x, y] = cv2.fitLine(contours_, cv2.DIST_L2, 0, 0.01, 0.01)

            # slope, point = self.lineFit(largestConnectComponent, ori_image)
            k = vy / vx
            b = y - k * x
            dist = abs(k * self.samll_circleCent[0] - self.samll_circleCent[1] + b) / ((k ** 2 + 1) ** 0.5)
            # print(dist, self.circleR)

            rect = get_min_rect(largestConnectComponent)
            # print(rect)
            w = Functions.distances(rect[0], rect[1])
            h = Functions.distances(rect[1], rect[2])
            if min(w,h)==0:
                break
            ratio = max(w,h) / min(w,h)

            if dist < self.circleR * 0.2 and ratio > 2:
        #         point = props[i].centroid
        #         centroid_point = [int(point[1]), int(point[0])]
        #         return [vx, vy] + centroid_point, 1
        #         # return largestConnectComponent, 1, slope, point
        # return [], 0
                areas.append(numPix[i])
                point = props[i].centroid
                centroid_point = [int(point[1]), int(point[0])]
                out.append([[vx, vy] + centroid_point, 1, largestConnectComponent])
        #         cv2.drawContours(mask_edge_, [rect], 0, (0, 0, 255), 1)
        # cv2.imwrite('/opt/tjh/model_server/cv_device_img/output/mask_rectangle.jpg', mask_edge_)
        if len(out) == 0:
            return [], 0, 0
        else:
            index = areas.index(max(areas))
            return out[index]

        # largestConnectComponent = mask_edge.copy()
        # largestConnectComponent[labeled_img!=props[index[0]].label] = 0

        # minRect = cv2.minAreaRect(props[index[0]].coords)
        # ##最小矩形框的四个点坐标（右上，左上，左下，右下）
        # box = np.int0(cv2.boxPoints(minRect))
        # box[:,[0,1]] = box[:,[1,0]]
        # max_dst = max(self.Disttances(box[0], box[1]), self.Disttances(box[1], box[2]))
        # if max_dst < 0.6*self.circleR:
        #     largestConnectComponent1 = mask_edge.copy()
        #     largestConnectComponent1[labeled_img!=props[index[1]].label] = 0
        #     largestConnectComponent = np.logical_or(largestConnectComponent, largestConnectComponent1)
        #     largestConnectComponent = np.where(largestConnectComponent==True, 255, 0)
        # cv2.imwrite(os.path.join(self.out_dir, 'ConnectComponent.jpg'), largestConnectComponent)

        # contours_ = np.argwhere(largestConnectComponent==255)
        # contours_[:,[0,1]] = contours_[:,[1,0]]
        # [vx , vy , x, y] = cv2.fitLine(contours_ , cv2.DIST_L2, 0,0.01 , 0.01)

        # point = props[index[0]].centroid
        # centroid_point = [int(point[1]), int(point[0])]
        # return [vx , vy] + centroid_point, 1

    def extend_point(self, ori_image, line):
        vx, vy, x, y = line
        blank = np.zeros(ori_image.shape[0:3])
        rows, cols = ori_image.shape[:2]
        lefty = int((-x * vy / vx) + y)
        righty = int(((cols - x) * vy / vx) + y)
        line_img = cv2.line(blank.copy(), (cols - 1, righty), (0, lefty), (255, 255, 255), 2)
        cicle_img = cv2.circle(blank.copy(), self.samll_circleCent, self.circleR_ori, (255, 255, 255), 1)
        # intersection_img = cv2.addWeighted(line_img, 0.5, cicle_img, 0.5, 0)
        # cv2.imwrite(os.path.join(self.out_dir, 'intersection_img.jpg'), intersection_img)
        intersection = np.logical_and(line_img, cicle_img)
        # 获得交点位置
        points = np.argwhere(intersection == True)
        point1 = points[0]
        dist1 = self.disttances((x, y), (point1[1], point1[0]))
        point2 = points[-1]
        dist2 = self.disttances((x, y), (point2[1], point2[0]))
        if dist1 < dist2:
            return [int(point1[1]), int(point1[0])]
        else:
            return [int(point2[1]), int(point2[0])]

    # def normlDetect(self, centroid_point):
    #     # 上限与圆心的向量
    #     self.topVector = [self.circleCent[0] -
    #                       self.top[0], self.circleCent[1] - self.top[1]]
    #     # 下限与圆心的向量
    #     self.bottleVector = [self.circleCent[0] -
    #                          self.bottle[0], self.circleCent[1] - self.bottle[1]]
    #     # 指针(最远点)与圆心的向量
    #     self.farVector = [self.circleCent[0] - centroid_point[0],
    #                       self.circleCent[1] - centroid_point[1]]
    #     self.farTopTheta = Functions.GetClockAngle(
    #         self.topVector, self.farVector)
    #     self.topBottleTheta = Functions.GetClockAngle(
    #         self.topVector, self.bottleVector)
    #     if self.farTopTheta < self.topBottleTheta:
    #         return 'normal'
    #     else:
    #         return 'unnormal'
