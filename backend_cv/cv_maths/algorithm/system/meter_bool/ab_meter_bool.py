import importlib
import os
from typing import Any, List
import sys
import cv2
import numpy as np

from backend_common.constants.alarm import ExceedLimitAlarmRule
from backend_common.constants.constant import AlgorithmClassifyEnum, AlgorithmStrategyEnum, FAKE_PERCENT
from backend_common.constants.math_constants import ALGORITHM_CV_USER_DEFINE_PATH, ALGORITHM_CV_SYSTEM_INNER_PATH
from backend_common.utils.util import circle2bbox, render_text_on_bbox
from backend_common.exceptions.inspection_exception import InspectionException
from backend_common.maths.algorithm.base.algorithm_base import AlgorithmBase, AlgorithmStrategy, AlgorithmSettings, AlgorithmDetail, \
    AlgorithmBaseInfo, AlgorithmInput

SUFFIX_START = "sub"


class MeterBoolReader(AlgorithmBase):
    """
    仪表识别算法
    """

    schema_path = os.path.join(os.path.dirname(__file__), "schema.json")

    _name = "meter_bool"
    _description = "仪表识别.基础.版本v1"
    _cn_name = "仪表识别算法"

    # 支持的算法分类个数
    __supported_classify = (AlgorithmClassifyEnum.Primary, AlgorithmClassifyEnum.Secondary)
    # 支持的算法策略，primary -> main & secondary -> main
    __supported_strategies = [
        AlgorithmStrategy(__supported_classify[0], AlgorithmStrategyEnum.Main, "仪表识别策略1",
                          "基于轮廓检测的仪表识别主策略", kv_param={}),
        AlgorithmStrategy(__supported_classify[1], AlgorithmStrategyEnum.Main, "仪表识别策略2",
                          "基于颜色检测的仪表识别主策略", kv_param={})
    ]

    # 支持的算法参数，若干
    __supported_params = AlgorithmBase.json_schema_2_algorithm_params(schema_path)

    def __init__(self, _inputs: AlgorithmInput, _id: int = -1):
        super(MeterBoolReader, self).__init__(self.__class__._name, self.__class__._description,
                                          self.__class__._cn_name,
                                          self.__class__.__supported_strategies,
                                          self.__class__.__supported_params)

        if _inputs.__eq__(dict()):
            return

        self._local_var._inputs = _inputs
        # 算法实例参数
        self._local_var._settings = None

        self._inputImages = self._get_input_images()
        self._meterList = self._get_input_value_by_name("meterList")
        self._meterCount = len(self._meterList)

        # self.scene_image_uri = self._get_scene_img_uri()
        self._processFinalRet = None
        # 检测到的指针针尖点集合, 有多少张图 就初始化多少
        self._processFarPose = [[[0, 0] for _ in self._meterList] for _ in self._inputImages]
        self._processOutputDir = os.getcwd()

        output_define = AlgorithmBase._get_algorithm_schema_info(self.schema_path, "outputDefine")
        self._val_enum = output_define['val_enum']

    def _supported_classify(self):
        return self.__supported_classify

    def _supported_strategy(self):
        return self.__supported_strategies

    def _supported_params(self):
        return self.__supported_params

    def get_algorithm_detail(self, a_type) -> AlgorithmDetail:
        schema_base = AlgorithmBase._get_algorithm_schema_info(self.schema_path, "algorithm")
        info = AlgorithmBaseInfo(self._id, self._name, self._name, self.cn_name, self._description,
                                 batch_support=schema_base.get("batchSupport"),
                                 alarm_support=schema_base.get("alarmSupport"),
                                 is_override_alarm=schema_base.get("isOverrideAlarm"))
        classifies = [self._classify2info(c) for c in self.__supported_classify]
        strategies = [self._strategy2info(s) for s in self.__supported_strategies]
        roi = self._get_roi_define(self.schema_path)
        scene_image = self._get_scene_image(self.schema_path)
        params = AlgorithmBase.json_schema_2_algorithm_params(self.schema_path)  # 重新加载一下, 可能会有刷新
        output_define = self._output_define2info(
            AlgorithmBase._get_algorithm_schema_info(self.schema_path, "outputDefine"))
        sub_list = self.load_algorithm_sublist(self._name, a_type, 'cv')

        return AlgorithmDetail(info, classifies, strategies, roi, scene_image, params, output_define, sub_list)

    def _preprocess(self) -> Any:  # TODO
        pass

    def _do_detect(self) -> (bool, Any, tuple):
        # 再重新初始化一下
        # self._processFarPose = [[[0, 0] for _ in self._meterList] for _ in self._inputImages]
        ret = [[self.read_meter(param_dict, img, idx, m_idx) for m_idx, param_dict in enumerate(self._meterList)] for
               idx, img in enumerate(self._inputImages)]
        # 二维数组 依次按每张图每个仪表的读数
        self._processFinalRet = ret
        #return True, self._processFinalRet, [[[circle2bbox(p['roiSquare']), FAKE_PERCENT] for p in self._meterList] for i in self._inputImages]
        return True, self._processFinalRet, [[[circle2bbox(p['roiSquare']), FAKE_PERCENT, [['line', {"start": p['roiSquare']['center'], "end": p_e}]]]                                           
                                            for p,p_e in zip(self._meterList, self._processFarPose[i])] 
                                            for i,_ in enumerate(self._inputImages)]

    def _postprocess(self) -> Any:
        pass

    def _gen_result_img(self) -> List[np.ndarray]:
        assert self._processFinalRet is not None

        ret_images = []
        for img_idx, one_img_ret in enumerate(self._processFinalRet):
            ret_img = self._inputImages[img_idx]
            for idx, readable in enumerate(one_img_ret):
                # 只显示一位小数
                txt = '%.1f' % readable
                far_pose = self._processFarPose[img_idx][idx]

                circle = self._meterList[idx]['roiSquare']
                zero_point = self._meterList[idx]['zeroPoint']
                start_point = self._meterList[idx]['startPoint']
                end_point = self._meterList[idx]['endPoint']
                # 绘制 读数
                # ret_img = cv2_put_chinese_txt(ret_img, txt, circle['center'], (60, 255, 0))
                # ret_img = cv2.putText(ret_img, txt, circle['center'], cv2.FONT_HERSHEY_SIMPLEX, 1, (60, 255, 0), 2)
                bbox = circle2bbox(circle)
                # x,y to min-max
                bbox = [bbox[0][0], bbox[0][1], bbox[2][0], bbox[2][1]]
                text = f'{txt} {"%.2f" % FAKE_PERCENT}'
                ret_img = render_text_on_bbox(ret_img, bbox, text)

                # 绘制 指针
                cv2.line(ret_img, far_pose, circle['center'], (0, 255, 0), 4, -1)
                cv2.circle(ret_img, zero_point, 2, (0, 0, 255), 2)
                cv2.circle(ret_img, start_point, 2, (60, 255, 0), 2)
                cv2.circle(ret_img, end_point, 2, (60, 255, 0), 2)
            ret_images.append(ret_img)
        return ret_images

    def determine_algorithm_instance(self, settings: AlgorithmSettings = None, index: int = 0,
                                     a_type: str = "system_inner"):
        suffix = f"{SUFFIX_START}{index}"
        module_name = f".{self._name}.ab_{self._name}_{suffix}"

        base_path = ALGORITHM_CV_SYSTEM_INNER_PATH
        if a_type == "user_define":
            base_path = ALGORITHM_CV_USER_DEFINE_PATH

        try:
            M = importlib.import_module(module_name, base_path)
        except ModuleNotFoundError:
            raise InspectionException(f"Auto-load module '{module_name}' from '{base_path}' \
                                failure, please check it manually")

        # !!!!  找出子类中名称以当前 suffix.capitalize()  为 END 的作为 target 算法类  !!!!
        target_clazz_ = [clazz for clazz in MeterBoolReader.__subclasses__()
                         if clazz.__name__.endswith(suffix.capitalize())][0]

        algorithm_instance: MeterBoolReader = target_clazz_(self._local_var._inputs, self._id)
        algorithm_instance._settings = settings

        return algorithm_instance

    def read_meter(self, param_dict, img, img_idx, meter_idx):
        """
        识别并读取仪表读数

        :param param_dict 参数字典
        :param img 输入图片
        :param img_idx 输入图片 index
        :param meter_idx 仪表 index
        """
        raise InspectionException("you must implement this function by yourself!")
    
    def _alarm_trig(self, detect_ret) -> List[List]:
        """
        根据报警规则触发报警
        return:
            ret : 是否报警
            level: 报警等级
            desc: 报警描述
        """
        status, val, _ = detect_ret
        alarms = []
        if self._local_var._settings.alarm_rules is None or len(self._local_var._settings.alarm_rules) == 0:
            return alarms

        for ret_of_each_img in val:
            alm_of_img = []
            for v_idx, v_txt in enumerate(ret_of_each_img):
                # 文本 转 数值, 默认 sys.maxsize 此时按不放过异常处理
                a_v = self._val_enum.get(v_txt, sys.maxsize)
                # 对于一张图仅产生一条汇总报警的，报警逻辑应当走自己的逻辑 比如 安全帽，明火等
                # 此时报警个数 和检测结果数是不一致的, 无法直接 [v_idx]
                rule = self._local_var._settings.alarm_rules[v_idx]
                ret, level, desc = False, None, None
                if isinstance(rule, ExceedLimitAlarmRule):
                    if rule.hh_is_bind and a_v == rule.hh_limit:
                        ret, level, desc = True, "HH", "指针状态异常" if rule.hh_txt is None else rule.hh_txt
                    elif rule.h_is_bind and a_v == rule.h_limit:
                        ret, level, desc = True, "H", "指针状态异常" if rule.h_txt is None else rule.h_txt
                    # elif rule.l_is_bind and a_v == rule.l_limit:
                    #     ret, level, desc = True, "L", "越低限报警" if rule.l_txt is None else rule.l_txt
                    # elif rule.ll_is_bind and a_v == rule.ll_limit:
                    #     ret, level, desc = True, "LL", "越低低限报警" if rule.ll_txt is None else rule.ll_txt
                # TODO support more type AlarmRules
                # alm_of_av.append((ret, level, desc))
                alm_of_img.append((ret, level, desc))
            alarms.append(alm_of_img)
        return alarms
