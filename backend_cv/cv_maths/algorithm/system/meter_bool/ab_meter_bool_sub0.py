from typing import Any

import cv2
import numpy as np
from skimage import measure, morphology

from backend_common.constants.constant import color_list, LOWER, UPPER
from backend_common.exceptions.inspection_exception import AlgorithmProcessException
from backend_common.utils.util import Functions, get_hsv_color_str, get_mask_by_color, get_angle, circle_bounding_box, img_cut_check


class MeterBoolReaderSub0():
    """
    仪表识别算法1
    """

    def _do_detect(self, ori_image, param_dict):
        pointerRes, point = self.read_meter(param_dict, ori_image)
        if self.resultDes is not None:
            meterResult = {"meterResult": {"vaule": pointerRes, "resultDes": self.resultDes}}
        else:
            meterResult = {"meterResult": {"vaule": pointerRes}}
        return True, {"meterResult": meterResult}, [{"dataType": "SQUARE","textObj": meterResult,"coords": circle_bounding_box(self.roi)},  \
                                                    {"dataType": "LINE","coords": {"start":self.roi['center'],"end":{'x':point[0], 'y':point[1]},"direct": False}},
                                                    {"dataType": "LINE","coords": {"start":self.roi['center'],"end":param_dict['startPoint'],"direct": False}},
                                                    {"dataType": "LINE","coords": {"start":self.roi['center'],"end":param_dict['endPoint'],"direct": False}}]

    def _postprocess(self) -> Any:
        pass

    def get_angel(self, point1, point2):
        pose1CentLine = [self.circleCent[0] -
                         point1[0], self.circleCent[1] - point1[1]]
        pose2CentLine = [self.circleCent[0] -
                         point2[0], self.circleCent[1] - point2[1]]
        # 圆心到直线两点的向量夹角
        theta = Functions.get_clock_angle(pose1CentLine, pose2CentLine)
        return theta

    def parse_parameter(self, param_dict):
        """
        识别并读取仪表读数

        :param param_dict 参数字典
        :param img 输入图片
        :param img_idx 输入图片 index
        :param meter_idx 仪表 index
        """
        self.startPoint = list(param_dict['startPoint'].values())
        self.endPoint = list(param_dict['endPoint'].values())
        self.colorThreshold = int(param_dict['colorThreshold'])
        self.roi = param_dict['roiSquare']
        self.circleCent = list(self.roi['center'].values())
        self.circleR_ori = self.roi['radius']
        circleR_clacu = int(0.8 * ((Functions.distances(self.circleCent, self.startPoint) +
                                    Functions.distances(self.circleCent, self.endPoint)) / 2))
        # print(circleR, circleR_clacu)
        self.circleR = min(self.circleR_ori, circleR_clacu)
        self.meter_color_rgb = param_dict['colorPicker']
        self.resultDes = param_dict["resultDes"]


        # 计算角度单位值，即每角度的刻度值
        self.start_angle = get_angle(self.circleCent, self.startPoint)
        # zero_angle = get_angle(self.circleCent, self.zeroPoint)
        end_angle = get_angle(self.circleCent, self.endPoint)
        range_angel = end_angle - self.start_angle
        if range_angel > 0:
            range_angel = 360 - range_angel
        else:
            range_angel = abs(range_angel)
        self.range_angel = range_angel

    def read_meter(self, param_dict, ori_image):
        # 解析参数
        self.parse_parameter(param_dict)
        
        # 截取ROI, 仪表盘切分
        small_roi_img = self.imgCutCircle(ori_image)
        # small_roi_img = self.find_roi_rectangle(ori_image)
        # cv2.imwrite('/opt/tjh/model_server/depl_img/output/roi_img.jpg', small_roi_img)
        # 根据指针颜色mask
        #color_mask = self.edgeFilter(roi_img)
        color_mask = get_mask_by_color(small_roi_img, self.meter_color_rgb, self.colorThreshold, color_picker=True)
        # cv2.imwrite('/opt/tjh/model_server/depl_img/output/mask.jpg', color_mask)
        # 寻找最大连通域并返回质心坐标
        largestConnectComponent, res, slope, point, mean_x_y, skeleton, points_set = self.largestConnectComponentAndPoint(color_mask)
        if not res:
            print("抱歉，没有识别到指针，请再次尝试")
            raise AlgorithmProcessException("抱歉，没有识别到指针，请再次尝试")
        if len(points_set) > 0:
            point = mean_x_y
        point = self.extend_point(small_roi_img, slope, point)
        point[0] += self.coor[0]
        point[1] += self.coor[1]
        # pointer_zero_angel = self.get_angel(self.zeroPoint, point)

        pointer_angel = get_angle(self.circleCent, point)
        pointer_zero_angel = pointer_angel - self.start_angle
        if pointer_zero_angel > 0:
            pointer_zero_angel = 360 - pointer_zero_angel
        else:
            pointer_zero_angel = abs(pointer_zero_angel)
        
        if pointer_zero_angel > self.range_angel:
            return 'False', point
        else:
            return 'True', point
        
    def find_roi_rectangle(self, img):
        # 图黑圆白
        circle = np.zeros(img.shape[:2], dtype="uint8")
        cv2.circle(circle, self.circleCent, self.circleR, 255, -1)
        contours, _ = cv2.findContours(circle,  cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE)
        x, y, w, h = cv2.boundingRect(contours[0])

        # 扩大外接矩形面积, wh设置为xy的两倍，注意如果ROI在图像的边缘，坐标可能会报错，扩大区域可以小一些
        x = x - 10
        y = y - 10
        w = w + 20
        h = h + 20

        new_img = self.bitwiseOr[y:y + h, x:x + w]
        self.coor = [x, y]
        self.samll_circleCent = [self.circleCent[0]-self.coor[0], self.circleCent[1]-self.coor[1]]
        return new_img

    def imgCutCircle(self, img):  
        # 图白圆黑
        circle = np.ones(img.shape, dtype="uint8")
        circle = circle * 255
        cv2.circle(circle, self.circleCent, self.circleR, 0, -1)
        self.bitwiseOr = cv2.bitwise_or(img, circle)
        

        # 修改处
        circleR_ori_expansion = self.circleR_ori + 5
        x = self.circleCent[0] - circleR_ori_expansion
        y = self.circleCent[1] - circleR_ori_expansion
        w = circleR_ori_expansion * 2
        h = circleR_ori_expansion * 2

        if x < 0:
            x = 0
        if x + w > img.shape[1]:
            w = img.shape[1] - x
        if y < 0:
            y = 0
        if y + h > img.shape[0]:
            h = img.shape[0] - y
        img_cut_check(self.bitwiseOr, x + w, y + h)
        self.bitwiseOr = self.bitwiseOr[y:y + h, x:x + w]
        self.coor = [x, y]
        self.samll_circleCent = [self.circleCent[0]-self.coor[0], self.circleCent[1]-self.coor[1]]
        # 修改结束
        return self.bitwiseOr

    def edgeFilter(self, img):
        # 颜色空间mask
        meter_color, point_hsv = get_hsv_color_str(self.meter_color_rgb)
        print('meter color is {}'.format(meter_color))
        if meter_color == 'Red':
            hsv_index_list = [0, 1]
        else:
            hsv_index_list = [color_list.index(meter_color)]
        img_hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)
        image_add = []
        print(hsv_index_list)
        for index in hsv_index_list:
            if LOWER[index][0] < point_hsv[0] < UPPER[index][0]:
                low = np.where((point_hsv-self.colorThreshold) > LOWER[index], point_hsv-self.colorThreshold, LOWER[index])
                up = np.where((point_hsv+self.colorThreshold) < UPPER[index], point_hsv+self.colorThreshold, UPPER[index])
            else:
                low, up = LOWER[index], UPPER[index]
            print(point_hsv, LOWER[index], UPPER[index])
            print(low, up)
            mask_image = cv2.inRange(img_hsv, low, up)
            image_add.append(mask_image)
            #cv2.imwrite(os.path.join(self.out_dir, 'mask_{}.jpg'.format(index)), mask_image)
            if index == 1:
                mask_image = cv2.add(image_add[0], image_add[1])
        return mask_image

    def largestConnectComponentAndPoint(self, mask_edge):
        def get_min_rect(gray):
            # 找到轮廓
            contours, hierarchy = cv2.findContours(gray, cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE)
            # 计算旋转矩形
            rect = cv2.minAreaRect(contours[0])
            # 获取旋转矩形参数
            center, size, angle = rect
            # 转换为int类型
            center = tuple(map(int, center))
            size = tuple(map(int, size))
            # 画出旋转矩形
            rect = cv2.boxPoints(rect)
            rect = np.int0(rect)
            return rect
        labeled_img, num = measure.label(mask_edge, background=0, return_num=True)
        if num == 0:
            return [], 0, 0, 0, 0, 0, 0
        #visual_image= color.label2rgb(labeled_img)
        #cv2.imwrite(os.path.join(self.out_dir, 'ConnectComponent.jpg'), visual_image)
        props = measure.regionprops(labeled_img)

        numPix = []
        for ia in range(len(props)):
            numPix += [props[ia].area]
        
        #像素最多的连通区域及其指引
        # maxnum = max(numPix)
        # index = numPix.index(maxnum)

        areas = []
        out = []
        index = np.argsort(numPix)[::-1]
        # print(index)
        for i in index:
            largestConnectComponent = mask_edge.copy()
            largestConnectComponent[labeled_img!=props[i].label] = 0
            # cv2.imwrite('/opt/tjh/model_server/depl_img/output/ConnectComponent_{}.jpg'.format(i), largestConnectComponent)
            slope, point, skeleton, mean_x_y, points_set = self.lineFit(largestConnectComponent)
            k = slope[1] / slope[0]
            b = point[1] - k * point[0]
            dist = abs(k * self.samll_circleCent[0] - self.samll_circleCent[1] + b) / ((k**2 + 1)**0.5)
            # print(dist, self.circleR)

            rect = get_min_rect(largestConnectComponent)
            # print(rect)
            w = Functions.distances(rect[0], rect[1])
            h = Functions.distances(rect[1], rect[2])
            if min(w,h)==0:
                break
            ratio = max(w,h) / min(w,h)
            # print(w,h,ratio)
            if dist < self.circleR*0.2 and ratio > 2:
                areas.append(numPix[i])
                out.append([largestConnectComponent, 1, slope, point, mean_x_y, skeleton, points_set])
        #         cv2.drawContours(mask_edge_, [rect], 0, (0, 0, 255), 1)
        # cv2.imwrite('/opt/tjh/model_server/cv_device_img/output/mask_rectangle.jpg', mask_edge_)
        if len(out) == 0:
            return [], 0, 0, 0, 0, 0, 0
        else:
            index = areas.index(max(areas))
            return out[index]
    
    def extend_point(self, ori_image, slope, point):
        (vx, vy), (x, y) = slope, point
        blank = np.zeros(ori_image.shape[0:3])
        rows , cols = ori_image.shape[:2]
        lefty = int((-x*vy/vx) + y) 
        righty = int(((cols - x) * vy/vx) + y )
        line_img = cv2.line(blank.copy(), (cols -1 ,righty) , (0,lefty) , (255, 255, 255), 2)
        cicle_img = cv2.circle(blank.copy(), self.samll_circleCent, self.circleR_ori, (255, 255, 255), 1)
        intersection_img = cv2.addWeighted(line_img, 0.5, cicle_img, 0.5, 0)
        # cv2.circle(intersection_img, self.circleCent, 2, (255, 255, 255), 1)
        # cv2.imwrite('/opt/tjh/model_server/depl_img/output/intersection_img.jpg', intersection_img)
        intersection = np.logical_and(line_img, cicle_img)
        # 获得交点位置
        points = np.argwhere(intersection==True)
        point1 = points[0]
        dist1 = Functions.distances(point, (point1[1], point1[0]))
        point2 = points[-1]
        dist2 = Functions.distances(point, (point2[1], point2[0]))
        if dist1 < dist2:
            return [int(point1[1]), int(point1[0])]
        else:
            return [int(point2[1]), int(point2[0])]
        

    def lineFit(self, largestConnectComponent):
        # dst = cv2.distanceTransform(largestConnectComponent, cv2.DIST_L2, 3).astype(np.uint8)
        # dst = np.array(dst/np.max(dst)*255, dtype="uint8")
        # cv2.imwrite(os.path.join(self.out_dir, 'dst.jpg'), dst*255)
        largestConnectComponent_ = largestConnectComponent.copy()
        largestConnectComponent_[largestConnectComponent_==255]=1
        skeleton = morphology.skeletonize(largestConnectComponent_)
        skeleton = np.where(skeleton==True, 1, 0).astype('uint8')

        contours_ = np.argwhere(skeleton==1)
        contours_[:,[0,1]] = contours_[:,[1,0]]

        [vx , vy , x, y] = cv2.fitLine(contours_ , cv2.DIST_L1, 0,0.01 , 0.01)
        line_points = []
        for i in range(-50, 51):
            px = int(x + i * vx)
            py = int(y + i * vy)
            line_points.append([px, py])
        points_set = []
        for point in contours_.tolist():
            result = cv2.pointPolygonTest(np.array(line_points), (point[0], point[1]), False)
            if result==0:
                points_set.append(point)
        
        if len(points_set) == 0:
            mean_x, mean_y = 0, 0
        else:
            # 计算点集的中心
            mean_x, mean_y = np.mean(np.array(points_set), axis=0)
        # print(points_set, mean_x, mean_y)

        return (vx , vy), (int(x), int(y)), skeleton, [int(mean_x), int(mean_y)],points_set
