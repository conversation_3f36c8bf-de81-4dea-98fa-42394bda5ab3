{"algorithmMetadata": {"name": "指针式仪表", "code": "meter", "version": "1.0.0", "description": "指针式仪表识别算法.V1", "type": "openCV", "isBatchProcessing": false, "roiDrawType": "SQUARE", "roiRequired": false, "classification": "DEVICE_METER"}, "input": [{"key": "roiSquare", "label": "仪表区域", "dataType": "CIRCLE", "constraints": {"required": true}, "drawToOsd": true}, {"key": "colorPicker", "label": "指针颜色", "dataType": "RGB", "defaultValue": [0, 0, 0], "constraints": {"required": true, "minLength": 3, "maxlength": 3}, "drawToOsd": false}, {"key": "colorThreshold", "label": "颜色偏差", "dataType": "INTEGER", "defaultValue": 55, "constraints": {"min": 0, "max": 255, "precision": 1, "required": true}, "drawToOsd": false}, {"key": "startPoint", "label": "起点位置", "dataType": "POINT", "constraints": {"required": true}, "drawToOsd": true}, {"key": "endPoint", "label": "终点位置", "dataType": "POINT", "constraints": {"required": true}, "drawToOsd": true}, {"key": "resultDes", "label": "结果描述", "dataType": "STRING", "constraints": {"required": false}, "drawToOsd": true}], "output": [{"key": "meterResult", "label": "仪表结果", "dataType": "BOOLEAN"}]}