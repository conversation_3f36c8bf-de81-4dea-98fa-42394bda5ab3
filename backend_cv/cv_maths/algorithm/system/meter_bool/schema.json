{"algorithm": {"name": "指针式仪表识别算法", "code": "meter_bool", "des": "指针式仪表识别算法.V1", "type": "openCV", "alarmSupport": false, "isOverrideAlarm": false, "batchSupport": false}, "images": ["e:/images/test.jpg"], "roi": {"name": "ROI区域", "type": "polygon", "des": "支持绘制的ROI区域类型, polygon:多边形, circle:圆, ellipse:椭圆", "value": [[325, 116], [331, 115], [346, 310], [337, 310]], "nullable": true}, "targetSceneImage": {"in_use": false, "uri": "", "name": "场景基准图"}, "inputParam": {"meterList": {"name": "仪表盘列表", "type": "array", "value": [], "val_range": [{"roiSquare": {"name": "圆心坐标及半径", "type": "circle", "value": {"center": [100, 200], "radius": 20}, "showable": true, "desc": "圆心坐标及半径"}, "color_picker": {"name": "指针颜色", "type": "color_picker", "value": [0, 255, 0], "des": "从图像中提取到的颜色值(RGB)"}, "colorThreshold": {"name": "颜色偏差", "type": "number", "des": "颜色偏差,值越大颜色区间越大,可根据实际情况酌情调节", "value": 55, "val_range": [0, 255, 1, 55], "nullable": false}, "startPoint": {"name": "起点位置", "type": "point", "value": [123, 456], "showable": true, "desc": "一个point对象，仪表刻度起始点的坐标"}, "endPoint": {"name": "终点位置", "type": "point", "value": [123, 456], "showable": true, "desc": "一个point对象，仪表刻度结束点的坐标"}}], "desc": "支持多个仪表盘作为输入"}}, "outputDefine": {"type": "switch", "desc": "指针是否正常", "val_enum": {"True": 1, "False": 0}, "ruleInfo": [{"desc": "指针状态异常", "isBind": false, "type": "HH", "val": null, "placeHolder": "数值;1:异常,0:正常"}, {"desc": "指针状态异常", "isBind": false, "type": "H", "val": null, "placeHolder": "数值;1:异常,0:正常"}, {"desc": "指针状态异常", "isBind": false, "type": "L", "val": null, "placeHolder": "数值;1:异常,0:正常"}, {"desc": "指针状态异常", "isBind": false, "type": "LL", "val": null, "placeHolder": "数值;1:异常,0:正常"}]}}