from typing import Any, List

import cv2
import numpy as np

# from backend_common.constants.alarm import ExceedLimitAlarmRule
from backend_common.exceptions.inspection_exception import AlgorithmProcessException
from backend_common.exceptions.inspection_exception import InspectionException
from backend_common.maths.algorithm.base.algorithm_base import AlgorithmInput
from backend_common.utils.util import Functions
from cv_maths.algorithm.system.knob_gear.ab_knob_gear import Knob<PERSON>earReader, KnobGearEnum


class KnobGearReaderSub1(KnobGearReader):
    """
    旋钮档位识别算法
    """

    _index =1 
    _description = "旋钮档位识别.主模型.版本v1"
    _cn_name = "旋钮档位识别算法"

    def __init__(self, _inputs: AlgorithmInput, _id: int = -1):
        super(KnobGearReaderSub1, self).__init__(_inputs, _id)

    def _preprocess(self) -> Any:  # TODO
        pass

    def _do_detect(self) -> (bool, Any, tuple):
        return super(KnobGearReaderSub1, self)._do_detect()

    def _postprocess(self) -> Any:
        pass

    def read_knob_gear(self, param_dict, img):
        """
        识别并读取仪表读数

        :param param_dict 参数字典
        :param img 输入图片
        """
        roi = param_dict['roiSquare']
        gear_up1 = param_dict['gearUp1']
        gear_down1 = param_dict['gearDown1']
        gear_up2 = param_dict['gearUp2']
        gear_down2 = param_dict['gearDown2']
        # 档位三是可选的
        gear_up3 = param_dict.get('gearUp3', None)
        gear_down3 = param_dict.get('gearDown3', None)

        # origin_img = img.copy()
        circle_center = roi['center']
        circle_radius = roi['radius']

        gray_img = self.get_button(circle_center, circle_radius, img)
        #cv2.imwrite('/opt/tjh/model_server/git_code_cv/backend_cv/gray_img.jpg', gray_img)
        # 计算圆心与四个点的夹角
        gear_limits = [gear_up1, gear_down1, gear_up2, gear_down2, gear_up3, gear_down3]
        up1, down1, up2, down2, up3, down3 = self.calculate_angel(circle_center, gear_limits)
        dilate_img = self.get_edge(gray_img)
        angels, boxes = self.get_button_angel(img, dilate_img, circle_center, circle_radius)
        if boxes is None:
            print("抱歉，没有识别到旋钮，请再次尝试")
            raise InspectionException("抱歉，没有识别到旋钮，请再次尝试")
        result = self.check_norm(angels, [up1, down1, up2, down2, up3, down3])
        return [result, boxes.tolist()]

    @staticmethod
    def calculate_angel(circle_cent, gear_limits: list):
        """
        计算角度
        :param circle_cent 圆心
        :param gear_limits 各档位从1~n顺序排列的 '下限,上限'
               gear_limits[0] =  firstGearUp
               gear_limits[1] =  firstGearDown
               gear_limits[2] =  secondGearup
               gear_limits[3] =  secondGearDown
               gear_limits[4] =  thirdGearup
               gear_limits[5] =  thirdGearDown
        """
        first_up = 180 - Functions.get_clock_angle((-8, 0),
                                                   (circle_cent[0] - gear_limits[0][0],
                                                    circle_cent[1] - gear_limits[0][1]))
        first_down = 180 - Functions.get_clock_angle((-8, 0), (
            circle_cent[0] - gear_limits[1][0], circle_cent[1] - gear_limits[1][1]))

        second_up = 180 - Functions.get_clock_angle((-8, 0), (
            circle_cent[0] - gear_limits[2][0], circle_cent[1] - gear_limits[2][1]))
        second_down = 180 - Functions.get_clock_angle((-8, 0), (
            circle_cent[0] - gear_limits[3][0], circle_cent[1] - gear_limits[3][1]))
        if first_up < 0:
            first_up += 360
        if first_down < 0:
            first_down += 360
        if second_up < 0:
            second_up += 360
        if second_down< 0:
            second_down += 360
        # 3档是可选的, so 可能为None
        third_up, third_down = None, None
        if gear_limits[4] is not None and gear_limits[5] is not None:
            third_up = 180 - Functions.get_clock_angle((-8, 0), (
                circle_cent[0] - gear_limits[4][0], circle_cent[1] - gear_limits[4][1]))
            third_down = 180 - Functions.get_clock_angle((-8, 0), (
                circle_cent[0] - gear_limits[5][0], circle_cent[1] - gear_limits[5][1]))
            if third_up < 0:
                third_up += 360
            if third_down < 0:
                third_down += 360
        print('每个档位角度 \n', first_up, first_down, second_up, second_down, third_up, third_down)
        return first_up, first_down, second_up, second_down, third_up, third_down

    @staticmethod
    def get_button(circle_cent, circle_r, img):
        """
        获取按钮圆的 灰度图
        """
        circle = np.ones(img.shape, dtype="uint8")
        circle = circle * 255
        cv2.circle(circle, circle_cent, circle_r, 0, -1)
        button_img = cv2.bitwise_or(img, circle)

        dst = cv2.pyrMeanShiftFiltering(button_img, 10, 10)  # 均值滤波
        gray_img = cv2.cvtColor(dst, cv2.COLOR_BGR2GRAY)  # 灰度变换

        return gray_img

    @staticmethod
    def get_edge(gray_img):
        """
        获取按钮圆的图像边缘
        """
        # 如果两个阈值参数都很小则能检测到更多的边缘信息，包括一些杂质
        edge = cv2.Canny(gray_img, 50, 150)
        #kernel = np.ones((5, 5), np.uint8)
        #膨胀
        #dilate_img = cv2.dilate(edge, kernel, iterations=1)
        return edge

    @staticmethod
    def get_button_angel(img, dilate_img, circle_cent, circle_r):
        """
        获取按钮旋转角度
        :param img 原始图像
        :param dilate_img 按钮膨胀后的边缘图
        :param circle_cent 圆心
        :param circle_r 半径
        """
        #cv2.imwrite('/opt/tjh/model_server/git_code_cv/backend_cv/dilate_img.jpg', dilate_img)
        contours, hier = cv2.findContours(dilate_img, cv2.RETR_LIST, cv2.CHAIN_APPROX_SIMPLE)
        angles = []
        boxes = []
        for cnt in contours:
            # 计算轮廓周长
            perimeter = cv2.arcLength(cnt, True)
            # 矩形拟合
            approx = cv2.approxPolyDP(cnt, 0.03 * perimeter, True)
            if len(approx) in [4, 5, 6, 7, 8]:
                # 最小矩形框,（中心点坐标，（宽度，高度）,旋转的角度）
                rect = cv2.minAreaRect(cnt)
                # 最小矩形框的四个点坐标（左下，左上，右下，右上）
                box = cv2.boxPoints(rect)
                # cv2.drawContours(self.img, [cnt], -1, (255, 90, 60), 3)
                # 旋钮识别缩放比例
                scale = 0.5
                # TODO 计算矩形最长边，计算两条最长边中心的平行线，计算中心平行线与圆心的距离，判断距离，符合阈值认为是旋钮
                if (circle_r *2 > rect[1][0] > circle_r * scale or circle_r *2 > rect[1][1] > circle_r * scale) \
                        and (circle_cent[0] - circle_r * scale) < rect[0][0] < (circle_cent[0] + circle_r * scale) \
                        and (circle_cent[1] - circle_r * scale) < rect[0][1] < (circle_cent[1] + circle_r * scale):
                    if box[0][0] >= rect[0][0]:
                        theta = 90 + abs(rect[2])
                        angles.append(theta)
                        boxes.append(box)
                        print("偏转角度：", theta)
                    else:
                        if box[0][1] > circle_cent[1]:
                            theta = 90 + abs(rect[2])
                        else:
                            theta = abs(rect[2])
                        print("偏转角度：", theta)
                        angles.append(theta)
                        boxes.append(box)
                    break
        if len(boxes) == 0:
            raise AlgorithmProcessException("计算旋钮旋转角度失败,请调整参数后重试")
        else:
            return angles, boxes[0].astype('int32')

    @staticmethod
    def check_norm(angels, gear_angles):
        """
        档位检查
        :param angels 按钮识别结果角度数组
        :param gear_angles 档位角度数组
               gear_angles[0] = first_up
               gear_angles[1] = first_down
               gear_angles[2] = second_up
               gear_angles[3] = second_down
               gear_angles[4] = third_up
               gear_angles[5] = third_down

        """
        if not angels:
            raise AlgorithmProcessException('没有检测到旋钮')
        if gear_angles[0] > 180:
            offset = 360-gear_angles[0]
            angels -= offset
            gear_angles = [i-offset if i is not None else None for i in gear_angles]
            gear_angles[0] = 0
        if gear_angles[0] < angels[0] < gear_angles[1]:
            res = KnobGearEnum.FirstGear
        elif gear_angles[2] < angels[0] < gear_angles[3]:
            res = KnobGearEnum.SecondGear
        elif (gear_angles[4] is not None and gear_angles[5] is not None) and \
                gear_angles[4] < angels[0] < gear_angles[5]:
            res = KnobGearEnum.ThirdGear
        else:
            res = KnobGearEnum.Abnormal

        print("旋钮档位", res)
        return res.value[2]

