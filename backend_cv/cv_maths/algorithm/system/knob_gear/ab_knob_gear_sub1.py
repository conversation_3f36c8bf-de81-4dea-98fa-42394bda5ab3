from typing import Any, List

import cv2
import numpy as np

from loguru import logger
from backend_common.exceptions.inspection_exception import AlgorithmProcessException
from backend_common.exceptions.inspection_exception import InspectionException
from backend_common.utils.util import Functions, circle_bounding_box, box2points
from cv_maths.algorithm.system.knob_gear.ab_knob_gear import KnobGearEnum


class KnobGearReaderSub1():
    """
    旋钮档位识别算法2
    """

    def _do_detect(self, ori_image, param_dict) -> (bool, Any, tuple):
        knob_result, knob_box = self.read_knob_gear(param_dict, ori_image)
        if self.resultDes is not None:
            result = {"value":knob_result, "resultDes": self.resultDes}
        else:
            result = {"value":knob_result}
        return True, {"KnobGear": result}, [{"dataType": "SQUARE","textObj": result,"coords": circle_bounding_box(self.roi)},
                                                 {"dataType": "SQUARE","coords": box2points(knob_box)}]


    def Disttances(self, a, b):
        # 返回两点间距离
        from math import sqrt
        x1, y1 = a
        x2, y2 = b
        Disttances = int(sqrt((x1 - x2) ** 2 + (y1 - y2) ** 2))
        return Disttances

    def read_knob_gear(self, param_dict, img):
        """
        识别并读取仪表读数

        :param param_dict 参数字典
        :param img 输入图片
        """
        self.roi = param_dict['circle']
        gear_up1 = list(param_dict['gearDown1'].values())
        gear_down1 = list(param_dict['gearUp1'].values())
        gear_up2 = list(param_dict['gearDown2'].values())
        gear_down2 = list(param_dict['gearUp2'].values())
        # 档位三是可选的
        # gear_up3 = param_dict.get('gearUp3', None)
        # gear_down3 = param_dict.get('gearDown3', None)

        # origin_img = img.copy()
        circle_center = list(self.roi['center'].values())
        circle_radius = self.roi['radius']
        self.resultDes = param_dict['resultDes']

        gray_img = self.get_button(circle_center, circle_radius, img)
        # cv2.imwrite('/opt/tjh/model_server/test_imgs/output/gray_img.jpg', gray_img)
        # 计算圆心与四个点的夹角
        gear_limits = [gear_up1, gear_down1, gear_up2, gear_down2]
        up1, down1, up2, down2 = self.calculate_angel(circle_center, gear_limits)
        dilate_img = self.get_edge(gray_img)
        # cv2.imwrite('/opt/tjh/model_server/test_imgs/output/dilate_img.jpg', dilate_img)
        angels, boxes = self.get_button_angel(img, dilate_img, circle_center, circle_radius)
        if boxes is None:
            logger.info("旋钮档位识别算法2: 没有识别到旋钮，请再次尝试")
            raise InspectionException("没有识别到旋钮，请再次尝试")
        result = self.check_norm(angels, [up1, down1, up2, down2])
        return [result, boxes.tolist()]

    @staticmethod
    def calculate_angel(circle_cent, gear_limits: list):
        """
        计算角度
        :param circle_cent 圆心
        :param gear_limits 各档位从1~n顺序排列的 '下限,上限'
               gear_limits[0] =  firstGearUp
               gear_limits[1] =  firstGearDown
               gear_limits[2] =  secondGearup
               gear_limits[3] =  secondGearDown
               gear_limits[4] =  thirdGearup
               gear_limits[5] =  thirdGearDown
        """
        first_up = 180 - Functions.get_clock_angle((-8, 0),
                                                   (circle_cent[0] - gear_limits[0][0],
                                                    circle_cent[1] - gear_limits[0][1]))
        first_down = 180 - Functions.get_clock_angle((-8, 0), (
            circle_cent[0] - gear_limits[1][0], circle_cent[1] - gear_limits[1][1]))

        second_up = 180 - Functions.get_clock_angle((-8, 0), (
            circle_cent[0] - gear_limits[2][0], circle_cent[1] - gear_limits[2][1]))
        second_down = 180 - Functions.get_clock_angle((-8, 0), (
            circle_cent[0] - gear_limits[3][0], circle_cent[1] - gear_limits[3][1]))
        if first_up < 0:
            first_up += 360
        if first_down < 0:
            first_down += 360
        if second_up < 0:
            second_up += 360
        if second_down< 0:
            second_down += 360
        # 3档是可选的, so 可能为None
        # third_up, third_down = None, None
        # if gear_limits[4] is not None and gear_limits[5] is not None:
        #     third_up = 180 - Functions.get_clock_angle((-8, 0), (
        #         circle_cent[0] - gear_limits[4][0], circle_cent[1] - gear_limits[4][1]))
        #     third_down = 180 - Functions.get_clock_angle((-8, 0), (
        #         circle_cent[0] - gear_limits[5][0], circle_cent[1] - gear_limits[5][1]))
        #     if third_up < 0:
        #         third_up += 360
        #     if third_down < 0:
        #         third_down += 360

        logger.info(f"旋钮档位识别算法2: 每个档位角度: {first_up}, {first_down}, {second_up}, {second_down}")

        return first_up, first_down, second_up, second_down

    @staticmethod
    def get_button(circle_cent, circle_r, img):
        """
        获取按钮圆的 灰度图
        """
        circle = np.ones(img.shape, dtype="uint8")
        circle = circle * 255
        cv2.circle(circle, circle_cent, circle_r, 0, -1)
        button_img = cv2.bitwise_or(img, circle)

        dst = cv2.pyrMeanShiftFiltering(button_img, 10, 10)  # 均值滤波
        gray_img = cv2.cvtColor(dst, cv2.COLOR_BGR2GRAY)  # 灰度变换

        return gray_img

    @staticmethod
    def get_edge(gray_img):
        """
        获取按钮圆的图像边缘
        """
        # 如果两个阈值参数都很小则能检测到更多的边缘信息，包括一些杂质
        edge = cv2.Canny(gray_img, 50, 150)
        #kernel = np.ones((5, 5), np.uint8)
        #膨胀
        #dilate_img = cv2.dilate(edge, kernel, iterations=1)
        return edge

    @staticmethod
    def get_button_angel(img, dilate_img, circle_cent, circle_r):
        """
        获取按钮旋转角度
        :param img 原始图像
        :param dilate_img 按钮膨胀后的边缘图
        :param circle_cent 圆心
        :param circle_r 半径
        """
        #cv2.imwrite('/opt/tjh/model_server/git_code_cv/backend_cv/dilate_img.jpg', dilate_img)
        contours, hier = cv2.findContours(dilate_img, cv2.RETR_LIST, cv2.CHAIN_APPROX_SIMPLE)
        angles = []
        boxes = []
        for cnt in contours:
            # 计算轮廓周长
            perimeter = cv2.arcLength(cnt, True)
            # 矩形拟合
            approx = cv2.approxPolyDP(cnt, 0.03 * perimeter, True)
            if len(approx) in [4, 5, 6, 7, 8]:
                # 最小矩形框,（中心点坐标，（宽度，高度）,旋转的角度）
                rect = cv2.minAreaRect(cnt)
                # 最小矩形框的四个点坐标（左下，左上，右下，右上）
                box = cv2.boxPoints(rect)
                # cv2.drawContours(self.img, [cnt], -1, (255, 90, 60), 3)
                # 旋钮识别缩放比例
                scale = 0.5
                # TODO 计算矩形最长边，计算两条最长边中心的平行线，计算中心平行线与圆心的距离，判断距离，符合阈值认为是旋钮
                if (circle_r *2 > rect[1][0] > circle_r * scale or circle_r *2 > rect[1][1] > circle_r * scale) \
                        and (circle_cent[0] - circle_r * scale) < rect[0][0] < (circle_cent[0] + circle_r * scale) \
                        and (circle_cent[1] - circle_r * scale) < rect[0][1] < (circle_cent[1] + circle_r * scale):
                    if box[0][0] >= rect[0][0]:
                        theta = 90 + abs(rect[2])
                        angles.append(theta)
                        boxes.append(box)
                        print("偏转角度：", theta)
                    else:
                        if box[0][1] > circle_cent[1]:
                            theta = 90 + abs(rect[2])
                        else:
                            theta = abs(rect[2])
                        print("偏转角度：", theta)
                        angles.append(theta)
                        boxes.append(box)
                    break
        if len(boxes) == 0:
            raise AlgorithmProcessException("计算旋钮旋转角度失败,请调整参数后重试")
        else:
            return angles, boxes[0].astype('int32')

    @staticmethod
    def check_norm(angels, gear_angles):
        """
        档位检查
        :param angels 按钮识别结果角度数组
        :param gear_angles 档位角度数组
               gear_angles[0] = first_up
               gear_angles[1] = first_down
               gear_angles[2] = second_up
               gear_angles[3] = second_down

        """
        if not angels:
            raise AlgorithmProcessException('没有检测到旋钮')
        if gear_angles[0] > 180:
            offset = 360-gear_angles[0]
            angels -= offset
            gear_angles = [i-offset if i is not None else None for i in gear_angles]
            gear_angles[0] = 0
        if gear_angles[0] < angels[0] < gear_angles[1]:
            res = KnobGearEnum.FirstGear
        elif gear_angles[2] < angels[0] < gear_angles[3]:
            res = KnobGearEnum.SecondGear
        # elif (gear_angles[4] is not None and gear_angles[5] is not None) and \
        #         gear_angles[4] < angels[0] < gear_angles[5]:
        #     res = KnobGearEnum.ThirdGear
        else:
            res = KnobGearEnum.Abnormal

        logger.info(f"旋钮档位:{res}")
        return res.value[1]
