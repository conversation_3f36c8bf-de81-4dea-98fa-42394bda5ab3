from typing import Any

import cv2
import numpy as np

from backend_common.exceptions.inspection_exception import AlgorithmProcessException
from backend_common.exceptions.inspection_exception import InspectionException
from backend_common.maths.algorithm.base.algorithm_base import AlgorithmInput
from cv_maths.algorithm.system.knob_gear.ab_knob_gear import KnobGear<PERSON>eader, KnobGearEnum
from backend_common.utils.util import Functions, get_mask_by_color
from backend_common.constants.constant import color_list, LOWER, UPPER


class KnobGearReaderSub0(KnobGearReader):
    """
    旋钮档位识别算法
    """

    _index =0 
    _description = "旋钮档位识别.主模型.版本v1"
    _cn_name = "旋钮档位识别算法"

    def __init__(self, _inputs: AlgorithmInput, _id: int = -1):
        super(KnobGearReaderSub0, self).__init__(_inputs, _id)

    def _preprocess(self) -> Any:  # TODO
        pass

    def _do_detect(self) -> (bool, Any, tuple):
        return super(KnobGearReaderSub0, self)._do_detect()

    def _postprocess(self) -> Any:
        pass

    def Disttances(self, a, b):
        # 返回两点间距离
        from math import sqrt
        x1, y1 = a
        x2, y2 = b
        Disttances = int(sqrt((x1 - x2) ** 2 + (y1 - y2) ** 2))
        return Disttances

    def parse_parameter(self, param_dict):
        
        roi = param_dict['roiSquare']
        gear_up1 = param_dict['gearUp1']
        gear_down1 = param_dict['gearDown1']
        gear_up2 = param_dict['gearUp2']
        gear_down2 = param_dict['gearDown2']
        # 档位三是可选的
        gear_up3 = param_dict.get('gearUp3', None)
        gear_down3 = param_dict.get('gearDown3', None)

        # origin_img = img.copy()
        self.circleCent = roi['center']
        circleR = roi['radius']
        circleR_calculate = int(0.85 * ((self.Disttances(self.circleCent, gear_up1) + self.Disttances(self.circleCent, gear_down1) + 
                                         self.Disttances(self.circleCent, gear_up2) + self.Disttances(self.circleCent, gear_down2)) / 4))
        self.circleR = min(circleR, circleR_calculate)
        self.knob_gear_color = param_dict['color_picker']

        # 计算圆心与四个点的夹角
        gear_limits = [gear_up1, gear_down1, gear_up2, gear_down2, gear_up3, gear_down3]
        self.up1, self.down1, self.up2, self.down2, self.up3, self.down3 = self.calculate_angel(self.circleCent, gear_limits)
        

    def read_knob_gear(self, param_dict, ori_image):
        """
        识别并读取仪表读数

        :param param_dict 参数字典
        :param img 输入图片
        """
        self.parse_parameter(param_dict)
        # 仪表盘切分
        roi_img = self.imgCutCircle(ori_image)
        # cv2.imwrite('/opt/tjh/model_server/test_imgs/output//仪表盘.jpg', roi_img)
        # 根据旋钮颜色mask
        # color_mask = self.edgeFilter(roi_img)
        color_mask = get_mask_by_color(roi_img, self.knob_gear_color, color_picker=True)
        # cv2.imwrite('/opt/tjh/model_server/test_imgs/output//mask.jpg', color_mask)
        # 寻找最大连通域并返回液位坐标
        [knob_box, angel], res = self.largestConnectComponentAndPoint(color_mask)
        if not res:
            print("抱歉，没有识别到旋钮，请再次尝试")
            raise InspectionException("抱歉，没有识别到旋钮，请再次尝试")
        knob_result = self.check_norm(angel, [self.up1, self.down1, self.up2, self.down2, self.up3, self.down3])
        return [knob_result, knob_box]

    def imgCutCircle(self, img):  
        # 图白圆黑
        circle = np.ones(img.shape, dtype="uint8")
        circle = circle * 255
        cv2.circle(circle, self.circleCent, self.circleR, 0, -1)
        #cv2.rectangle(rectangle, self.roi[0], self.roi[2], 0, -1)
        self.bitwiseOr = cv2.bitwise_or(img, circle)
        return self.bitwiseOr

    def edgeFilter(self, img):
        # 颜色空间mask
        def getMeterColor(rgb):
            # 得到指针颜色，确定其颜色属性
            rgb = np.array(rgb, dtype='uint8').reshape(1,1,3)[:,:,::-1]
            hsv = cv2.cvtColor(rgb,cv2.COLOR_BGR2HSV)[0][0]
            # pointHsv = []
            # pointHsv.append(img_hsv[self.point_color_position[1], self.point_color_position[0]])
            # #pointHsv.append(img_hsv[403, 1131])
            lower_hsv = np.greater_equal(hsv, LOWER)
            upper_hsv = np.greater_equal(UPPER, hsv)
            hsv_and = np.logical_and(lower_hsv, upper_hsv)     
            hsv_index = np.where(np.all(hsv_and, axis=1))[0][0]
            meter_color = color_list[hsv_index]
            #print('       point color is rgb:{}, hsv:{}, {}.'.format(rgb, hsv, meter_color))
            return meter_color
        knob_gear_color = getMeterColor(self.knob_gear_color)
        if knob_gear_color == 'Red':
            hsv_index_list = [0, 1]
        else:
            hsv_index_list = [color_list.index(knob_gear_color)]
        img_hsv = cv2.cvtColor(img,cv2.COLOR_BGR2HSV)
        image_add = []
        for index in hsv_index_list:
            mask_image = cv2.inRange(img_hsv, LOWER[index], UPPER[index])
        #cv2.imwrite(os.path.join(self.out_dir, 'mask.jpg'), mask)
        #reval_t, thre = cv2.threshold(mask, 0, 255, cv2.THRESH_BINARY | cv2.THRESH_OTSU)
        #cv2.imwrite('thre.jpg', thre)
        #kernel = np.ones((5, 1), np.uint8)
        #self.edge = cv2.erode(mask, kernel, iterations=1)
            #mask_image = cv2.morphologyEx(mask_image, cv2.MORPH_CLOSE,kernel=np.ones((5,5),np.uint8))
            image_add.append(mask_image)
            #cv2.imwrite(os.path.join(self.out_dir, 'mask_{}.jpg'.format(index)), mask_image)
            if index == 1:
                mask_image = cv2.add(image_add[0], image_add[1])
        #mask_image = cv2.morphologyEx(mask_image,cv2.MORPH_OPEN,kernel=np.ones((5,5),np.uint8))
        #cv2.imwrite(os.path.join(self.out_dir, 'mask_pre.jpg'), mask_image)
        #mask_image = cv2.dilate(mask_image, kernel=np.ones((2,2), np.uint8), iterations=1)
        #cv2.imwrite(os.path.join(self.out_dir, 'mask.jpg'), mask_image)
        return mask_image

    def largestConnectComponentAndPoint(self, mask_edge):
        from skimage import measure
        labeled_img, num = measure.label(mask_edge, background=0, return_num=True)
        if num == 0:
            return [None,None], 0
        #visual_image= color.label2rgb(labeled_img)
        #cv2.imwrite(os.path.join(self.out_dir, 'ConnectComponent.jpg'), visual_image)
        props = measure.regionprops(labeled_img)

        numPix = []
        for ia in range(len(props)):
            numPix += [props[ia].area]

        #像素最多的连通区域及其指引
        maxnum = max(numPix)
        index = numPix.index(maxnum)

        largestConnectComponent = mask_edge.copy()
        largestConnectComponent[labeled_img!=props[index].label] = 0
        # cv2.imwrite('/opt/tjh/model_server/test_imgs/output/ConnectComponent.jpg', largestConnectComponent)
        coords = props[index].coords
        coords[:,[0,1]] = coords[:,[1,0]]
        minRect = cv2.minAreaRect(coords)
        ##最小矩形框的四个点坐标(xmin点，---> ymax点)顺时针，x轴顺时针旋转最先重合的边为w，angle为x轴顺时针旋转的角度，angle取值为(0,90]
        knob_box = np.int0(cv2.boxPoints(minRect))
        print(minRect)

        if self.Disttances(knob_box[3], knob_box[0]) > self.Disttances(knob_box[3], knob_box[2]):
            angel = minRect[2]
        else:
            angel = 90 + minRect[2]
        return [knob_box.tolist(), angel], 1
        # knob_box[:,[0,1]] = knob_box[:,[1,0]]
        # max_y_index = list(knob_box[:,1]).index((max(knob_box[:,1])))
        # if max_y_index==3:
        #     next_index = 1
        # else:
        #     next_index = max_y_index + 1
        # if self.Disttances(knob_box[max_y_index], knob_box[next_index]) > self.Disttances(knob_box[max_y_index], knob_box[max_y_index-1]):
        #     angel = 180 - minRect[2]
        # else:
        #     angel = 90 - minRect[2]

        #bbox = props[index].bbox
        #bbox_left_top_point = (bbox[1], bbox[0])
        #def intersection_point(point1, point2, bbox_left_top_point):
        #    a = (point1[1] - point2[1]) / (point1[0] - point2[0])
        #    b = point1[1] - a * point1[0]
        #    y = a * bbox_left_top_point[0] + b
        #    return (bbox_left_top_point[0], int(y))
        #liquid_point = intersection_point(self.endPoint, self.startPoint, bbox_left_top_point)
        # knob_box = knob_box[np.newaxis,:]
        # knob_box[:, [0,1,2,3]] = knob_box[:, [1,2,3,0]]
        # return [knob_box.tolist()[0], angel], 1


    @staticmethod
    def calculate_angel(circle_cent, gear_limits: list):
        """
        计算角度
        :param circle_cent 圆心
        :param gear_limits 各档位从1~n顺序排列的 '下限,上限'
               gear_limits[0] =  firstGearUp
               gear_limits[1] =  firstGearDown
               gear_limits[2] =  secondGearup
               gear_limits[3] =  secondGearDown
               gear_limits[4] =  thirdGearup
               gear_limits[5] =  thirdGearDown
        """
        first_up = 180 - Functions.get_clock_angle((-8, 0),
                                                   (circle_cent[0] - gear_limits[0][0],
                                                    circle_cent[1] - gear_limits[0][1]))
        first_down = 180 - Functions.get_clock_angle((-8, 0), (
            circle_cent[0] - gear_limits[1][0], circle_cent[1] - gear_limits[1][1]))

        second_up = 180 - Functions.get_clock_angle((-8, 0), (
            circle_cent[0] - gear_limits[2][0], circle_cent[1] - gear_limits[2][1]))
        second_down = 180 - Functions.get_clock_angle((-8, 0), (
            circle_cent[0] - gear_limits[3][0], circle_cent[1] - gear_limits[3][1]))
        if first_up < 0:
            first_up += 360
        if first_down < 0:
            first_down += 360
        if second_up < 0:
            second_up += 360
        if second_down< 0:
            second_down += 360
        if gear_limits[4] is not None and gear_limits[5] is not None:
            third_up = 180 - Functions.get_clock_angle((-8, 0), (
                circle_cent[0] - gear_limits[4][0], circle_cent[1] - gear_limits[4][1]))
            third_down = 180 - Functions.get_clock_angle((-8, 0), (
                circle_cent[0] - gear_limits[5][0], circle_cent[1] - gear_limits[5][1]))
            if third_up < 0:
                third_up += 360
            if third_down < 0:
                third_down += 360
            print('每个档位角度 \n', first_up, first_down, second_up, second_down, third_up, third_down)
            return first_up, first_down, second_up, second_down, third_up, third_down
        else:
            print('每个档位角度 \n', first_up, first_down, second_up, second_down)
            return first_up, first_down, second_up, second_down, None, None

    @staticmethod
    def check_norm(angels, gear_angles):
        """
        档位检查
        :param angels 按钮识别结果角度数组
        :param gear_angles 档位角度数组
               gear_angles[0] = first_up
               gear_angles[1] = first_down
               gear_angles[2] = second_up
               gear_angles[3] = second_down
               gear_angles[4] = third_up
               gear_angles[5] = third_down

        """
        if not angels:
            raise AlgorithmProcessException('没有检测到旋钮')
        if gear_angles[0] > 180:
            offset = 360-gear_angles[0]
            angels -= offset
            gear_angles = [i-offset if i is not None else None for i in gear_angles]
            gear_angles[0] = 0
        if gear_angles[0] < angels < gear_angles[1]:
            res = KnobGearEnum.FirstGear
        elif gear_angles[2] < angels < gear_angles[3]:
            res = KnobGearEnum.SecondGear
        elif (gear_angles[4] is not None and gear_angles[5] is not None) and (gear_angles[4] < angels < gear_angles[5]):
            res = KnobGearEnum.ThirdGear
        else:
            res = KnobGearEnum.Abnormal
        print("旋转角度：{},\n旋钮档位:{}".format(angels, res))
        return res.value[2]
