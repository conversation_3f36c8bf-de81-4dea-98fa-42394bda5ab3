import importlib
import os
from enum import Enum
from typing import Any, List

from loguru import logger
import traceback

from backend_common.maths.algorithm.base.algorithm_base import AlgorithmBase, AlgorithmInput
from backend_common.exceptions.inspection_exception import AlgorithmProcessException


class KnobGearEnum(Enum):
    """
    按钮档位枚举
    """
    FirstGear = (1, "一档", "firstGear")
    SecondGear = (2, "二档", "secondGear")
    Abnormal = (3, "其他档位", "abnormal")

    @classmethod
    def from_str(cls, txt):
        for k, v in cls.__members__.items():
            if k.casefold() == txt.casefold():
                return v
        else:
            raise ValueError(f"'{cls.__name__}' enum not found for '{txt}'")

    @classmethod
    def value_of(cls, value):
        for k, v in cls.__members__.items():
            if v.value[0] == value:
                return v
        else:
            raise ValueError(f"'{cls.__name__}' enum not found for '{value}'")


class KnobGearReader(AlgorithmBase):
    """
    旋钮挡位识别算法
    """
    _name = "knob_gear"

    def __init__(self, _inputs: AlgorithmInput, _id: int = -1):
        super(KnobGearReader, self).__init__(self.__class__._name)

        if _inputs.__eq__(dict()):
            return

        self._local_var._inputs = _inputs
        # 算法实例参数
        self._local_var._settings = None
        self.originImg = self._get_input_images()[0]
        self.roi = self._get_roi()
        self.input_param = self._get_input_param()


    def _do_detect(self) -> (bool, Any, tuple):
        """
        核心检测算法
        return:
            ret: 检测是否成功
            val: 数值化结果(如果有,若没有留空)
            points: 检测到的关键结果点集(具体每个点含义由各算法自行约定)

        """
        from cv_maths.algorithm.system.knob_gear.ab_knob_gear_sub0 import KnobGearReaderSub0
        from cv_maths.algorithm.system.knob_gear.ab_knob_gear_sub1 import KnobGearReaderSub1
        try:
            isSuccess, output, osdInfo = KnobGearReaderSub0()._do_detect(self.originImg, self.input_param)
            logger.info('Algorithm 1 executed successfully')
        except (AlgorithmProcessException, Exception) as Algorithm1e:
            tb = traceback.extract_tb(Algorithm1e.__traceback__)
            filename, lineno, funcname, text = tb[-1]
            logger.error(f"Error in file {filename}, line {lineno}, in {funcname}: {text}")
            try:
                isSuccess, output, osdInfo = KnobGearReaderSub1()._do_detect(self.originImg, self.input_param)
                logger.info(f'Algorithm 2 executed successfully')
            except Exception as e:
                # logger.info(f"算法检测异常,")
                tb = traceback.extract_tb(e.__traceback__)
                filename, lineno, funcname, text = tb[-1]
                logger.error(f"Error in file {filename}, line {lineno}, in {funcname}: {text}")
                if isinstance(Algorithm1e, AlgorithmProcessException):
                    raise AlgorithmProcessException(Algorithm1e.__str__())
                else:
                    raise Exception(Algorithm1e.__str__())
        return isSuccess, output, osdInfo
