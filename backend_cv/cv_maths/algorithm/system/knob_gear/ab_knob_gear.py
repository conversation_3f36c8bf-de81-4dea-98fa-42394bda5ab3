import importlib
import os
from enum import Enum
from typing import Any, List

import cv2
import numpy as np

from backend_common.maths.algorithm.base.algorithm_base import AlgorithmBase, AlgorithmStrategy, AlgorithmSettings, \
    AlgorithmDetail, \
    AlgorithmBaseInfo, AlgorithmInput
from backend_common.utils.util import circle2bbox, render_text_on_bbox
from backend_common.constants.constant import AlgorithmClassifyEnum, AlgorithmStrategyEnum, FAKE_PERCENT
from backend_common.constants.math_constants import ALGORITHM_CV_USER_DEFINE_PATH, ALGORITHM_CV_SYSTEM_INNER_PATH
from backend_common.exceptions.inspection_exception import InspectionException
from backend_common.constants.alarm import ExceedLimitAlarmRule

SUFFIX_START = "sub"


class KnobGearEnum(Enum):
    """
    按钮档位枚举
    """
    FirstGear = (1, "一档", "firstGear")
    SecondGear = (2, "二档", "secondGear")
    ThirdGear = (3, "三档", "thirdGear")
    Abnormal = (4, "异常", "abnormal")

    @classmethod
    def from_str(cls, txt):
        for k, v in cls.__members__.items():
            if k.casefold() == txt.casefold():
                return v
        else:
            raise ValueError(f"'{cls.__name__}' enum not found for '{txt}'")

    @classmethod
    def value_of(cls, value):
        for k, v in cls.__members__.items():
            if v.value[0] == value:
                return v
        else:
            raise ValueError(f"'{cls.__name__}' enum not found for '{value}'")


class KnobGearReader(AlgorithmBase):
    """
    旋钮挡位识别算法
    """

    schema_path = os.path.join(os.path.dirname(__file__), "schema.json")

    _name = "knob_gear"
    _description = "旋钮挡位识别.基础.版本v1"
    _cn_name = "旋钮挡位识别算法"

    # 支持的算法分类个数
    __supported_classify = (AlgorithmClassifyEnum.Primary, AlgorithmClassifyEnum.Secondary)
    # 支持的算法策略，primary -> main & secondary -> main
    __supported_strategies = [
        AlgorithmStrategy(__supported_classify[0], AlgorithmStrategyEnum.Main, "旋钮挡位识别策略1",
                          "基于颜色检测的旋钮挡位识别主策略", kv_param={}),
        AlgorithmStrategy(__supported_classify[1], AlgorithmStrategyEnum.Main, "旋钮挡位识别策略2",
                          "基于轮廓检测的旋钮挡位识别主策略", kv_param={})
    ]

    # 支持的算法参数，若干
    __supported_params = AlgorithmBase.json_schema_2_algorithm_params(schema_path)

    def __init__(self, _inputs: AlgorithmInput, _id: int = -1):
        super(KnobGearReader, self).__init__(self.__class__._name, self.__class__._description,
                                             self.__class__._cn_name,
                                             self.__class__.__supported_strategies,
                                             self.__class__.__supported_params)

        if _inputs.__eq__(dict()):
            return

        self._local_var._inputs = _inputs
        # 算法实例参数
        self._local_var._settings = None

        self._inputImages = self._get_input_images()
        self._knobList = self._get_input_value_by_name("knobList")
        self._knobCount = len(self._knobList)

        # self._processCutTarget = None  # TODO
        # self._processFinalArea = None
        self._processFinalRet = None
        self._processOutputDir = os.getcwd()

        output_define = AlgorithmBase._get_algorithm_schema_info(self.schema_path, "outputDefine")
        self._val_enum = output_define['val_enum']

    def _supported_classify(self):
        return self.__supported_classify

    def _supported_strategy(self):
        return self.__supported_strategies

    def _supported_params(self):
        return self.__supported_params

    def get_algorithm_detail(self, a_type) -> AlgorithmDetail:
        """
        获取算法详情信息
        """
        schema_base = AlgorithmBase._get_algorithm_schema_info(self.schema_path, "algorithm")
        info = AlgorithmBaseInfo(self._id, self._name, self._name, self.cn_name, self._description,
                                 batch_support=schema_base.get("batchSupport"),
                                 alarm_support=schema_base.get("alarmSupport"),
                                 is_override_alarm=schema_base.get("isOverrideAlarm"),
                                 result_show_type=schema_base.get("resultShowType"))
        classifies = [self._classify2info(c) for c in self.__supported_classify]
        strategies = [self._strategy2info(s) for s in self.__supported_strategies]
        roi = self._get_roi_define(self.schema_path)
        scene_image = self._get_scene_image(self.schema_path)
        params = AlgorithmBase.json_schema_2_algorithm_params(self.schema_path)  # 重新加载一下, 可能会有刷新
        output_define = self._output_define2info(
            AlgorithmBase._get_algorithm_schema_info(self.schema_path, "outputDefine"))
        sub_list = self.load_algorithm_sublist(self._name, a_type, 'cv')

        return AlgorithmDetail(info, classifies, strategies, roi, scene_image, params, output_define, sub_list)

    def _preprocess(self) -> Any:  # TODO
        pass

    def _do_detect(self) -> (bool, Any, tuple):
        ret = [[self.read_knob_gear(param_dict, img) for param_dict in self._knobList] for img in
               self._inputImages]
        # print(ret)
        # square = [[self.read_knob_gear(param_dict, img)[1] for param_dict in self._knobList] for img in
        #        self._inputImages]
        # 二维数组 依次按每张图每个仪表的读数和液位识别框
        self._processFinalRet = [[ret[i][j][0] for j, p in enumerate(self._knobList)] for i, _ in enumerate(self._inputImages)]
        self.liquidSquare = [[ret[i][j][1] for j, p in enumerate(self._knobList)] for i, _ in enumerate(self._inputImages)]
        return True, self._processFinalRet, [[[circle2bbox(p['roiSquare']), FAKE_PERCENT, [['square'] +  [self.liquidSquare[i][j]]]] 
                                            for j, p in enumerate(self._knobList)] 
                                            for i, _ in enumerate(self._inputImages)]

    def _postprocess(self) -> Any:
        pass

    def _gen_result_img(self) -> List[np.ndarray]:
        assert self._processFinalRet is not None

        ret_images = []
        for img_idx, one_img_ret in enumerate(self._processFinalRet):
            ret_img = self._inputImages[img_idx]
            for idx, numb in enumerate(one_img_ret):
                # 只显示一位小数
                txt = KnobGearEnum.from_str(numb).value[1]  # (档位, 文本)

                circle = self._knobList[idx]['roiSquare']

                bbox = circle2bbox(circle)
                # x,y to min-max
                bbox = [bbox[0][0], bbox[0][1], bbox[2][0], bbox[2][1]]

                text = f'{txt} {"%.2f" % FAKE_PERCENT}'
                liquidSquare = self.liquidSquare[img_idx][idx]
                cv2.drawContours(ret_img, np.array([liquidSquare]), 0, (0, 0, 255), 2)
                ret_img = render_text_on_bbox(ret_img, bbox, text)
                points = [v for k, v in self._knobList[idx].items() if isinstance(v, list) and len(v) == 2]
                for i, point in enumerate(points):
                    cv2.circle(ret_img, point, 2, (0, i * 50, 255), 2)
            ret_images.append(ret_img)
        return ret_images

    def determine_algorithm_instance(self, settings: AlgorithmSettings = None, index: int = 0,
                                     a_type: str = "system_inner"):
        suffix = f"{SUFFIX_START}{index}"
        module_name = f".{self._name}.ab_{self._name}_{suffix}"

        base_path = ALGORITHM_CV_SYSTEM_INNER_PATH
        if a_type == "user_define":
            base_path = ALGORITHM_CV_USER_DEFINE_PATH

        try:
            M = importlib.import_module(module_name, base_path)
        except ModuleNotFoundError:
            raise InspectionException(f"Auto-load module '{module_name}' from '{base_path}' \
                                failure, please check it manually")

        # !!!!  找出子类中名称以当前 suffix.capitalize()  为 END 的作为 target 算法类  !!!!
        target_clazz_ = [clazz for clazz in KnobGearReader.__subclasses__()
                         if clazz.__name__.endswith(suffix.capitalize())][0]

        algorithm_instance: KnobGearReader = target_clazz_(self._local_var._inputs, self._id)
        algorithm_instance._settings = settings

        return algorithm_instance
    
    def _alarm_trig(self, detect_ret) -> List[List]:
        """
        根据报警规则触发报警
        return:
            ret : 是否报警
            level: 报警等级
            desc: 报警描述
        """
        status, val, _ = detect_ret
        alarms = []
        if self._local_var._settings.alarm_rules is None or len(self._local_var._settings.alarm_rules) == 0:
            return alarms

        for ret_of_each_img in val:
            alm_of_img = []
            for v_idx, v_txt in enumerate(ret_of_each_img):
                # 文本 转 数值, 默认 abnormal: 4， @see schema.json outputDefine.val_enum.abnormal
                a_v = self._val_enum.get(v_txt, 4)
                # 对于一张图仅产生一条汇总报警的，报警逻辑应当走自己的逻辑 比如 安全帽，明火等
                # 此时报警个数 和检测结果数是不一致的, 无法直接 [v_idx]
                rule = self._local_var._settings.alarm_rules[v_idx]
                ret, level, desc = False, None, None
                if isinstance(rule, ExceedLimitAlarmRule):
                    if rule.hh_is_bind and a_v == rule.hh_limit:
                        ret, level, desc = True, "HH", "旋钮档位异常" if rule.hh_txt is None else rule.hh_txt
                    elif rule.h_is_bind and a_v == rule.h_limit:
                        ret, level, desc = True, "H", "旋钮档位异常" if rule.h_txt is None else rule.h_txt
                    elif rule.l_is_bind and a_v == rule.l_limit:
                        ret, level, desc = True, "L", "旋钮档位异常" if rule.l_txt is None else rule.l_txt
                    elif rule.ll_is_bind and a_v == rule.ll_limit:
                        ret, level, desc = True, "LL", "旋钮档位异常" if rule.ll_txt is None else rule.ll_txt
                # TODO support more type AlarmRules
                # alm_of_av.append((ret, level, desc))
                alm_of_img.append((ret, level, desc))
            alarms.append(alm_of_img)
        return alarms

    def read_knob_gear(self, param_dict, img):
        """
        识别并读取旋钮档位

        :param param_dict 参数字典
        :param img 输入图片
        """
        raise InspectionException("you must implement this function by yourself!")
