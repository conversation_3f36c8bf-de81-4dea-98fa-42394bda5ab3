{"algorithm": {"name": "旋钮挡位识别算法", "code": "knob_gear", "des": "旋钮挡位识别算法.V1", "type": "openCV", "alarmSupport": true, "isOverrideAlarm": true, "batchSupport": true, "resultShowType": "RESULT"}, "images": ["e:/images/test.jpg"], "roi": {"name": "ROI区域", "type": "polygon", "des": "支持绘制的ROI区域类型, polygon:多边形, circle:圆, ellipse:椭圆", "value": [[325, 116], [331, 115], [346, 310], [337, 310]], "nullable": true}, "targetSceneImage": {"in_use": false, "uri": "", "name": "场景基准图"}, "inputParam": {"knobList": {"name": "旋钮列表", "type": "array", "value": [], "val_range": [{"color_picker": {"name": "旋钮颜色", "type": "color_picker", "value": [0, 255, 0], "des": "从图像中提取到 旋钮突起部位 的颜色值(RGB)"}, "roiSquare": {"name": "圆心坐标及半径1", "type": "circle", "value": {"center": [1220, 793], "radius": 45}, "des": "圆心坐标及半径", "showable": true}, "gearUp1": {"name": "一档上限1", "type": "point", "value": [123, 456], "showable": true, "des": "一个point对象，点的坐标,一档必填"}, "gearDown1": {"name": "一档下限1", "type": "point", "value": [123, 456], "showable": true, "des": "一个point对象，点的坐标，一档必填"}, "gearUp2": {"name": "二档上限1", "type": "point", "value": [123, 456], "showable": true, "des": "一个point对象，点的坐标，二档必填"}, "gearDown2": {"name": "二档下限1", "type": "point", "value": [123, 456], "showable": true, "des": "一个point对象，点的坐标，二档必填"}, "gearUp3": {"name": "三档上限1", "type": "point", "value": [123, 456], "showable": true, "des": "一个point对象，点的坐标，三档可选", "nullable": true}, "gearDown3": {"name": "三档下限1", "type": "point", "value": [123, 456], "showable": true, "des": "一个point对象，点的坐标，三档可选", "nullable": true}, "resultDes": {"name": "结果描述", "type": "string", "desc": "结果展示的描述文字"}}], "desc": "支持多个旋钮作为输入"}}, "outputDefine": {"type": "string", "desc": "旋钮读数-模拟量", "val_enum": {"firstGear": 1, "secondGear": 2, "thirdGear": 3, "abnormal": 4}, "ruleInfo": [{"desc": "旋钮档位异常", "isBind": false, "type": "HH", "val": null, "placeHolder": "大于等于时报警;1~3:1档~3档;4:异常"}, {"desc": "旋钮档位异常", "isBind": false, "type": "H", "val": null, "placeHolder": "大于等于时报警;1~3:1档~3档;4:异常"}, {"desc": "旋钮档位异常", "isBind": false, "type": "L", "val": null, "placeHolder": "小于等于时报警;1~3:1档~3档;4:异常"}, {"desc": "旋钮档位异常", "isBind": false, "type": "LL", "val": null, "placeHolder": "小于等于时报警;1~3:1档~3档;4:异常"}]}}