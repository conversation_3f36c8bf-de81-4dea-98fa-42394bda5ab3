{"algorithmMetadata": {"name": "旋钮挡位", "code": "knob_gear", "version": "1.0.0", "description": "旋钮挡位识别.V1", "type": "openCV", "isBatchProcessing": false, "roiDrawType": "SQUARE", "roiRequired": false, "classification": "DEVICE_METER"}, "input": [{"key": "circle", "label": "旋钮截取圆", "dataType": "CIRCLE", "constraints": {"required": true}, "drawToOsd": true}, {"key": "colorPicker", "label": "旋钮颜色", "dataType": "RGB", "defaultValue": [0, 0, 0], "constraints": {"required": true, "minLength": 3, "maxlength": 3}, "drawToOsd": false}, {"key": "gearDown1", "label": "一档下限", "dataType": "POINT", "constraints": {"required": true}, "drawToOsd": true}, {"key": "gearUp1", "label": "一档上限", "dataType": "POINT", "constraints": {"required": true}, "drawToOsd": true}, {"key": "gearDown2", "label": "二档下限", "dataType": "POINT", "constraints": {"required": true}, "drawToOsd": true}, {"key": "gearUp2", "label": "二档上限", "dataType": "POINT", "constraints": {"required": true}, "drawToOsd": true}, {"key": "resultDes", "label": "结果描述", "dataType": "STRING", "constraints": {"required": false}, "drawToOsd": true}], "output": [{"key": "KnobGear", "label": "旋钮结果", "dataType": "STRING"}]}