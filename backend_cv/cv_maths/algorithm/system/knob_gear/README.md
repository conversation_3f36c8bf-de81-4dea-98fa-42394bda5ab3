## 旋钮挡位识别算法V1.0.0

### 描述

该算法用于旋钮挡位识别，支持工厂以及实验室大多数旋钮识别。旋钮颜色参数建议选取图片中旋钮内大多数相同的颜色。
- 是否配置ROI：否
- 是否配置多图输入：否
- 算法一 基于颜色检测的旋钮挡位识别策略
- 算法二 基于轮廓检测的旋钮挡位识别策略

### 输入参数

| 参数名     | 是否绘制 | 参数类型    | 默认值 | 是否必填 | 数据范围   | 精度   |描述
|-------|--------------|----------------------|--------------|----------------------|--------------|----------------------|----------------------|
| 旋钮截取圆 | 是|CIRCLE | -|是|-|-|圆，包含旋钮在内的识别区域 |
| 旋钮颜色 | 否|RGB | [0, 0, 0] |是|0-255|1|取色笔，指示灯亮的颜色|
| 一档下限 | 是|POINT |-|是|-|-| 坐标点，一档的下限位置 |
| 一档上限 | 是|POINT |-|是|-|-| 坐标点，一档的上限位置 |
| 二档下限 | 是|POINT |-|是|-|-| 坐标点，二档的下限位置 |
| 二档上限 | 是|POINT |-|是|-|-| 坐标点，二档的上限位置 |
| 结果描述 | 是|STRING|-|否|-|-| 结果描述文字，例如主油箱旋钮|

### 输出参数

| 参数名         | 参数类型   | 描述               |
| ----------- | ------ | ---------------- |
| KnobGear | STRING | 旋钮挡位识别，例如识别为一档 |
| resultDes|STRING| 结果描述文字，例如主油箱旋钮|

### 结果展示

| 名称        | 描述                 |
|-----------|--------------------|
| 旋钮位置      | 矩形，代表识别出的旋钮        |
| 旋钮挡位      | 字符串，旋钮挡位识别，例如识别为一档 |
| 旋钮截取圆外接矩形 | 矩形                 |