## 旋钮挡位识别算法
## 版本:

 v 1.0.0

## 描述:

 该算法用于旋钮挡位识别，支持工厂以及实验室大多数旋钮识别。使用建议:

1) 旋钮颜色参数建议选取图片中旋钮内大多数相同的颜色。

## 输入:

| 名称 | 类型 | 描述 |
|-------|--------------|----------------------|
| 圆心坐标及半径 | circle | 圆，仪表包含指针在内的识别区域 |
| 旋钮颜色 | color_picker | 取色笔，旋钮的颜色 |
| 一档上限 | point | 坐标点，item为int，len=2，一档的上限位置 |
| 一档下限 | point | 坐标点，item为int，len=2，一档的下限位置 |
| 二档上限 | point | 坐标点，item为int，len=2，二档的上限位置 |
| 二档下限 | point | 坐标点，item为int，len=2，二档的下限位置 |
| 三档上限 | point | 坐标点，item为int，len=2，三档的上限位置 |
| 三档下限 | point | 坐标点，item为int，len=2，三档的下限位置 |

* 是否支持单图批量识别：是

## 输出：

| 名称 | 类型 | 描述 |
|-------|--------------|----------------------|
| 旋钮位置 | square | 用矩形代表识别出的旋钮 |
| 旋钮挡位 | number | 数值，旋钮挡位识别，例如识别为一档 |
| 置信度 | number | 数值，0-1之间，代表识别出来旋钮挡位的可信度 |