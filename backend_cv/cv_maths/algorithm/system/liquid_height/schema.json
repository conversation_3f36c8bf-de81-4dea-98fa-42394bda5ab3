{"algorithm": {"name": "料液高度识别", "code": "liquid_height", "des": "料液高度识别.V1", "type": "openCV", "alarmSupport": false, "isOverrideAlarm": false, "batchSupport": false}, "roi": {"name": "ROI区域", "type": "polygon", "des": "支持绘制的ROI区域类型, polygon:多边形, circle:圆, ellipse:椭圆", "value": [[325, 116], [331, 115], [346, 310], [337, 310]], "nullable": true}, "targetSceneImage": {"in_use": false, "uri": "", "name": "场景基准图"}, "images": ["e:/images/test.jpg"], "inputParam": {"roiSquare": {"name": "圆心坐标及半径", "type": "circle", "value": {"center": [100, 200], "radius": 20}, "showable": true, "desc": "圆心坐标及半径"}, "binary_thresh": {"name": "分割阈值", "type": "number", "des": "分割阈值,可根据实际情况调节", "value": 70, "val_range": [0, 255, 1, 70], "nullable": false}, "heigh_percent": {"name": "液体高度百分比", "type": "number", "des": "料液高度偏差百分比", "value": 0.97, "val_range": [0, 1, 0.01, 0.97], "nullable": false}}, "outputDefine": {"type": "string", "desc": "液体高度", "val_enum": {"full": 1, "not_full": 0}, "ruleInfo": [{"desc": "液体高度越高高限", "isBind": false, "type": "HH", "val": 0.0, "placeHolder": "数值;液体高度识别结果"}, {"desc": "液体高度越高限", "isBind": false, "type": "H", "val": 0.0, "placeHolder": "数值;液体高度识别结果"}, {"desc": "液体高度越低限", "isBind": false, "type": "L", "val": 0.0, "placeHolder": "数值;液体高度识别结果"}, {"desc": "液体高度越低低限", "isBind": false, "type": "LL", "val": 0.0, "placeHolder": "数值;液体高度识别结果"}]}}