## 料液高度识别算法

## 版本:

 v 1.0.0

## 描述:

 该算法用于识别圆形管道窗的液位高度

## 输入:

| 名称      | 类型     | 描述                     |
| ------- | ------ | ---------------------- |
| 圆心坐标及半径 | circle | 圆，圆形管道窗识别区域            |
| 分割阈值    | number | 0-255，可自行设定阈值          |
| 液体高度百分比 | number | 0-1，高度占比，如果达到比例则认为液位为满 |

* 是否支持单图批量识别：否

## 输出：

| 名称     | 类型     | 描述                 |
| ------ | ------ | ------------------ |
| 液位是否为满 | string | 满：full 不满：not_full |