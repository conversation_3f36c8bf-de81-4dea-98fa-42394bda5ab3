from typing import Any

import cv2
import numpy as np

from backend_common.exceptions.inspection_exception import AlgorithmProcessException
from backend_common.maths.algorithm.base.algorithm_base import AlgorithmInput

from cv_maths.algorithm.system.liquid_height import LiquidHeightReader


class LiquidHeightReaderSub0(LiquidHeightReader):
    """
    液体高度检测算法
    """

    _index = 0
    _description = "液体高度识别.主模型.版本v1"
    _cn_name = "液体高度识别算法"

    def __init__(self, _inputs: AlgorithmInput, _id: int = -1):
        super(LiquidHeightReaderSub0, self).__init__(_inputs, _id)

    def _preprocess(self) -> Any:
        pass

    def _postprocess(self) -> Any:
        pass

    def _do_detect(self) -> (bool, Any, tuple):
        return super(LiquidHeightReaderSub0, self)._do_detect()

    def get_circle_roi(self, img, center_coordinates, radius):
        # 黑色的背景图像
        circle = np.zeros(img.shape, dtype="uint8")

        # 背景黑色圆形白色mask
        circle_mask = cv2.circle(circle, center_coordinates, radius, (255, 255, 255), -1)  #图黑圆白

        # 获取圆形ROI原始图像
        black_img = cv2.bitwise_and(img, circle_mask)

        x = center_coordinates[0] - radius
        y = center_coordinates[1] - radius
        w = radius * 2
        h = radius * 2

        # 获取圆外接矩形区域
        roi_img = black_img[y:y + h, x:x + w]
        return roi_img, (x, y)

    def get_segment(self, roi_img, binary_thresh):
        gray_roi_img = cv2.cvtColor(roi_img, cv2.COLOR_BGR2GRAY)
        # 阈值分割
        _, thresh_res = cv2.threshold(gray_roi_img, binary_thresh, 255, cv2.THRESH_BINARY)

        # 开操作去除小的区域
        kernel = np.ones((3, 3), dtype=np.uint8)
        thresh_res = cv2.morphologyEx(thresh_res, cv2.MORPH_OPEN, kernel)

        contours, _ = cv2.findContours(thresh_res, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_NONE)
        return contours

    def get_contours(self, cnt, roi_coor):
        cnt_list = cnt.tolist()

        res = []
        for i in cnt_list:
            info = i[0]
            info[0] = info[0] + roi_coor[0]
            info[1] = info[1] + roi_coor[1]
            res.append(info)

        res = res[::4]
        return res

    def det_height(self, img, roi_square, binary_thresh, heigh_percent):

        center_coordinates = roi_square["center"]
        radius = roi_square["radius"]
        roi_img, roi_coor = self.get_circle_roi(img, center_coordinates, radius)
        contours = self.get_segment(roi_img, binary_thresh)

        if len(contours) == 0:
            return ['not_full', []]

        # 面积最大的轮廓
        cnts = sorted(contours, key=cv2.contourArea, reverse=True)  # 所有轮廓按面积排序
        # cnt: [[[89 60]] [[80 79]] [[82 59]]]  numpy.ndarray
        cnt = cnts[0]

        # 轮廓的外接矩形
        rect = cv2.boundingRect(cnt)
        x, y, w, h = rect

        roi_h = roi_img.shape[0]

        contours_point = self.get_contours(cnt, roi_coor)

        if h / roi_h >= heigh_percent:
            return ['full', contours_point]
        else:
            return ['not_full', contours_point]
