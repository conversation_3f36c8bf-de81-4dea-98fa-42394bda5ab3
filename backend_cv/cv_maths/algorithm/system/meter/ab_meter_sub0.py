from typing import Any

import cv2
import numpy as np
from skimage import measure, morphology

from backend_common.constants.constant import color_list, LOWER, UPPER
from backend_common.constants.math_constants import DEFAULT_DECIMAL_PLACES
from backend_common.exceptions.inspection_exception import AlgorithmProcessException
from backend_common.maths.algorithm.base.algorithm_base import AlgorithmInput
from backend_common.utils.util import Functions, get_hsv_color_str, get_mask_by_color, get_angle
from cv_maths.algorithm.system.meter import MeterReader


class MeterReaderSub0(MeterReader):
    """
    仪表识别算法
    """

    _index = 0
    _description = "仪表识别.主模型.版本v1"
    _cn_name = "仪表识别算法"

    def __init__(self, _inputs: AlgorithmInput, _id: int = -1):
        super(MeterReaderSub0, self).__init__(_inputs, _id)

    def _preprocess(self) -> Any:  # TODO
        pass

    def _do_detect(self) -> (bool, Any, tuple):
        return super(MeterReaderSub0, self)._do_detect()

    def _postprocess(self) -> Any:
        pass

    def get_angel(self, point1, point2):
        pose1CentLine = [self.circleCent[0] -
                         point1[0], self.circleCent[1] - point1[1]]
        pose2CentLine = [self.circleCent[0] -
                         point2[0], self.circleCent[1] - point2[1]]
        # 圆心到直线两点的向量夹角
        theta = Functions.get_clock_angle(pose1CentLine, pose2CentLine)
        return theta

    def parse_parameter(self, param_dict):
        """
        识别并读取仪表读数

        :param param_dict 参数字典
        :param img 输入图片
        :param img_idx 输入图片 index
        :param meter_idx 仪表 index
        """
        self.resultScale = int(param_dict['resultScale'])
        self.zeroPoint = param_dict['zeroPoint']
        self.startPoint = param_dict['startPoint']
        self.endPoint = param_dict['endPoint']
        self.colorThreshold = int(param_dict['colorThreshold'])
        self.meterRange = param_dict['range']
        roi = param_dict['roiSquare']
        self.circleCent = roi['center']
        self.circleR_ori = roi['radius']
        circleR_clacu = int(1 * ((Functions.distances(self.circleCent, self.zeroPoint) +
                                    Functions.distances(self.circleCent, self.startPoint) +
                                    Functions.distances(self.circleCent, self.endPoint)) / 3))
        # print(circleR, circleR_clacu)
        self.circleR = min(self.circleR_ori, circleR_clacu)
        self.meter_color_rgb = param_dict['color_picker']

        # 计算角度单位值，即每角度的刻度值
        start_angle = get_angle(self.circleCent, self.startPoint)
        zero_angle = get_angle(self.circleCent, self.zeroPoint)
        end_angle = get_angle(self.circleCent, self.endPoint)
        angel = end_angle - zero_angle
        if angel > 0:
            angel = 360 - angel
        else:
            angel = abs(angel)
        #self.divisionValue = (self.meterRange[1]-self.meterRange[0])/angel
        self.divisionValue = self.meterRange[1]/angel
        # print(self.divisionValue)

        # angel = self.get_angel(self.zeroPoint, self.endPoint)
        # self.wrongAngelRange = [self.get_angel(self.zeroPoint, self.startPoint), self.get_angel(self.zeroPoint, self.endPoint)]
        # if self.endPoint[0] > self.zeroPoint[0]:
        #     angel = 360-angel
        # self.divisionValue = (self.meterRange[1]-0)/angel
        # print(self.divisionValue)

    def read_meter(self, param_dict, ori_image, img_idx, meter_idx):
        # 解析参数
        self.parse_parameter(param_dict)
        
        # 截取ROI, 仪表盘切分
        small_roi_img = self.imgCutCircle(ori_image)
        # small_roi_img = self.find_roi_rectangle(ori_image)
        # cv2.imwrite('/opt/tjh/model_server/depl_img/output/roi_img.jpg', small_roi_img)
        # 根据指针颜色mask
        #color_mask = self.edgeFilter(roi_img)
        color_mask = get_mask_by_color(small_roi_img, self.meter_color_rgb, self.colorThreshold, color_picker=True)
        # cv2.imwrite('/opt/tjh/model_server/depl_img/output/mask.jpg', color_mask)
        # 寻找最大连通域并返回质心坐标
        largestConnectComponent, res, slope, point, mean_x_y, skeleton, points_set = self.largestConnectComponentAndPoint(color_mask)
        if not res:
            print("抱歉，没有识别到指针，请再次尝试")
            raise AlgorithmProcessException("抱歉，没有识别到指针，请再次尝试")
        #else:
        #    res = self.normlDetect(centroid_point)
        # cv2.imwrite('/opt/tjh/model_server/depl_img/output/ConnectComponent.jpg', largestConnectComponent)
        # cv2.circle(skeleton*255, point, 5, (0,0,255), -1)
        # cv2.imwrite('/opt/tjh/model_server/depl_img/output/skeleton.jpg', skeleton*255)
        #slope, point = self.lineFit(largestConnectComponent, ori_image)
        # print(point)
        # for p in points_set:
        #     cv2.circle(small_roi_img, p, 2, 100, -1)
        # cv2.circle(small_roi_img, point, 2, 255, -1)
        # cv2.imwrite('/opt/tjh/model_server/depl_img/output/small_result.jpg', small_roi_img)
        if len(points_set) > 0:
            point = mean_x_y
        point = self.extend_point(small_roi_img, slope, point)
        point[0] += self.coor[0]
        point[1] += self.coor[1]
        # pointer_zero_angel = self.get_angel(self.zeroPoint, point)

        # 注释如下3行 修复 指针翻转了 180 度问题
        # if self.wrongAngelRange[0] < pointer_zero_angel < self.wrongAngelRange[1]:
        #     point = [2 * self.circleCent[0]-point[0], 2 * self.circleCent[1]-point[1]]
        #     pointer_zero_angel = self.get_angel(self.zeroPoint, point)

        #pointer_zero_angel = self.getAngel(self.zeroPoint, centroid)
        # if point[0] > self.zeroPoint[0]:
        #     pointer_zero_angel = 360-pointer_zero_angel
        #     pointerRes = self.divisionValue*pointer_zero_angel
        # else:
        #     pointerRes = -self.divisionValue*pointer_zero_angel
        # print(pointer_zero_angel, pointerRes)

        pointer_angel = get_angle(self.circleCent, point)
        # start_angle = get_angle(self.circleCent, self.startPoint)
        zero_angle = get_angle(self.circleCent, self.zeroPoint)
        pointer_zero_angel = pointer_angel - zero_angle
        if pointer_zero_angel > 0:
            pointer_zero_angel = 360 - pointer_zero_angel
        else:
            pointer_zero_angel = abs(pointer_zero_angel)
        pointerRes = self.divisionValue*pointer_zero_angel
        # cv2.line(ori_image, self.circleCent, point, (0, 0, 255), 2)
        # cv2.imwrite('/opt/tjh/model_server/depl_img/output/result.jpg', ori_image)
        self._processFarPose[img_idx][meter_idx] = point
        if self.meterRange[0] <= pointerRes <= self.meterRange[1]:
            if self.resultScale == 0:
                return int(pointerRes)
            return round(pointerRes, self.resultScale)
        else:
            print("抱歉，识别到的指针不在量程范围，请再次尝试")
            raise AlgorithmProcessException("抱歉，识别到的指针不在量程范围，请再次尝试")

    def find_roi_rectangle(self, img):
        # 图黑圆白
        circle = np.zeros(img.shape[:2], dtype="uint8")
        cv2.circle(circle, self.circleCent, self.circleR, 255, -1)
        contours, _ = cv2.findContours(circle,  cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE)
        x, y, w, h = cv2.boundingRect(contours[0])

        # 扩大外接矩形面积, wh设置为xy的两倍，注意如果ROI在图像的边缘，坐标可能会报错，扩大区域可以小一些
        x = x - 10
        y = y - 10
        w = w + 20
        h = h + 20

        new_img = self.bitwiseOr[y:y + h, x:x + w]
        self.coor = [x, y]
        self.samll_circleCent = [self.circleCent[0]-self.coor[0], self.circleCent[1]-self.coor[1]]
        return new_img

    def imgCutCircle(self, img):  
        # 图白圆黑
        circle = np.ones(img.shape, dtype="uint8")
        circle = circle * 255
        cv2.circle(circle, self.circleCent, self.circleR, 0, -1)
        self.bitwiseOr = cv2.bitwise_or(img, circle)
        

        # 修改处
        circleR_ori_expansion = self.circleR_ori + 5
        x = self.circleCent[0] - circleR_ori_expansion
        y = self.circleCent[1] - circleR_ori_expansion
        w = circleR_ori_expansion * 2
        h = circleR_ori_expansion * 2

        if x < 0:
            x = 0
        if x + w > img.shape[1]:
            w = img.shape[1] - x
        if y < 0:
            y = 0
        if y + h > img.shape[0]:
            h = img.shape[0] - y
        self.img_cut_check(self.bitwiseOr, x + w, y + h)
        self.bitwiseOr = self.bitwiseOr[y:y + h, x:x + w]
        self.coor = [x, y]
        self.samll_circleCent = [self.circleCent[0]-self.coor[0], self.circleCent[1]-self.coor[1]]
        # 修改结束
        return self.bitwiseOr

    def edgeFilter(self, img):
        # 颜色空间mask
        meter_color, point_hsv = get_hsv_color_str(self.meter_color_rgb)
        print('meter color is {}'.format(meter_color))
        if meter_color == 'Red':
            hsv_index_list = [0, 1]
        else:
            hsv_index_list = [color_list.index(meter_color)]
        img_hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)
        image_add = []
        print(hsv_index_list)
        for index in hsv_index_list:
            if LOWER[index][0] < point_hsv[0] < UPPER[index][0]:
                low = np.where((point_hsv-self.colorThreshold) > LOWER[index], point_hsv-self.colorThreshold, LOWER[index])
                up = np.where((point_hsv+self.colorThreshold) < UPPER[index], point_hsv+self.colorThreshold, UPPER[index])
            else:
                low, up = LOWER[index], UPPER[index]
            print(point_hsv, LOWER[index], UPPER[index])
            print(low, up)
            mask_image = cv2.inRange(img_hsv, low, up)
        #cv2.imwrite(os.path.join(self.out_dir, 'mask.jpg'), mask)
        #reval_t, thre = cv2.threshold(mask, 0, 255, cv2.THRESH_BINARY | cv2.THRESH_OTSU)
        #cv2.imwrite('thre.jpg', thre)
        #kernel = np.ones((5, 1), np.uint8)
        #self.edge = cv2.erode(mask, kernel, iterations=1)
            #mask_image = cv2.morphologyEx(mask_image, cv2.MORPH_CLOSE,kernel=np.ones((5,5),np.uint8))
            image_add.append(mask_image)
            #cv2.imwrite(os.path.join(self.out_dir, 'mask_{}.jpg'.format(index)), mask_image)
            if index == 1:
                mask_image = cv2.add(image_add[0], image_add[1])
        #mask_image = cv2.morphologyEx(mask_image,cv2.MORPH_CLOSE,kernel=np.ones((5,5),np.uint8))
        #cv2.imwrite(os.path.join(self.out_dir, 'mask_pre.jpg'), mask_image)
        #mask_image = cv2.dilate(mask_image, kernel=np.ones((2,2), np.uint8), iterations=1)
        #cv2.imwrite(os.path.join(self.out_dir, 'mask.jpg'), mask_image)
        return mask_image

    def largestConnectComponentAndPoint(self, mask_edge):
        def get_min_rect(gray):
            # 找到轮廓
            contours, hierarchy = cv2.findContours(gray, cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE)
            # 计算旋转矩形
            rect = cv2.minAreaRect(contours[0])
            # 获取旋转矩形参数
            center, size, angle = rect
            # 转换为int类型
            center = tuple(map(int, center))
            size = tuple(map(int, size))
            # 画出旋转矩形
            rect = cv2.boxPoints(rect)
            rect = np.int0(rect)
            return rect
        labeled_img, num = measure.label(mask_edge, background=0, return_num=True)
        if num == 0:
            return [], 0, 0, 0, 0, 0, 0
        #visual_image= color.label2rgb(labeled_img)
        #cv2.imwrite(os.path.join(self.out_dir, 'ConnectComponent.jpg'), visual_image)
        props = measure.regionprops(labeled_img)

        numPix = []
        for ia in range(len(props)):
            numPix += [props[ia].area]
        
        #像素最多的连通区域及其指引
        # maxnum = max(numPix)
        # index = numPix.index(maxnum)

        areas = []
        out = []
        index = np.argsort(numPix)[::-1]
        # print(index)
        for i in index:
            largestConnectComponent = mask_edge.copy()
            largestConnectComponent[labeled_img!=props[i].label] = 0
            # cv2.imwrite('/opt/tjh/model_server/depl_img/output/ConnectComponent_{}.jpg'.format(i), largestConnectComponent)
            slope, point, skeleton, mean_x_y, points_set = self.lineFit(largestConnectComponent)
            k = slope[1] / slope[0]
            b = point[1] - k * point[0]
            dist = abs(k * self.samll_circleCent[0] - self.samll_circleCent[1] + b) / ((k**2 + 1)**0.5)
            # print(dist, self.circleR)

            rect = get_min_rect(largestConnectComponent)
            # print(rect)
            w = Functions.distances(rect[0], rect[1])
            h = Functions.distances(rect[1], rect[2])
            if min(w,h)==0:
                break
            ratio = max(w,h) / min(w,h)
            # print(w,h,ratio)
            if dist < self.circleR*0.2 and ratio > 2:
                # bbox = (props[i].bbox)
                # boxes.append(bbox)
                # print(numPix[i])
                areas.append(numPix[i])
                out.append([largestConnectComponent, 1, slope, point, mean_x_y, skeleton, points_set])
        #         cv2.drawContours(mask_edge_, [rect], 0, (0, 0, 255), 1)
        # cv2.imwrite('/opt/tjh/model_server/cv_device_img/output/mask_rectangle.jpg', mask_edge_)
        if len(out) == 0:
            return [], 0, 0, 0, 0, 0, 0
        else:
            index = areas.index(max(areas))
            return out[index]
            
        # largestConnectComponent = mask_edge.copy()
        # largestConnectComponent[labeled_img!=props[index[0]].label] = 0

        # minRect = cv2.minAreaRect(props[index[0]].coords)
        # ##最小矩形框的四个点坐标（右上，左上，左下，右下）
        # box = np.int0(cv2.boxPoints(minRect))
        # box[:,[0,1]] = box[:,[1,0]]
        # max_dst = max(self.Disttances(box[0], box[1]), self.Disttances(box[1], box[2]))
        # if max_dst < 0.6*self.circleR:
        #     largestConnectComponent1 = mask_edge.copy()
        #     largestConnectComponent1[labeled_img!=props[index[1]].label] = 0
        #     largestConnectComponent = np.logical_or(largestConnectComponent, largestConnectComponent1)
        #     largestConnectComponent = np.where(largestConnectComponent==True, 255, 0)

        # point = props[index].centroid
        # centroid_point = [int(point[1]), int(point[0])]
        #return centroid_point, 1
        # return largestConnectComponent, 1
    
    def extend_point(self, ori_image, slope, point):
        (vx, vy), (x, y) = slope, point
        blank = np.zeros(ori_image.shape[0:3])
        rows , cols = ori_image.shape[:2]
        if vy==1:
            line_img = cv2.line(blank.copy(), (x, 1) , (x, cols) , (255, 255, 255), 2)
        else:
            lefty = int((-x*vy/vx) + y) 
            righty = int(((cols - x) * vy/vx) + y )
            line_img = cv2.line(blank.copy(), (cols -1 ,righty) , (0,lefty) , (255, 255, 255), 2)
        cicle_img = cv2.circle(blank.copy(), self.samll_circleCent, self.circleR_ori, (255, 255, 255), 1)
        intersection_img = cv2.addWeighted(line_img, 0.5, cicle_img, 0.5, 0)
        # cv2.circle(intersection_img, self.circleCent, 2, (255, 255, 255), 1)
        # cv2.imwrite('/opt/tjh/model_server/depl_img/output/intersection_img.jpg', intersection_img)
        intersection = np.logical_and(line_img, cicle_img)
        # 获得交点位置
        points = np.argwhere(intersection==True)
        point1 = points[0]
        dist1 = Functions.distances(point, (point1[1], point1[0]))
        point2 = points[-1]
        dist2 = Functions.distances(point, (point2[1], point2[0]))
        if dist1 < dist2:
            return [int(point1[1]), int(point1[0])]
        else:
            return [int(point2[1]), int(point2[0])]
        

    def lineFit(self, largestConnectComponent):
        # dst = cv2.distanceTransform(largestConnectComponent, cv2.DIST_L2, 3).astype(np.uint8)
        # dst = np.array(dst/np.max(dst)*255, dtype="uint8")
        # cv2.imwrite(os.path.join(self.out_dir, 'dst.jpg'), dst*255)
        largestConnectComponent_ = largestConnectComponent.copy()
        largestConnectComponent_[largestConnectComponent_==255]=1
        skeleton = morphology.skeletonize(largestConnectComponent_)
        skeleton = np.where(skeleton==True, 1, 0).astype('uint8')

        contours_ = np.argwhere(skeleton==1)
        contours_[:,[0,1]] = contours_[:,[1,0]]

        # contours , hierarchy = cv2.findContours(skeleton, cv2.RETR_LIST, cv2.CHAIN_APPROX_SIMPLE)
        # a = contours[3]      
        # cv2.drawContours(ori_image, contours[3], -1, (0,0,255), 3)
        # cv2.imwrite(os.path.join(self.out_dir, 'contours.jpg'), ori_image)

        [vx , vy , x, y] = cv2.fitLine(contours_ , cv2.DIST_L1, 0,0.01 , 0.01)
        # rows , cols = skeleton.shape[:2]
        # lefty = int((-x*vy/vx) + y) 
        # righty = int(((cols - x) * vy/vx) + y )
        # cv2.line(ori_image, (cols -1 ,righty) , (0,lefty) , (0,255,0) , 2)
        # cv2.imwrite(os.path.join(self.out_dir, 'line.jpg'), ori_image)
        # x, y = x.astype('int32'), y.astype('int32')
        line_points = []
        for i in range(-500, 500):
            px = int(x + i * vx)
            py = int(y + i * vy)
            line_points.append([px, py])
        points_set = []
        for point in contours_.tolist():
            result = cv2.pointPolygonTest(np.array(line_points), (point[0], point[1]), False)
            if result==0:
                points_set.append(point)
        
        if len(points_set) == 0:
            mean_x, mean_y = 0, 0
        else:
            # 计算点集的中心
            mean_x, mean_y = np.mean(np.array(points_set), axis=0)
        # print(points_set, mean_x, mean_y)

        return (vx , vy), (int(x), int(y)), skeleton, [int(mean_x), int(mean_y)],points_set
        
    # def normlDetect(self, centroid_point):
    #     # 上限与圆心的向量
    #     self.topVector = [self.circleCent[0] -
    #                       self.top[0], self.circleCent[1] - self.top[1]]
    #     # 下限与圆心的向量
    #     self.bottleVector = [self.circleCent[0] -
    #                          self.bottle[0], self.circleCent[1] - self.bottle[1]]
    #     # 指针(最远点)与圆心的向量
    #     self.farVector = [self.circleCent[0] - centroid_point[0],
    #                       self.circleCent[1] - centroid_point[1]]
    #     self.farTopTheta = Functions.GetClockAngle(
    #         self.topVector, self.farVector)
    #     self.topBottleTheta = Functions.GetClockAngle(
    #         self.topVector, self.bottleVector)
    #     if self.farTopTheta < self.topBottleTheta:
    #         return 'normal'
    #     else:
    #         return 'unnormal'
