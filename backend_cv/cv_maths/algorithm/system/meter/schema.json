{"algorithm": {"name": "指针式仪表识别算法", "code": "meter", "des": "指针式仪表识别算法.V1", "type": "openCV", "alarmSupport": true, "isOverrideAlarm": true, "batchSupport": true, "resultShowType": "RESULT"}, "images": ["e:/images/test.jpg"], "roi": {"name": "ROI区域", "type": "polygon", "des": "支持绘制的ROI区域类型, polygon:多边形, circle:圆, ellipse:椭圆", "value": [[325, 116], [331, 115], [346, 310], [337, 310]], "nullable": true}, "targetSceneImage": {"in_use": false, "uri": "", "name": "场景基准图"}, "inputParam": {"meterList": {"name": "仪表盘列表", "type": "array", "value": [], "val_range": [{"roiSquare": {"name": "圆心坐标及半径", "type": "circle", "value": {"center": [100, 200], "radius": 20}, "showable": true, "desc": "圆心坐标及半径"}, "color_picker": {"name": "指针颜色", "type": "color_picker", "value": [0, 255, 0], "des": "从图像中提取到的颜色值(RGB)"}, "colorThreshold": {"name": "颜色偏差", "type": "number", "des": "颜色偏差,值越大颜色区间越大,可根据实际情况酌情调节", "value": 55, "val_range": [0, 255, 1, 55], "nullable": false}, "zeroPoint": {"name": "零点位置", "type": "point", "value": [123, 456], "showable": true, "desc": "一个point对象，点的坐标"}, "startPoint": {"name": "起点位置", "type": "point", "value": [123, 456], "showable": true, "desc": "一个point对象，仪表刻度起始点的坐标"}, "endPoint": {"name": "终点位置", "type": "point", "value": [123, 456], "showable": true, "desc": "一个point对象，仪表刻度结束点的坐标"}, "range": {"name": "量程", "type": "range", "value": [-30, 50], "desc": "取值范围", "val_range": [-30.0, 50.0, 0.1]}, "resultDes": {"name": "结果描述", "type": "string", "desc": "结果展示的描述文字"}, "resultScale": {"name": "结果精度", "type": "number", "desc": "结果保留精度位数", "val_range": [0, 5, 1, 0]}, "resultUnit": {"name": "结果单位", "type": "string", "desc": "结果单位"}}], "desc": "支持多个仪表盘作为输入"}}, "outputDefine": {"type": "analogue", "desc": "仪表读数-模拟量", "ruleInfo": [{"desc": "仪表读数越高高限", "isBind": false, "type": "HH", "val": null, "placeHolder": "大于等于时报警"}, {"desc": "仪表读数越高限", "isBind": false, "type": "H", "val": null, "placeHolder": "大于等于时报警"}, {"desc": "仪表读数越低限", "isBind": false, "type": "L", "val": null, "placeHolder": "小于等于时报警"}, {"desc": "仪表读数越低低限", "isBind": false, "type": "LL", "val": null, "placeHolder": "小于等于时报警"}]}}