{"algorithmMetadata": {"name": "指针式仪表", "code": "meter", "version": "1.0.0", "description": "指针式仪表识别算法.V1", "type": "openCV", "isBatchProcessing": false, "roiDrawType": "SQUARE", "roiRequired": false, "classification": "DEVICE_METER"}, "input": [{"key": "roiSquare", "label": "仪表区域", "dataType": "CIRCLE", "constraints": {"required": true}, "drawToOsd": true}, {"key": "colorPicker", "label": "指针颜色", "dataType": "RGB", "defaultValue": [0, 0, 0], "constraints": {"required": true, "minLength": 3, "maxlength": 3}, "drawToOsd": false}, {"key": "colorThreshold", "label": "颜色偏差", "dataType": "INTEGER", "defaultValue": 55, "constraints": {"min": 0, "max": 255, "precision": 1, "required": true}, "drawToOsd": false}, {"key": "zeroPoint", "label": "零点位置", "dataType": "POINT", "constraints": {"required": true}, "drawToOsd": true}, {"key": "startPoint", "label": "起点位置", "dataType": "POINT", "constraints": {"required": true}, "drawToOsd": true}, {"key": "endPoint", "label": "终点位置", "dataType": "POINT", "constraints": {"required": true}, "drawToOsd": true}, {"key": "range", "label": "量程", "dataType": "RANGE", "defaultValue": {"start": -30, "end": 50}, "constraints": {"required": true, "max": "999999", "min": "-999999", "precision": 0.001}, "drawToOsd": false}, {"key": "resultPrecision", "label": "结果精度", "dataType": "INTEGER", "defaultValue": 2, "constraints": {"min": 0, "max": 5, "precision": 1, "required": false}, "drawToOsd": false}, {"key": "unit", "label": "单位", "dataType": "STRING", "constraints": {"maxLength": 255, "regexPattern": "^[A-Za-z0-9_]+$", "required": false}, "drawToOsd": true}, {"key": "resultDes", "label": "结果描述", "dataType": "STRING", "constraints": {"required": false}, "drawToOsd": true}], "output": [{"key": "meterReading", "label": "仪表读数", "dataType": "FLOAT"}]}