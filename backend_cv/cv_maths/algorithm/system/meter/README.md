## 指针式仪表识别算法V1.0.0

### 描述

 该算法用于指针式仪表读数识别，支持工厂以及实验室大多数仪表识别，指针颜色参数建议选取图片中指针内大多数的颜色，颜色偏差不建议调整。
 - 是否配置ROI：否
- 是否配置多图输入：否
- 算法一 基于颜色检测的仪表识别主策略
- 算法二 基于轮廓检测的仪表识别主策略

### 输入参数

| 参数名     | 是否绘制 | 参数类型    | 默认值 | 是否必填 | 数据范围   | 精度   |描述
|-------|--------------|----------------------|--------------|----------------------|--------------|----------------------|----------------------|
| 仪表区域 |是| CIRCLE |-|是|-|-| 圆，仪表包含指针在内的识别区域 |
| 指针颜色 | 否|RGB | [0, 0, 0] |是|0-255|1|取色笔，指针的颜色|
| 颜色偏差 |否|INTEGER | 55|是|0-255|1|数值，指的是指针颜色可变区域 |
| 零点位置|是| POINT |-|是|-|-| 坐标点，仪表0坐标点位置 |
| 起点位置|是| POINT |-|是|-|-| 坐标点，仪表起点位置 |
| 终点位置|是| POINT |-|是|-|-| 坐标点，仪表终点位置 |
| 量程 |否| RANGE | -|是|-999999-999999|0.001|仪表的量程 |
| 结果精度 |否| INTEGER|2|否|0-5|1| 结果保留小数点后几位|
| 单位 |是| STRING|-|否|-|-| 结果识别单位，如ml|
| 结果描述 |是| STRING|-|否|-|-| 可选，结果描述文字，例如主油箱指针读数|

### 输出参数

| 参数名         | 参数类型   | 描述               |
| ----------- | ------ | ---------------- |
|meterReading|FLOAT|指针读数数值 |
|resultPrecision|INTEGER|结果保留小数点后几位|
|unit|STRING|结果识别单位，如MPa|
|resultDes|STRING|结果描述文字，例如主油箱温度读数|

## 结果展示

| 名称     | 描述                  |
| ------ | ------------------- |
| 指针位置 | 用直线（有箭头）代表识别出的指针 |
| 指针读数 | 数值，指针识别到的数值 |
|仪表区域外接矩形|矩形框|