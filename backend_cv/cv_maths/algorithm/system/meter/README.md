## 指针式仪表识别算法 
## 版本:

 v 1.0.0

## 描述:

 该算法用于指针式仪表读数识别，支持工厂以及实验室大多数仪表识别。使用建议:

1) 指针颜色参数建议选取图片中指针内大多数的颜色。

2) 颜色偏差不建议调整。

## 输入:

| 名称 | 类型 | 描述 |
|-------|--------------|----------------------|
| 圆心坐标及半径 | circle | 圆，仪表包含指针在内的识别区域 |
| 指针颜色 | color_picker | 取色笔，指针的颜色 |
| 颜色偏差 | number| 数值，指的是指针颜色可变区域 |
| 零点位置| point | 坐标点，item为int,len=2，仪表0坐标点位置 |
| 起点位置| point | 坐标点，item为int,len=2，仪表起点位置 |
| 终点位置| point | 坐标点，item为int,len=2，仪表终点位置 |
| 量程 | range | 量程，item为number，仪表的量程 |

* 是否支持单图批量识别：是

## 输出：

| 名称 | 类型 | 描述 |
|-------|--------------|----------------------|
| 指针位置 | line | 用直线代表识别出的指针 |
| 指针读数 | number | 数值，指针读数 |