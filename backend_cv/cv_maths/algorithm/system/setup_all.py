#!/usr/bin/env python

"""算法包安装制作脚本 打包全部算法, 可用于环境初始化"""

from setuptools import setup, find_packages

# with open('README.rst') as readme_file:
#     readme = readme_file.read()
block_name = "algorithm_blocks"

requirements = []

test_requirements = ['pytest>=3', ]
setup(
    author="qinghaibo",
    author_email='<EMAIL>',
    python_requires='>=3.6',
    classifiers=[
        'Development Status :: 2 - Pre-Alpha',
        'Intended Audience :: Developers',
        'Natural Language :: English',
        'Programming Language :: Python :: 3',
        'Programming Language :: Python :: 3.6',
        'Programming Language :: Python :: 3.7',
        'Programming Language :: Python :: 3.8',
    ],
    description="算法块打包",
    install_requires=requirements,
    # long_description=open("../../../README.md").read(),
    long_description="copyright © hollysys.com 2023",
    long_description_content_type='text/markdown',
    include_package_data=True,
    keywords=block_name,
    name=block_name,
    packages=find_packages(),  # 默认基于当前路径
    # test_suite='tests',
    package_data={
        'indicator_flash': ['*.py', '*.json'],
        'knob_gear': ['*.py', '*.json'],
        'liquid_level': ['*.py', '*.json'],
        'meter': ['*.py', '*.json'],
        'qrcode': ['*.py', '*.json']
    },
    tests_require=test_requirements,
    url='https://www.hollysys.com',
    version='1.0.0',
    zip_safe=False,
)
