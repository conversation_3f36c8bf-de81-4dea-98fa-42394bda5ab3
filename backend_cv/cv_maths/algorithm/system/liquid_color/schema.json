{"algorithm": {"name": "液体颜色识别", "code": "liquid_color", "des": "液体颜色识别.V1", "type": "openCV", "alarmSupport": true, "isOverrideAlarm": false, "batchSupport": false}, "roi": {"name": "ROI区域", "type": "polygon", "des": "支持绘制的ROI区域类型, polygon:多边形, circle:圆, ellipse:椭圆", "value": [[325, 116], [331, 115], [346, 310], [337, 310]], "nullable": true}, "targetSceneImage": {"in_use": false, "uri": "", "name": "场景基准图"}, "images": ["e:/images/test.jpg"], "inputParam": {"area_ratio_thresh": {"name": "ROI区域中颜色面积占比", "type": "number", "des": "ROI区域中颜色面积占比", "value": 0.4, "val_range": [0, 1, 0.1, 0.4]}, "object_roi": {"name": "液体ROI区域", "type": "square", "value": [[325, 116], [331, 115], [346, 310], [337, 310]], "showable": true, "des": "数组形式，元素是一个point对象，长度为4，分别为左上、左下、右下、右上顶点坐标"}, "area_thresh": {"name": "颜色区域面积过滤", "type": "number", "des": "颜色区域面积过滤值，主要用于过滤小的区域", "value": 100, "val_range": [0, 3000, 10, 100]}, "color_select": {"name": "检测颜色", "type": "select", "des": "需检测的液体颜色", "val_range": {"0": "红色", "2": "绿色", "3": "蓝色", "4": "橙黄色", "5": "青色", "6": "紫色", "7": "黑色", "8": "白色", "9": "灰色"}}}, "outputDefine": {"type": "string", "desc": "液体颜色检测", "val_enum": {"color_detected": 1, "no_color_detected": 0}, "ruleInfo": [{"desc": "液体颜色越高高限", "isBind": false, "type": "HH", "val": 0.0, "placeHolder": "数值;液体颜色识别结果"}, {"desc": "液体颜色越高限", "isBind": true, "type": "H", "val": 0.0, "placeHolder": "数值;液体颜色识别结果"}, {"desc": "液体颜色越低限", "isBind": false, "type": "L", "val": 0.0, "placeHolder": "数值;液体颜色识别结果"}, {"desc": "液体颜色越低低限", "isBind": false, "type": "LL", "val": 0.0, "placeHolder": "数值;液体颜色识别结果"}]}}