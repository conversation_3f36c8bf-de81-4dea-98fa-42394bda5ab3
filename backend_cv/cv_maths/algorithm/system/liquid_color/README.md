## 液体颜色识别算法

## 版本:

 v 1.0.0

## 描述:

 该算法用于识别矩形ROI区域中是否存在指定的颜色

## 输入:

| 名称         | 类型     | 描述                                                  |
| ---------- | ------ | --------------------------------------------------- |
| 液体ROI区域    | square | 矩形ROI区域                                             |
| 检测颜色       | select | 可指定白色、黑色等9种颜色                                       |
| 液体颜色面积占比阈值 | number | 0-1，指定ROI中颜色面积与ROI面积占比阈值，大于等于该阈值，则判定为ROI区域中检测到指定的颜色 |
| 液体轮廓面积     | number | 屏蔽小于该值的轮廓，不显示并不计算面积                                 |

* 是否支持单图批量识别：否

## 输出：

| 名称       | 类型     | 描述                                            |
| -------- | ------ | --------------------------------------------- |
| 液体颜色检测结果 | string | color_detected:检测到颜色，no_color_detected：未检测到颜色 |