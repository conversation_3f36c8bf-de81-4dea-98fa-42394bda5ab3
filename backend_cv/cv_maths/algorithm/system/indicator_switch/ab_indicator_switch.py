
from enum import Enum
from backend_common.exceptions.inspection_exception import AlgorithmProcessException
from backend_common.maths.algorithm.base.algorithm_base import AlgorithmBase, AlgorithmInput
from loguru import logger
import traceback

SUFFIX_START = "sub"


class IndicatorStatus(Enum):
    # 常亮
    On = (1, '亮', 'on', True)
    # 常灭
    Off = (0, '灭', 'off', False)
    # 闪烁 TODO 暂不支持
    switching = (2, '闪烁', 'switching')
    # 未知
    Unknown = (3, '未知', 'unknown')

    @classmethod
    def from_str(cls, txt):
        for k, v in cls.__members__.items():
            if k.casefold() == txt.casefold():
                return v
        else:
            raise ValueError(f"'{cls.__name__}' enum not found for '{txt}'")

    @classmethod
    def value_of(cls, value):
        for k, v in cls.__members__.items():
            if v.value[0] == value:
                return v
        else:
            raise ValueError(f"'{cls.__name__}' enum not found for '{value}'")


class IndicatorSwitchReader(AlgorithmBase):
    """
    指示灯开关识别
    """

    _name = "indicator_switch"  # 此名称同算法文件夹名

    def __init__(self, _inputs: AlgorithmInput, _id: int = -1):
        super(IndicatorSwitchReader, self).__init__(self.__class__._name)
        if _inputs.__eq__(dict()):
            return

        self._local_var._inputs = _inputs
        # 算法实例参数
        self._local_var._settings = None
        self.originImg = self._get_input_images()[0]
        self.roi = self._get_roi()
        self.input_param = self._get_input_param()

    def _do_detect(self):

        from cv_maths.algorithm.system.indicator_switch.ab_indicator_switch_sub0 import IndicatorSwitchReaderSub0
        from cv_maths.algorithm.system.indicator_switch.ab_indicator_switch_sub1 import IndicatorSwitchReaderSub1
        try:
            isSuccess, output, osdInfo = IndicatorSwitchReaderSub0()._do_detect(self.input_param, self.originImg)
            logger.info('Algorithm 1 executed successfully')
        except (AlgorithmProcessException, Exception) as Algorithm1e:
            tb = traceback.extract_tb(Algorithm1e.__traceback__)
            filename, lineno, funcname, text = tb[-1]
            logger.error(f"Error in file {filename}, line {lineno}, in {funcname}: {text}")
            try:
                isSuccess, output, osdInfo = IndicatorSwitchReaderSub1()._do_detect(self.input_param, self.originImg)
                logger.info(f'Algorithm 2 executed successfully')
            except Exception as e:
                tb = traceback.extract_tb(e.__traceback__)
                filename, lineno, funcname, text = tb[-1]
                logger.error(f"Error in file {filename}, line {lineno}, in {funcname}: {text}")
                if isinstance(Algorithm1e, AlgorithmProcessException):
                    raise AlgorithmProcessException(Algorithm1e.__str__())
                else:
                    raise Exception(Algorithm1e.__str__())
        return isSuccess, output, osdInfo
