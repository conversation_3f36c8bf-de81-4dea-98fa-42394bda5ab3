{"algorithmMetadata": {"name": "指示灯开关", "code": "indicator_switch", "version": "1.0.0", "description": "指示灯开关识别.V1", "type": "openCV", "isBatchProcessing": false, "roiDrawType": "SQUARE", "roiRequired": false, "classification": "DEVICE_METER"}, "input": [{"key": "circle", "label": "指示灯截取圆", "dataType": "CIRCLE", "constraints": {"required": true}, "drawToOsd": true}, {"key": "colorPicker", "label": "灯亮颜色", "dataType": "RGB", "defaultValue": [0, 0, 0], "constraints": {"required": true, "minLength": 3, "maxlength": 3}, "drawToOsd": false}, {"key": "colorThreshold", "label": "颜色偏差", "dataType": "INTEGER", "defaultValue": 55, "constraints": {"min": 0, "max": 255, "precision": 1, "required": true}, "drawToOsd": false}, {"key": "threshold", "label": "识别灵敏度", "dataType": "FLOAT", "defaultValue": 0.42, "constraints": {"min": 0, "max": 1, "precision": 0.01, "required": true}, "drawToOsd": false}, {"key": "resultDes", "label": "结果描述", "dataType": "STRING", "constraints": {"required": false}, "drawToOsd": true}], "output": [{"key": "indicatorSwitch", "label": "指示灯结果", "dataType": "STRING"}, {"key": "indicatorSwitchBoolean", "label": "指示灯结果", "dataType": "BOOLEAN"}]}