#!/usr/bin/env python

"""Tests for `LiquidLevelReader` algorithm."""
import importlib
import json
import os

import cv2
import pytest

from backend_common.constants.alarm import ExceedLimitAlarmRule, AlarmRule
from backend_common.constants.constant import AlgorithmClassifyEnum, AlgorithmStrategyEnum, AlgorithmOutputTypeEnum
from backend_common.constants.math_constants import ALGORITHM_CV_USER_DEFINE_PATH, ALGORITHM_CV_SYSTEM_INNER_PATH
from backend_common.exceptions.inspection_exception import InspectionException
from backend_common.maths.algorithm.base.algorithm_base import AlgorithmOutput, AlgorithmBase, AlgorithmSettings, \
    AlgorithmInput
from backend_common.utils.util import to_camel_case

clazz_name_prefix = "LiquidLevelReader"


def do_itself(algorithm_index, classify, strategy, output_type, alarm_rule: AlarmRule = None):
    settings = AlgorithmSettings(classify, strategy, alarm_rules=[alarm_rule])
    basedir = os.path.dirname(os.path.abspath(__file__))
    algorithm_name = "liquid_level"

    inputs = AlgorithmInput({"images": [os.path.join(basedir, "./resources/input-img/liquid/ywj010.jpg")],
                             "color_picker": [132, 105, 113], "range": [-30, 50],
                             # "thresholdUpper": [335, 157], "thresholdLower": [335, 217],
                             "column": [[325, 116], [337, 310], [346, 310], [331, 115]]})

    sup: AlgorithmBase
    sup = load_algorithm_super(algorithm_name, "system_inner", inputs)
    target_instance = sup.determine_algorithm_instance(settings, algorithm_index)

    assert target_instance is not None
    ret, val, points, alarms = target_instance.perform(settings)
    print(f"结果 {ret, val, points, alarms}")

    outputs = AlgorithmOutput(ret, val, points, alarms)
    print(f"outputs {str(outputs)}")

    # 可选操作
    ret_img = target_instance.gen_result_img()
    if ret_img is not None:
        cv2.imwrite(os.getcwd() + "/40_result.jpg", ret_img[0])

    return outputs


def load_algorithm_super(algorithm_name, a_type, inputs: AlgorithmInput):
    # self._name  算法块唯一标识名,通常需与算法块文件夹名称一致，比如 liquid_level
    module_name = f".{algorithm_name}.ab_{algorithm_name}"
    target_clazz_name = f"{to_camel_case(algorithm_name, '_')}Reader"

    base_path = ALGORITHM_CV_SYSTEM_INNER_PATH
    if a_type == "user_define":
        base_path = ALGORITHM_CV_USER_DEFINE_PATH

    try:
        M = importlib.import_module(module_name, base_path)
        target_clazz_ = getattr(M, target_clazz_name)
    except ModuleNotFoundError:
        raise InspectionException(
            f"Auto-load module '{module_name}' from '{base_path}' failure, please check it manually")

    algorithm_instance: AlgorithmBase = target_clazz_(inputs, -1)
    return algorithm_instance


@pytest.fixture
def primary_detect():
    algorithm_index = 0
    classify = AlgorithmClassifyEnum.Primary
    strategy = AlgorithmStrategyEnum.Main
    output_type = AlgorithmOutputTypeEnum.AlarmLevel

    # 报警条件（可选）
    alarm_rule = ExceedLimitAlarmRule(47, 17, -1, -15)
    return do_itself(algorithm_index, classify, strategy, output_type, alarm_rule=alarm_rule)


@pytest.fixture
def second_detect():
    algorithm_index = 1
    classify = AlgorithmClassifyEnum.Secondary
    strategy = AlgorithmStrategyEnum.Main
    output_type = AlgorithmOutputTypeEnum.AlarmLevel

    # 报警条件（可选）
    alarm_rule = ExceedLimitAlarmRule(47, 17, -1, -15)

    return do_itself(algorithm_index, classify, strategy, output_type, alarm_rule=alarm_rule)


@pytest.fixture
def algorithm_detail():
    def func(o):
        return o.__dict__

    algorithm_name = "liquid_level"
    # algorithm_name = "indicator_flash"
    sup: AlgorithmBase
    sup = load_algorithm_super(algorithm_name, "system_inner", AlgorithmInput())

    algorithm_detail = sup.get_algorithm_detail("system_inner")
    strs = json.dumps(algorithm_detail, default=func, sort_keys=True, indent=2, ensure_ascii=False)
    print(strs)
    return strs


def test_primary(primary_detect):
    assert primary_detect.ret is True


def test_second(second_detect):
    assert second_detect.alarms[0][0] is not None


def test_get_detail(algorithm_detail):
    assert algorithm_detail is not None
