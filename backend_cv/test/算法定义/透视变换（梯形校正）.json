{"algorithmId": "", "name": "透视变换（梯形校正）", "code": "CODE_TSBH", "des": "透视变换（梯形校正）", "url": "http://localhost:5000/identify/tsbh", "roiSquare": "[{\"x\":31,\"y\":45},{\"x\":200,\"y\":45},{\"x\":200,\"y\":84},{\"x\":31,\"y\":84}]", "imgPath": "[\"'D:\\Pythoncode\\learningcv\\box\\images\\test_.jpg\"]", "isCompareTemp": true, "inputParam": {"leftUpPoint": {"name": "左上", "type": "point", "value": "{'x':1444,'y':357}", "des": "左上点坐标"}, "rightUpPoint": {"name": "右上", "type": "point", "value": "{'x':1444,'y':357}", "des": "右上点坐标"}, "leftDownPoint": {"name": "左下", "type": "point", "value": "{'x':1444,'y':357}", "des": "左下点坐标"}, "rightDownPoint": {"name": "右下", "type": "point", "value": "{'x':1444,'y':357}", "des": "右下点坐标"}, "ratio": {"name": "伸缩比例", "type": "number", "value": "1.0", "des": "结果图形宽度伸缩比例"}}, "outputParam": {"orcCache": {"name": "结果图片路径", "type": "string", "value": "9263", "des": "结果图片路径"}}}