{"name": "旋钮档位识别", "code": "CODE_10091", "url": "http://localhost:5000/identify/button", "roiSquare": "[{\"x\":540,\"y\":195},{\"x\":1361,\"y\":195},{\"x\":1361,\"y\":1062},{\"x\":540,\"y\":1062}]", "imgPath": "[\"'D:\\Pythoncode\\learningcv\\box\\images\\test_.jpg\"]", "des": "旋钮档位识别", "isCompareTemp": true, "inputParam": {"gear1roiSquare": {"name": "圆心坐标及半径1", "type": "circle", "value": "[947,790,44]", "des": "圆心坐标及半径"}, "gear1firstGearUp": {"name": "一档上限1", "type": "point", "value": "{'x':914,'y':776}", "des": "一个point对象，点的坐标"}, "gear1firstGearDown": {"name": "一档下限1", "type": "point", "value": "{'x':941,'y':755}", "des": "一个point对象，点的坐标"}, "gear1secondGearUp": {"name": "二档上限1", "type": "point", "value": "{'x':980,'y':764}", "des": "一个point对象，点的坐标"}, "gear1secondGearDown": {"name": "二档下限1", "type": "point", "value": "{'x':988,'y':783}", "des": "一个point对象，点的坐标"}, "gear1gearRange": {"name": "档位信息1", "type": "number", "value": "2", "des": "数字类型"}, "gear2roiSquare": {"name": "圆心坐标及半径2", "type": "circle", "value": "[947,790,44]", "des": "圆心坐标及半径"}, "gear2firstGearUp": {"name": "一档上限2", "type": "point", "value": "{'x':914,'y':776}", "des": "一个point对象，点的坐标"}, "gear2firstGearDown": {"name": "一档下限2", "type": "point", "value": "{'x':941,'y':755}", "des": "一个point对象，点的坐标"}, "gear2secondGearUp": {"name": "二档上限2", "type": "point", "value": "{'x':980,'y':764}", "des": "一个point对象，点的坐标"}, "gear2secondGearDown": {"name": "二档下限2", "type": "point", "value": "{'x':988,'y':783}", "des": "一个point对象，点的坐标"}, "gear2gearRange2": {"name": "档位信息", "type": "number", "value": "2", "des": "数字类型"}}, "constantList": [{"algorithmParamKey": "buttonCache", "name": "buttonCache", "des": "旋钮档位识别", "time": "2022-12-21 16:59:03"}], "outputParam": {"buttonCache": {"name": "字符串", "type": "string", "value": "9263", "des": "旋钮档位识别"}, "audio": {"name": "字符串", "type": "audio", "value": "hello world", "des": "将发送kafka，语音播报"}}}