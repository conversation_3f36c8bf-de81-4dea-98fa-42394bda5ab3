import json
import os.path

import cv2
import numpy as np
import pytest

from backend_common.utils.resolution_ration_util import NormalizeImage


@pytest.fixture
def do_log_transform():
    """
    使用对数变换 提升图像亮度
    """
    # 读取图像
    img = cv2.imread(
        os.path.join(os.path.dirname(os.path.abspath(__file__)), "resources/input-img/liquid/ywj014.jpg")).astype(
        np.float32)

    # # 转换为灰度图像
    # gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

    # 对数变换
    c = 1
    log_transformed = c * cv2.log(1 + img)
    # 将像素值缩放至0-255之间
    out = cv2.normalize(log_transformed, None, 0, 255, cv2.NORM_MINMAX, dtype=cv2.CV_8U)

    # # 提升图像亮度
    alpha = 5.0
    beta = 3
    # mean_val = np.mean(out)
    # if mean_val > 127:
    #     alpha = (255 - beta) / (255 - mean_val)
    # else:
    #     alpha = beta / mean_val

    result = cv2.convertScaleAbs(img, alpha=alpha, beta=beta)

    # # 将图像从float类型转换为uint8类型
    # result = np.uint8(result)

    # 显示原图和处理后的图像
    cv2.imshow('Original Image', img)
    cv2.imshow('Log Transformation', out)
    cv2.imshow('ScaleAbs Transformation', result)

    cv2.waitKey(0)
    cv2.destroyAllWindows()

    return out


def test_log_rotation(do_log_transform):
    assert do_log_transform is not None


def test_dnn_forward1():
    """
    检测模型 测试 :: caffe 格式
    """
    image_name = 'resources/input-img/birds.jpg'
    prototxt_path = 'resources/models/MobileNetSSD_deploy.prototxt'
    model_path = 'resources/models/MobileNetSSD_deploy.caffemodel'
    confidence_ta = 0.2
    # 初始化MobileNet SSD训练的类标签列表
    # 检测，然后为每个类生成一组边界框颜色
    CLASSES = ["background", "aeroplane", "bicycle", "bird", "boat",
               "bottle", "bus", "car", "cat", "chair", "cow", "diningtable",
               "dog", "horse", "motorbike", "person", "pottedplant", "sheep",
               "sofa", "train", "tvmonitor"]
    COLORS = np.random.uniform(0, 255, size=(len(CLASSES), 3))

    # load our serialized model from disk
    print("[INFO] loading model...")
    net = cv2.dnn.readNetFromCaffe(prototxt_path, model_path)
    # 加载输入图像并为图像构造一个输入blob
    # 将大小调整为固定的300x300像素。
    # （注意：SSD模型的输入是300x300像素）
    image = cv2.imread(image_name)
    (h, w) = image.shape[:2]
    blob = cv2.dnn.blobFromImage(cv2.resize(image, (300, 300)), 0.007843,
                                 (300, 300), 127.5)
    # 通过网络传递blob并获得检测结果和
    # 预测
    print("[INFO] computing object detections...")
    net.setInput(blob)
    detections = net.forward()

    # 循环检测结果
    for i in np.arange(0, detections.shape[2]):
        # 提取与数据相关的置信度（即概率）
        # 预测
        confidence = detections[0, 0, i, 2]
        # 通过确保“置信度”来过滤掉弱检测
        # 大于最小置信度
        if confidence > confidence_ta:
            # 从`detections`中提取类标签的索引，
            # 然后计算物体边界框的 (x, y) 坐标
            idx = int(detections[0, 0, i, 1])
            box = detections[0, 0, i, 3:7] * np.array([w, h, w, h])
            (startX, startY, endX, endY) = box.astype("int")
            # 显示预测
            label = "{}: {:.2f}%".format(CLASSES[idx], confidence * 100)
            print("[INFO] {}".format(label))
            cv2.rectangle(image, (startX, startY), (endX, endY), COLORS[idx], 2)
            y = startY - 15 if startY - 15 > 15 else startY + 15
            cv2.putText(image, label, (startX, y),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.5, COLORS[idx], 2)
    # show the output image
    cv2.imshow("Output", image)
    cv2.imwrite("resources/output-img/output.jpg", image)
    cv2.waitKey(0)


# def preprocess1(img_path):
#     img = cv2.imread(img_path)
#     img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
#     print(img.shape)
#     size = max(img.shape[0], img.shape[1])
#     img1 = np.zeros((size, size, 3))
#     img1[:img.shape[0], :size, :] = img
#     img = img1
#
#     img = cv2.resize(img, (128, 128))
#     # cv2.imwrite('asfd.jpg', img)
#     img = img / 255
#
#     mean = np.array([0.485, 0.456, 0.406]).reshape((3, 1, 1)).astype(np.float32)
#     std = np.array([0.229, 0.224, 0.225]).reshape((3, 1, 1)).astype(np.float32)
#     # hwc -> chw
#     img = np.transpose(img, [2, 0, 1])
#     img = (img - mean) / std
#     print(img.shape)
#     img = img.astype(np.float32)
#     # img = torch.from_numpy(img).unsqueeze(dim=0)
#     return img


def test_dnn_forward2():
    """
    分割模型 测试 :: onnx 格式
    """
    image_name = 'resources/input-img/birds.jpg'
    # onnxsim unet.onnx unet.fixed.b1.onnx --input-shape "1,3,128,128"
    onnx_path = 'resources/models/unet.fixed.b1.onnx'  # so big a model
    # 初始化MobileNet SSD训练的类标签列表
    # 检测，然后为每个类生成一组边界框颜色
    CLASSES = ["background", "aeroplane", "bicycle", "bird", "boat",
               "bottle", "bus", "car", "cat", "chair", "cow", "diningtable",
               "dog", "horse", "motorbike", "person", "pottedplant", "sheep",
               "sofa", "train", "tvmonitor"]
    # COLORS = np.random.uniform(0, 255, size=(len(CLASSES), 3))

    # load our serialized model from disk
    print("[INFO] loading model...")
    net = cv2.dnn.readNetFromONNX(onnx_path)
    # 加载输入图像并为图像构造一个输入blob
    image = cv2.imread(image_name)

    # 1. 先将图像 加 padding 转为正方形
    size = max(image.shape[0], image.shape[1])
    img_background = np.zeros((size, size, 3))
    img_background[:image.shape[0], :size, :] = image
    image = img_background
    # # 2. 等比例 resize 到模型需要的尺寸
    image = cv2.resize(image, (128, 128))
    # # 3. 归一化
    image = NormalizeImage()(image)

    # image = np.transpose(preprocess1(image_name), [1, 2, 0])

    (h, w, _) = image.shape
    # blob = cv2.dnn.blobFromImage(cv2.resize(image, (128, 128)), 0.007843, (128, 128), 127.5)
    blob = cv2.dnn.blobFromImage(image)
    # 通过网络传递blob并获得检测结果和
    # 预测
    print("[INFO] computing Image Segmentation...")
    net.setInput(blob)
    output = net.forward()

    numClasses = output.shape[1]
    height = output.shape[2]
    width = output.shape[3]

    # 每种分类不同的颜色
    colors = [np.array([0, 0, 0], np.uint8)]
    for i in range(1, numClasses):
        colors.append((colors[i - 1] + np.random.randint(0, 256, [3], np.uint8)) / 2)

    class_ids = np.argmax(output[0], axis=0)
    segm = np.stack([colors[idx] for idx in class_ids.flatten()])
    segm = segm.reshape((height, width, 3))

    # segm = cv2.resize(segm, (w, h), interpolation=cv2.INTER_NEAREST)

    cv2.imshow("segm", segm)
    cv2.waitKey()

    image = (0.1 * image + 0.9 * segm).astype(np.uint8)

    # Put efficiency information.
    t, _ = net.getPerfProfile()
    label = 'Inference time: %.2f ms' % (t * 1000.0 / cv2.getTickFrequency())
    cv2.putText(image, label, (0, 15), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0))

    cv2.imshow("Output", image)
    cv2.waitKey()
    cv2.destroyAllWindows()


def test_dnn_forward3():
    """
    分类模型 测试 :: onnx 格式
    @see https://microsoft.github.io/onnxjs-demo/#/resnet50
    """
    image_path = 'resources/input-img/coffee.jpg'
    # onnxsim unet.onnx unet.fixed.b1.onnx --input-shape "1,3,128,128"
    onnx_path = 'resources/models/resnet50v2.onnx'  # so big a model
    # 初始化MobileNet SSD训练的类标签列表
    # 检测，然后为每个类生成一组边界框颜色
    with open('resources/models/imagenet-simple-labels.json', encoding='utf-8', errors='ignore') as f:
        labels = json.load(f)
    # COLORS = np.random.uniform(0, 255, size=(len(CLASSES), 3))

    # load our serialized model from disk
    print("[INFO] loading model...")
    net = cv2.dnn.readNetFromONNX(onnx_path)
    # 加载输入图像并为图像构造一个输入blob
    image = cv2.imread(image_path)

    # 1. 先将图像 加 padding 转为正方形
    size = max(image.shape[0], image.shape[1])
    img_background = np.zeros((size, size, 3))
    img_background[:image.shape[0], :size, :] = image
    image = img_background.astype(np.uint8)
    # # 2. 等比例 resize 到模型需要的尺寸
    image = cv2.resize(image, (224, 224))
    # # 3. 归一化
    image = NormalizeImage()(image)

    # image = np.transpose(preprocess1(image_path), [1, 2, 0])

    (h, w, _) = image.shape
    # blob = cv2.dnn.blobFromImage(cv2.resize(image, (128, 128)), 0.007843, (128, 128), 127.5)
    blob = cv2.dnn.blobFromImage(image)
    # 通过网络传递blob并获得检测结果和
    # 预测
    print("[INFO] computing Image Classification ...")
    net.setInput(blob)
    output = net.forward()

    # Get the top-5 predictions and their corresponding labels
    top_5 = sorted(range(len(output[0])), key=lambda j: output[0][j], reverse=True)[:5]
    for i in top_5:
        print(f'{labels[i]}: {output[0][i]:.4f}')


def softmax(x):
    """Compute softmax values for each sets of scores in x."""
    # e_x = np.exp(x)
    e_x = np.exp(x)
    return e_x / e_x.sum()


def test_np():
    x = np.array([-3, 2, -1, 0])
    res = softmax(x)
    print(res)  # [0.0056533  0.83902451 0.04177257 0.11354962]


def test_dnn_forward_light_glue():
    """
    分割模型 测试 :: onnx 格式
    !!!!  some onnx operator not supported !!!!
    """
    # onnxsim unet.onnx unet.fixed.b1.onnx --input-shape "1,3,128,128"
    extrator_path = 'resources/models/superpoint_512_fixed.onnx'  # so big a model
    lightglue_path = 'resources/models/superpoint_512_lightglue_end2end.onnx'  # so big a model

    # load our serialized model from disk
    print("[INFO] loading model...")
    extrator = cv2.dnn.readNetFromONNX(extrator_path)
    lightglue = cv2.dnn.readNetFromONNX(lightglue_path)
    # 加载输入图像并为图像构造一个输入blob
    image0 = cv2.imread('resources/input-img/meter/meter2.jpg')
    image1 = cv2.imread('resources/input-img/meter/test_2.jpg')

    """
    image0, scales0 = load_image("assets/sacre_coeur1.jpg", resize=512)
    image1, scales1 = load_image("assets/sacre_coeur2.jpg", resize=512)
    image0 = rgb_to_grayscale(image0)  # only needed for SuperPoint
    image1 = rgb_to_grayscale(image1)  # only needed for SuperPoint   
    """

    # 1. 先将图像 加 padding 转为正方形
    # size = max(image.shape[0], image.shape[1])
    # img_background = np.zeros((size, size, 3))
    # img_background[:image.shape[0], :size, :] = image
    # image = img_background
    # # 2. 等比例 resize 到模型需要的尺寸
    image0 = cv2.resize(image0, (512, 512))
    image0_gray = cv2.cvtColor(image0, cv2.COLOR_BGR2GRAY)  # only superPoint need

    image1 = cv2.resize(image1, (512, 512))
    image1_gray = cv2.cvtColor(image1, cv2.COLOR_BGR2GRAY)  # only superPoint need
    # # 3. 归一化
    image0_norm = NormalizeImage()(image0_gray)
    image1_norm = NormalizeImage()(image1_gray)

    # (h, w, _) = image.shape
    # ,  scalefactor=1.0, size=(512, 512), mean=(104, 117, 123)
    blob0 = cv2.dnn.blobFromImage(image0_norm)
    blob1 = cv2.dnn.blobFromImage(image1_norm)
    # 通过网络传递blob并获得检测结果和
    # 预测
    print("[INFO] extract image features...")
    extrator.setInput(blob0)
    feat0, scores0, descriptors0 = extrator.forward()

    extrator.setInput(blob1)
    feat1, scores1, descriptors1 = extrator.forward()

    print("[INFO]  light glue image comparing ...")
    lightglue.setInput(feat0, feat1)
    kpts0, kpts1, matches0, matches1, mscores0, mscores1 = lightglue.forward()
    matches = [matches0, matches1]

    points0 = kpts0[matches0[..., 0]]  # coordinates in image #0,
    points1 = kpts1[matches1[..., 0]]  # coordinates in image #1,

    img3 = cv2.drawMatches(image0, kpts0, image1, kpts1, matches[:100], None,
                           flags=cv2.DrawMatchesFlags_NOT_DRAW_SINGLE_POINTS)

    cv2.imshow("target", img3)
    cv2.waitKey()
    cv2.destroyAllWindows()
