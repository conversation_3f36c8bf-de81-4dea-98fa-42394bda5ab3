#!/usr/bin/env python

"""Tests for `IndicatorFlashReader` algorithm."""
import importlib
import os
from typing import List

import cv2
import pytest

# from backend_common.constants.alarm import AlarmRule, ExceedLimitAlarmRule
# from backend_common.constants.constant import AlgorithmClassifyEnum, AlgorithmStrategyEnum, AlgorithmOutputTypeEnum
from backend_common.constants.math_constants import ALGORITHM_CV_USER_DEFINE_PATH, ALGORITHM_CV_SYSTEM_INNER_PATH
from backend_common.exceptions.inspection_exception import InspectionException
from backend_common.maths.algorithm.base.algorithm_base import AlgorithmOutput, AlgorithmBase, AlgorithmInput
from backend_common.utils.util import to_camel_case
from backend_common.utils import MinioUtil

# clazz_name_prefix = "IndicatorFlashReader"


def do_itself():
    # settings = AlgorithmSettings(classify, strategy, alarm_rules=alarm_rules)

    # algorithm_name = "material_blockage"
    # TODO 默认采样频率 500ms + 采样时长 5s, 即 10 张图片


    request_param = {
	"code": "ynhxliquid_level",
	"images": [
		"./test/resources/input-img/ynhxliquid_level/py_20250708175238811_transform.jpg"
	],
	"roi": {
		"dataType": "SQUARE",
		"constraints": {
			"required": False
		}
	},
	"sceneImage": "http://minio:9001/inspection/SCENE/20250708175238811.jpg",
	"userOrSystem": "user_define",
	"inputParam": [
		{
			"algorithmInstanceId": "eeb86f1e8ceed1e76eb3bebc74c09a78",
			"sceneId": "78f85ea85935bc6d188e53706e2117ad",
			"value": [
				{
					"x": 98,
					"y": 38
				},
				{
					"x": 98,
					"y": 849
				},
				{
					"x": 289,
					"y": 849
				},
				{
					"x": 289,
					"y": 38
				}
			],
			"algorithmId": "e566f95a06ebe78c13ecdf440c160cf2",
			"key": "liquidColumn",
			"label": "液柱区域",
			"dataType": "SQUARE",
			"constraints": {
				"required": True
			},
			"drawToOsd": True,
			"sortNo": 0,
			"outOrIn": "IN"
		},
		{
			"algorithmInstanceId": "eeb86f1e8ceed1e76eb3bebc74c09a78",
			"sceneId": "78f85ea85935bc6d188e53706e2117ad",
			"value": [
				189,
				181,
				96
			],
			"algorithmId": "e566f95a06ebe78c13ecdf440c160cf2",
			"key": "colorPicker",
			"label": "液体颜色",
			"dataType": "RGB",
			"defaultValue": [
				0,
				0,
				0
			],
			"constraints": {
				"required": True,
				"minLength": 3
			},
			"drawToOsd": False,
			"sortNo": 1,
			"outOrIn": "IN"
		},
		{
			"algorithmInstanceId": "eeb86f1e8ceed1e76eb3bebc74c09a78",
			"sceneId": "78f85ea85935bc6d188e53706e2117ad",
			"value": 10,
			"algorithmId": "e566f95a06ebe78c13ecdf440c160cf2",
			"key": "colorThreshold",
			"label": "颜色偏差",
			"dataType": "INTEGER",
			"defaultValue": "55",
			"constraints": {
				"required": True,
				"max": 255,
				"min": 0,
				"precision": 1
			},
			"drawToOsd": False,
			"sortNo": 2,
			"outOrIn": "IN"
		},
		{
			"algorithmInstanceId": "eeb86f1e8ceed1e76eb3bebc74c09a78",
			"sceneId": "78f85ea85935bc6d188e53706e2117ad",
			"value": {
				"start": -250,
				"end": 200
			},
			"algorithmId": "e566f95a06ebe78c13ecdf440c160cf2",
			"key": "range",
			"label": "量程",
			"dataType": "RANGE",
			"defaultValue": {
				"start": -30,
				"end": 50
			},
			"constraints": {
				"required": True,
				"max": 999999,
				"min": -999999,
				"precision": 0.001
			},
			"drawToOsd": False,
			"sortNo": 3,
			"outOrIn": "IN"
		},
		{
			"algorithmInstanceId": "eeb86f1e8ceed1e76eb3bebc74c09a78",
			"sceneId": "78f85ea85935bc6d188e53706e2117ad",
			"value": 1,
			"algorithmId": "e566f95a06ebe78c13ecdf440c160cf2",
			"key": "resultPrecision",
			"label": "结果精度",
			"dataType": "INTEGER",
			"defaultValue": "2",
			"constraints": {
				"required": False,
				"max": 5,
				"min": 0,
				"precision": 1
			},
			"drawToOsd": False,
			"sortNo": 4,
			"outOrIn": "IN"
		},
		{
			"algorithmInstanceId": "eeb86f1e8ceed1e76eb3bebc74c09a78",
			"sceneId": "78f85ea85935bc6d188e53706e2117ad",
			"value": "ml",
			"algorithmId": "e566f95a06ebe78c13ecdf440c160cf2",
			"key": "unit",
			"label": "单位",
			"dataType": "STRING",
			"constraints": {
				"required": False,
				"regexPattern": "^[A-Za-z0-9_]+$",
				"maxLength": 255
			},
			"drawToOsd": True,
			"sortNo": 5,
			"outOrIn": "IN"
		},
		{
			"algorithmInstanceId": "eeb86f1e8ceed1e76eb3bebc74c09a78",
			"sceneId": "78f85ea85935bc6d188e53706e2117ad",
			"value": "液位",
			"algorithmId": "e566f95a06ebe78c13ecdf440c160cf2",
			"key": "resultDes",
			"label": "结果描述",
			"dataType": "STRING",
			"constraints": {
				"required": False
			},
			"drawToOsd": True,
			"sortNo": 6,
			"outOrIn": "IN"
		}
	]
}
    inputs = AlgorithmInput()
    images = request_param['images']
    # new_image_arr = []
    # for img_path in images:
    #     # 下载图片到本地
    #     # local_path = MinioUtil.download(img_path)
    #     new_image_arr.append(img_path)
    inputs['images'] = images

    roi = request_param['roi']
    inputs['roi'] = roi

    inputParam = request_param['inputParam']
    inputs['inputParam'] = inputParam

    method_code = request_param['code']
    user_or_system = request_param['userOrSystem']

    # 输入参数校验
    from backend_common.maths.algorithm.base.parameter_check import ParameterCheck
    param_check = ParameterCheck()
    param_check.do_check_param(inputs)

    # 加载算法类
    custom_algorithm_instance = load_algorithm_super(method_code, user_or_system, inputs)

    assert custom_algorithm_instance is not None, "无法加载CV算法实例!"

    # 算法执行
    isSuccess, output, osdInfo = custom_algorithm_instance.perform()
    print(isSuccess, output, osdInfo)
    outputs = AlgorithmOutput(isSuccess, output, osdInfo)
    print(f"outputs {str(outputs)}")
    return outputs


def load_algorithm_super(algorithm_name, suer_or_system, inputs: AlgorithmInput):
    # 算法块唯一标识名,通常需与算法块文件夹名称一致，比如 liquid_level
    module_name = f".{algorithm_name}.ab_{algorithm_name}"
    target_clazz_name = f"{to_camel_case(algorithm_name, '_')}Reader"

    base_path = ALGORITHM_CV_SYSTEM_INNER_PATH
    if suer_or_system == "user_define":
        base_path = ALGORITHM_CV_USER_DEFINE_PATH

    try:
        print("ALGORITHM_CV_USER_DEFINE_PATH", base_path)
        print("module_name", module_name)
        print("target_clazz_name", target_clazz_name)

        M = importlib.import_module(module_name, base_path)
        # 找到对应的类
        target_clazz_ = getattr(M, target_clazz_name)
    except ModuleNotFoundError:
        raise InspectionException(
            f"Auto-load module '{module_name}' from '{base_path}' failure, please check it manually")

    # 类实例创建
    algorithm_instance: AlgorithmBase = target_clazz_(inputs)
    return algorithm_instance


@pytest.fixture
def primary_detect():
    return do_itself()

def test_primary(primary_detect):
    assert primary_detect.isSuccess is True

