#!/usr/bin/env python

"""Tests for `IndicatorFlashReader` algorithm."""
import importlib
import os
from typing import List

import cv2
import pytest

# from backend_common.constants.alarm import AlarmRule, ExceedLimitAlarmRule
# from backend_common.constants.constant import AlgorithmClassifyEnum, AlgorithmStrategyEnum, AlgorithmOutputTypeEnum
from backend_common.constants.math_constants import ALGORITHM_CV_USER_DEFINE_PATH, ALGORITHM_CV_SYSTEM_INNER_PATH
from backend_common.exceptions.inspection_exception import InspectionException
from backend_common.maths.algorithm.base.algorithm_base import AlgorithmOutput, AlgorithmBase, AlgorithmInput
from backend_common.utils.util import to_camel_case
from backend_common.utils import MinioUtil

# clazz_name_prefix = "IndicatorFlashReader"


def do_itself():
    # settings = AlgorithmSettings(classify, strategy, alarm_rules=alarm_rules)

    # algorithm_name = "material_blockage"
    # TODO 默认采样频率 500ms + 采样时长 5s, 即 10 张图片


    request_param = {
	"code": "liquid_color",
	"images": [
		"./test/resources/input-img/liquid_color/202507081800578156.JPEG"
	],
	"roi": {
		"dataType": "SQUARE",
		"constraints": {
			"required": False
		}
	},
	"sceneImage": "http://minio:9001/inspection/SCENE/202507081800507194.JPEG",
	"userOrSystem": "user_define",
	"inputParam": [
		{
			"algorithmInstanceId": "c88a418632eb9ca5aa007e1d34153a22",
			"sceneId": "341f0be841d001b2ee38fa79cb3a4514",
			"value": [
				{
					"x": 845,
					"y": 511
				},
				{
					"x": 845,
					"y": 550
				},
				{
					"x": 890,
					"y": 550
				},
				{
					"x": 890,
					"y": 511
				}
			],
			"algorithmId": "03a4813259acb447452e6e17255131b1",
			"key": "liquild_roi",
			"label": "液体区域",
			"dataType": "SQUARE",
			"constraints": {
				"required": True
			},
			"drawToOsd": True,
			"sortNo": 0,
			"outOrIn": "IN"
		},
		{
			"algorithmInstanceId": "c88a418632eb9ca5aa007e1d34153a22",
			"sceneId": "341f0be841d001b2ee38fa79cb3a4514",
			"value": [
				"0"
			],
			"algorithmId": "03a4813259acb447452e6e17255131b1",
			"key": "color_select",
			"label": "检测颜色",
			"dataType": "SELECTOR",
			"constraints": {
				"required": True,
				"maxLength": 1,
				"options": [
					{
						"key": "0",
						"label": "红色"
					},
					{
						"key": "2",
						"label": "绿色"
					},
					{
						"key": "3",
						"label": "蓝色"
					},
					{
						"key": "4",
						"label": "橙黄色"
					},
					{
						"key": "5",
						"label": "青色"
					},
					{
						"key": "6",
						"label": "紫色"
					},
					{
						"key": "7",
						"label": "黑色"
					},
					{
						"key": "8",
						"label": "白色"
					},
					{
						"key": "9",
						"label": "灰色"
					}
				]
			},
			"drawToOsd": False,
			"sortNo": 1,
			"outOrIn": "IN"
		},
		{
			"algorithmInstanceId": "c88a418632eb9ca5aa007e1d34153a22",
			"sceneId": "341f0be841d001b2ee38fa79cb3a4514",
			"value": 0,
			"algorithmId": "03a4813259acb447452e6e17255131b1",
			"key": "area_thresh",
			"label": "颜色面积",
			"dataType": "INTEGER",
			"defaultValue": "0",
			"constraints": {
				"required": True,
				"max": 3000,
				"min": 0,
				"precision": 10
			},
			"drawToOsd": False,
			"sortNo": 2,
			"outOrIn": "IN"
		},
		{
			"algorithmInstanceId": "c88a418632eb9ca5aa007e1d34153a22",
			"sceneId": "341f0be841d001b2ee38fa79cb3a4514",
			"value": 0.27,
			"algorithmId": "03a4813259acb447452e6e17255131b1",
			"key": "area_ratio_thresh",
			"label": "面积占比",
			"dataType": "FLOAT",
			"defaultValue": "0.5",
			"constraints": {
				"required": True,
				"max": 1,
				"min": 0,
				"precision": 0.01
			},
			"drawToOsd": False,
			"sortNo": 3,
			"outOrIn": "IN"
		},
		{
			"algorithmInstanceId": "c88a418632eb9ca5aa007e1d34153a22",
			"sceneId": "341f0be841d001b2ee38fa79cb3a4514",
			"value": "红色",
			"algorithmId": "03a4813259acb447452e6e17255131b1",
			"key": "resultDes",
			"label": "结果描述",
			"dataType": "STRING",
			"constraints": {
				"required": False
			},
			"drawToOsd": True,
			"sortNo": 4,
			"outOrIn": "IN"
		}
	]
}
    inputs = AlgorithmInput()
    images = request_param['images']
    # new_image_arr = []
    # for img_path in images:
    #     # 下载图片到本地
    #     # local_path = MinioUtil.download(img_path)
    #     new_image_arr.append(img_path)
    inputs['images'] = images

    roi = request_param['roi']
    inputs['roi'] = roi

    inputParam = request_param['inputParam']
    inputs['inputParam'] = inputParam

    method_code = request_param['code']
    user_or_system = request_param['userOrSystem']

    # 输入参数校验
    from backend_common.maths.algorithm.base.parameter_check import ParameterCheck
    param_check = ParameterCheck()
    param_check.do_check_param(inputs)

    # 加载算法类
    custom_algorithm_instance = load_algorithm_super(method_code, user_or_system, inputs)

    assert custom_algorithm_instance is not None, "无法加载CV算法实例!"

    # 算法执行
    isSuccess, output, osdInfo = custom_algorithm_instance.perform()
    print(isSuccess, output, osdInfo)
    outputs = AlgorithmOutput(isSuccess, output, osdInfo)
    print(f"outputs {str(outputs)}")
    return outputs


def load_algorithm_super(algorithm_name, suer_or_system, inputs: AlgorithmInput):
    # 算法块唯一标识名,通常需与算法块文件夹名称一致，比如 liquid_level
    module_name = f".{algorithm_name}.ab_{algorithm_name}"
    target_clazz_name = f"{to_camel_case(algorithm_name, '_')}Reader"

    base_path = ALGORITHM_CV_SYSTEM_INNER_PATH
    if suer_or_system == "user_define":
        base_path = ALGORITHM_CV_USER_DEFINE_PATH

    try:
        print("ALGORITHM_CV_USER_DEFINE_PATH", base_path)
        print("module_name", module_name)
        print("target_clazz_name", target_clazz_name)

        M = importlib.import_module(module_name, base_path)
        # 找到对应的类
        target_clazz_ = getattr(M, target_clazz_name)
    except ModuleNotFoundError:
        raise InspectionException(
            f"Auto-load module '{module_name}' from '{base_path}' failure, please check it manually")

    # 类实例创建
    algorithm_instance: AlgorithmBase = target_clazz_(inputs)
    return algorithm_instance


@pytest.fixture
def primary_detect():
    return do_itself()

def test_primary(primary_detect):
    assert primary_detect.isSuccess is True

