{"code": "CODE_10093", "imgPath": "[\"D:\\\\\\\\project\\\\\\\\pic\\\\\\\\202301120951081.jpg\"]", "inputParam": {"color": {"algorithmId": 25, "cruisePointId": 1, "des": "下拉参数", "id": 492, "key": "color", "name": "液柱颜色", "outOrIn": "input", "type": "string", "value": "Red"}, "liquid": {"algorithmId": 25, "cruisePointId": 1, "des": "一个point对象，点的坐标", "id": 489, "key": "liquid", "name": "当前液位", "outOrIn": "input", "type": "point", "value": "{\"x\":893,\"y\":576}"}, "lower": {"algorithmId": 25, "cruisePointId": 1, "des": "一个point对象，点的坐标", "id": 488, "key": "lower", "name": "报警下限", "outOrIn": "input", "type": "point", "value": "{\"x\":904,\"y\":694}"}, "upper": {"algorithmId": 25, "cruisePointId": 1, "des": "一个point对象，点的坐标", "id": 487, "key": "upper", "name": "报警上限", "outOrIn": "input", "type": "point", "value": "{\"x\":886,\"y\":470}"}, "column": {"algorithmId": 25, "cruisePointId": 1, "des": "数组形式，元素是一个point对象，长度为4，分别为左上、左下、右下、右上顶点坐标", "extendParam": "{\"width\":14,\"angle\":354,\"centerPoint\":{\"x\":540,\"y\":366},\"height\":222}", "id": 486, "key": "column", "name": "矩形液柱", "outOrIn": "input", "type": "square", "value": "[{\"x\":869,\"y\":426},{\"x\":894,\"y\":423},{\"x\":928,\"y\":793},{\"x\":903,\"y\":796}]"}, "range_down": {"algorithmId": 25, "cruisePointId": 1, "des": "液位上下限", "id": 491, "key": "range_down", "name": "量程下限", "outOrIn": "input", "type": "number", "value": "-30"}, "range_up": {"algorithmId": 25, "cruisePointId": 1, "des": "液位上下限", "id": 490, "key": "range_up", "name": "量程上限", "outOrIn": "input", "type": "number", "value": "50"}}, "roiSquare": "[{\"x\":762,\"y\":285},{\"x\":1040,\"y\":285},{\"x\":1040,\"y\":1060},{\"x\":762,\"y\":1060}]"}