#!/usr/bin/env python

"""Tests for `QrCodeReader & Bar` algorithm."""
import importlib
import os

import cv2
import pytest

from backend_common.constants.alarm import AlarmRule
from backend_common.constants.constant import AlgorithmClassifyEnum, AlgorithmStrategyEnum, AlgorithmOutputTypeEnum
from backend_common.constants.math_constants import ALGORITHM_CV_USER_DEFINE_PATH, ALGORITHM_CV_SYSTEM_INNER_PATH
from backend_common.utils.util import to_camel_case
from backend_common.exceptions.inspection_exception import InspectionException
from backend_common.maths.algorithm.base.algorithm_base import AlgorithmOutput, AlgorithmBase, AlgorithmSettings, AlgorithmInput


def do_itself(algorithm_index, classify, strategy, output_type, alarm_rule: AlarmRule = None):
    settings = AlgorithmSettings(classify, strategy, alarm_rules=[])
    basedir = os.path.dirname(os.path.abspath(__file__))
    algorithm_name = "qrcode"

    # inputs = AlgorithmInput({"images": [
    #     # os.path.join(basedir, "./resources/input-img/qrcode/bar0.jpg"),
    #     os.path.join(basedir, "./resources/input-img/qrcode/bar3.jpg")
    # ],
    #     "bar": [[27, 54], [25, 437], [410, 436], [412, 59], ]})

    inputs = AlgorithmInput({"images": [
        # os.path.join(basedir, "./resources/input-img/qrcode/bar0.jpg"),
        os.path.join(basedir, "./resources/input-img/qrcode/202306290951508947.JPEG")
    ],
        "bar": [
            [
                478,
                451
            ],
            [
                478,
                839
            ],
            [
                858,
                839
            ],
            [
                858,
                451
            ]
        ]})

    sup: AlgorithmBase
    sup = load_algorithm_super(algorithm_name, "system_inner", inputs)
    target_instance = sup.determine_algorithm_instance(settings, algorithm_index)

    assert target_instance is not None
    ret, val, points, alarms = target_instance.perform(settings)
    print(f"结果 {ret, val, points, alarms}")

    outputs = AlgorithmOutput(ret, val, points, alarms)
    print(f"outputs {str(outputs)}")

    # 可选操作
    ret_images = target_instance.gen_result_img()
    if ret_images is not None:
        for idx, img in enumerate(ret_images):
            cv2.imwrite(os.getcwd() + f"/40_result_{idx}.jpg", img)

    return outputs


def load_algorithm_super(algorithm_name, a_type, inputs: AlgorithmInput):
    # self._name  算法块唯一标识名,通常需与算法块文件夹名称一致，比如 liquid_level
    module_name = f".{algorithm_name}.ab_{algorithm_name}"
    target_clazz_name = f"{to_camel_case(algorithm_name, '_')}Reader"

    base_path = ALGORITHM_CV_SYSTEM_INNER_PATH
    if a_type == "user_define":
        base_path = ALGORITHM_CV_USER_DEFINE_PATH

    try:
        M = importlib.import_module(module_name, base_path)
        target_clazz_ = getattr(M, target_clazz_name)
    except ModuleNotFoundError:
        raise InspectionException(
            f"Auto-load module '{module_name}' from '{base_path}' failure, please check it manually")

    algorithm_instance: AlgorithmBase = target_clazz_(inputs, -1)
    return algorithm_instance


@pytest.fixture
def primary_detect():
    algorithm_index = 0
    classify = AlgorithmClassifyEnum.Primary
    strategy = AlgorithmStrategyEnum.Main
    output_type = AlgorithmOutputTypeEnum.ValueQuality

    # 报警条件（可选）
    # alarm_rule = ExceedLimitAlarmRule(47, 17, -1, -15)
    return do_itself(algorithm_index, classify, strategy, output_type)


def test_primary(primary_detect):
    assert primary_detect.ret is True


def test_img_click():
    # mouse callback function
    def draw_circle(event, x, y, flags, param):
        if event == cv2.EVENT_LBUTTONDOWN:
            cv2.circle(img, (x, y), 100, (255, 0, 0), -1)
            print(f"[{x},{y}],")

    img = cv2.imread("resources/input-img/qrcode/bar0.jpg")

    cv2.imshow("img", img)
    cv2.setMouseCallback("img", draw_circle)
    cv2.waitKey()
