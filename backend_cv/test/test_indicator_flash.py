#!/usr/bin/env python

"""Tests for `IndicatorFlashReader` algorithm."""
import importlib
import os
from typing import List

import cv2
import pytest

# from backend_common.constants.alarm import AlarmRule, ExceedLimitAlarmRule
# from backend_common.constants.constant import AlgorithmClassifyEnum, AlgorithmStrategyEnum, AlgorithmOutputTypeEnum
from backend_common.constants.math_constants import ALGORITHM_CV_USER_DEFINE_PATH, ALGORITHM_CV_SYSTEM_INNER_PATH
from backend_common.exceptions.inspection_exception import InspectionException
from backend_common.maths.algorithm.base.algorithm_base import AlgorithmOutput, AlgorithmBase, AlgorithmInput
from backend_common.utils.util import to_camel_case
from backend_common.utils import MinioUtil

# clazz_name_prefix = "IndicatorFlashReader"


def do_itself():
    # settings = AlgorithmSettings(classify, strategy, alarm_rules=alarm_rules)

    # algorithm_name = "indicator_flash"
    # TODO 默认采样频率 500ms + 采样时长 5s, 即 10 张图片


    request_param = {
	"code": "indicator_flash",
	"images": [
		"./test/resources/input-img/indicator_flash/202507081725106223.JPEG",
		"./test/resources/input-img/indicator_flash/202507081725114983.JPEG",
		"./test/resources/input-img/indicator_flash/202507081725123972.JPEG"
	],
	"roi": {
		"dataType": "SQUARE",
		"constraints": {
			"required": False
		}
	},
	"sceneImage": "http://minio:9001/inspection/SCENE/202507081724085546.JPEG",
	"userOrSystem": "user_define",
	"inputParam": [
		{
			"algorithmInstanceId": "09e1eaa9c8237b563a08cccdfbd6d299",
			"sceneId": "07b40867ee8c0cff0d080afe9c336bbe",
			"value": {
				"center": {
					"x": 1062,
					"y": 401
				},
				"radius": 59
			},
			"algorithmId": "889415d1667842d6304cdbb1e5d9dc45",
			"key": "circle",
			"label": "指示灯截取圆",
			"dataType": "CIRCLE",
			"constraints": {
				"required": True
			},
			"drawToOsd": True,
			"sortNo": 0,
			"outOrIn": "IN",
			"id": "b12eb78a7e83691642e5a9e8d1faa863"
		},
		{
			"algorithmInstanceId": "09e1eaa9c8237b563a08cccdfbd6d299",
			"sceneId": "07b40867ee8c0cff0d080afe9c336bbe",
			"value": [
				123,
				136,
				129
			],
			"algorithmId": "889415d1667842d6304cdbb1e5d9dc45",
			"key": "colorPicker",
			"label": "灯亮颜色",
			"dataType": "RGB",
			"defaultValue": [
				0,
				0,
				0
			],
			"constraints": {
				"required": True,
				"minLength": 3
			},
			"drawToOsd": False,
			"sortNo": 1,
			"outOrIn": "IN",
			"id": "0cbc01e5924bee4c2686055c0ba4e2e9"
		},
		{
			"algorithmInstanceId": "09e1eaa9c8237b563a08cccdfbd6d299",
			"sceneId": "07b40867ee8c0cff0d080afe9c336bbe",
			"value": 55,
			"algorithmId": "889415d1667842d6304cdbb1e5d9dc45",
			"key": "colorThreshold",
			"label": "颜色偏差",
			"dataType": "INTEGER",
			"defaultValue": "55",
			"constraints": {
				"required": True,
				"max": 255,
				"min": 0,
				"precision": 1
			},
			"drawToOsd": False,
			"sortNo": 2,
			"outOrIn": "IN",
			"id": "88edc61ae02db96a17d234650635a88a"
		},
		{
			"algorithmInstanceId": "09e1eaa9c8237b563a08cccdfbd6d299",
			"sceneId": "07b40867ee8c0cff0d080afe9c336bbe",
			"value": 0.42,
			"algorithmId": "889415d1667842d6304cdbb1e5d9dc45",
			"key": "threshold",
			"label": "识别灵敏度",
			"dataType": "FLOAT",
			"defaultValue": "0.42",
			"constraints": {
				"required": True,
				"max": 1,
				"min": 0,
				"precision": 0.01
			},
			"drawToOsd": False,
			"sortNo": 3,
			"outOrIn": "IN",
			"id": "ccd3bd69c4b6bbdeae30bef659bff6e2"
		},
		{
			"algorithmInstanceId": "09e1eaa9c8237b563a08cccdfbd6d299",
			"sceneId": "07b40867ee8c0cff0d080afe9c336bbe",
			"value": "",
			"algorithmId": "889415d1667842d6304cdbb1e5d9dc45",
			"key": "resultDes",
			"label": "结果描述",
			"dataType": "STRING",
			"constraints": {
				"required": False
			},
			"drawToOsd": True,
			"sortNo": 4,
			"outOrIn": "IN",
			"id": "6730fac6023f777e5d1188f56b0b7c9f"
		}
	]
    }
    inputs = AlgorithmInput()

    images = request_param['images']
    # new_image_arr = []
    # for img_path in images:
    #     # 下载图片到本地
    #     # local_path = MinioUtil.download(img_path)
    #     new_image_arr.append(img_path)
    inputs['images'] = images

    roi = request_param['roi']
    inputs['roi'] = roi

    inputParam = request_param['inputParam']
    inputs['inputParam'] = inputParam

    method_code = request_param['code']
    user_or_system = request_param['userOrSystem']

    # 输入参数校验
    from backend_common.maths.algorithm.base.parameter_check import ParameterCheck
    param_check = ParameterCheck()
    param_check.do_check_param(inputs)

    # 加载算法类
    custom_algorithm_instance = load_algorithm_super(method_code, user_or_system, inputs)

    assert custom_algorithm_instance is not None, "无法加载CV算法实例!"

    # 算法执行
    isSuccess, output, osdInfo = custom_algorithm_instance.perform()
    print(isSuccess, output, osdInfo)
    outputs = AlgorithmOutput(isSuccess, output, osdInfo)
    print(f"outputs {str(outputs)}")
    return outputs


def load_algorithm_super(algorithm_name, suer_or_system, inputs: AlgorithmInput):
    # 算法块唯一标识名,通常需与算法块文件夹名称一致，比如 liquid_level
    module_name = f".{algorithm_name}.ab_{algorithm_name}"
    target_clazz_name = f"{to_camel_case(algorithm_name, '_')}Reader"

    base_path = ALGORITHM_CV_SYSTEM_INNER_PATH
    if suer_or_system == "user_define":
        base_path = ALGORITHM_CV_USER_DEFINE_PATH

    try:
        print("ALGORITHM_CV_USER_DEFINE_PATH", base_path)
        print("module_name", module_name)
        print("target_clazz_name", target_clazz_name)

        M = importlib.import_module(module_name, base_path)
        # 找到对应的类
        target_clazz_ = getattr(M, target_clazz_name)
    except ModuleNotFoundError:
        raise InspectionException(
            f"Auto-load module '{module_name}' from '{base_path}' failure, please check it manually")

    # 类实例创建
    algorithm_instance: AlgorithmBase = target_clazz_(inputs)
    return algorithm_instance


@pytest.fixture
def primary_detect():
    return do_itself()

def test_primary(primary_detect):
    assert primary_detect.isSuccess is True

