#!/usr/bin/env python

"""Tests for `IndicatorFlashReader` algorithm."""
import importlib
import os
from typing import List

import cv2
import pytest

# from backend_common.constants.alarm import AlarmRule, ExceedLimitAlarmRule
# from backend_common.constants.constant import AlgorithmClassifyEnum, AlgorithmStrategyEnum, AlgorithmOutputTypeEnum
from backend_common.constants.math_constants import ALGORITHM_CV_USER_DEFINE_PATH, ALGORITHM_CV_SYSTEM_INNER_PATH
from backend_common.exceptions.inspection_exception import InspectionException
from backend_common.maths.algorithm.base.algorithm_base import AlgorithmOutput, AlgorithmBase, AlgorithmInput
from backend_common.utils.util import to_camel_case
from backend_common.utils import MinioUtil

# clazz_name_prefix = "IndicatorFlashReader"


def do_itself():
    # settings = AlgorithmSettings(classify, strategy, alarm_rules=alarm_rules)

    # algorithm_name = "material_blockage"
    # TODO 默认采样频率 500ms + 采样时长 5s, 即 10 张图片


    request_param = {
	"code": "liquid_turbid",
	"images": [
		"./test/resources/input-img/liquid_turbid/202507081807297100.JPEG"
	],
	"roi": {
		"dataType": "SQUARE",
		"constraints": {
			"required": False
		}
	},
	"sceneImage": "http://minio:9001/inspection/SCENE/202507081807221278.JPEG",
	"userOrSystem": "user_define",
	"inputParam": [
		{
			"algorithmInstanceId": "f51b3d521b8e2439cb5c7235fddb0ec2",
			"sceneId": "cf26dbc82a60259c1b08683f7a1a4fe4",
			"value": {
				"center": {
					"x": 1060,
					"y": 403
				},
				"radius": 48
			},
			"algorithmId": "46094f22f1b34f2b99c11b90b653816d",
			"key": "circle_roi",
			"label": "圆心坐标及半径",
			"dataType": "CIRCLE",
			"constraints": {
				"required": True
			},
			"drawToOsd": True,
			"sortNo": 0,
			"outOrIn": "IN"
		},
		{
			"algorithmInstanceId": "f51b3d521b8e2439cb5c7235fddb0ec2",
			"sceneId": "cf26dbc82a60259c1b08683f7a1a4fe4",
			"value": [
				"5"
			],
			"algorithmId": "46094f22f1b34f2b99c11b90b653816d",
			"key": "color_select",
			"label": "检测颜色",
			"dataType": "SELECTOR",
			"constraints": {
				"required": True,
				"maxLength": 1,
				"options": [
					{
						"key": "0",
						"label": "红色"
					},
					{
						"key": "2",
						"label": "绿色"
					},
					{
						"key": "3",
						"label": "蓝色"
					},
					{
						"key": "4",
						"label": "橙黄色"
					},
					{
						"key": "5",
						"label": "青色"
					},
					{
						"key": "6",
						"label": "紫色"
					},
					{
						"key": "7",
						"label": "黑色"
					},
					{
						"key": "8",
						"label": "白色"
					},
					{
						"key": "9",
						"label": "灰色"
					}
				]
			},
			"drawToOsd": False,
			"sortNo": 1,
			"outOrIn": "IN"
		},
		{
			"algorithmInstanceId": "f51b3d521b8e2439cb5c7235fddb0ec2",
			"sceneId": "cf26dbc82a60259c1b08683f7a1a4fe4",
			"value": "浑浊度",
			"algorithmId": "46094f22f1b34f2b99c11b90b653816d",
			"key": "resultDes",
			"label": "结果描述",
			"dataType": "STRING",
			"constraints": {
				"required": False
			},
			"drawToOsd": True,
			"sortNo": 2,
			"outOrIn": "IN"
		}
	]
}
    inputs = AlgorithmInput()
    images = request_param['images']
    # new_image_arr = []
    # for img_path in images:
    #     # 下载图片到本地
    #     # local_path = MinioUtil.download(img_path)
    #     new_image_arr.append(img_path)
    inputs['images'] = images

    roi = request_param['roi']
    inputs['roi'] = roi

    inputParam = request_param['inputParam']
    inputs['inputParam'] = inputParam

    method_code = request_param['code']
    user_or_system = request_param['userOrSystem']

    # 输入参数校验
    from backend_common.maths.algorithm.base.parameter_check import ParameterCheck
    param_check = ParameterCheck()
    param_check.do_check_param(inputs)

    # 加载算法类
    custom_algorithm_instance = load_algorithm_super(method_code, user_or_system, inputs)

    assert custom_algorithm_instance is not None, "无法加载CV算法实例!"

    # 算法执行
    isSuccess, output, osdInfo = custom_algorithm_instance.perform()
    print(isSuccess, output, osdInfo)
    outputs = AlgorithmOutput(isSuccess, output, osdInfo)
    print(f"outputs {str(outputs)}")
    return outputs


def load_algorithm_super(algorithm_name, suer_or_system, inputs: AlgorithmInput):
    # 算法块唯一标识名,通常需与算法块文件夹名称一致，比如 liquid_level
    module_name = f".{algorithm_name}.ab_{algorithm_name}"
    target_clazz_name = f"{to_camel_case(algorithm_name, '_')}Reader"

    base_path = ALGORITHM_CV_SYSTEM_INNER_PATH
    if suer_or_system == "user_define":
        base_path = ALGORITHM_CV_USER_DEFINE_PATH

    try:
        print("ALGORITHM_CV_USER_DEFINE_PATH", base_path)
        print("module_name", module_name)
        print("target_clazz_name", target_clazz_name)

        M = importlib.import_module(module_name, base_path)
        # 找到对应的类
        target_clazz_ = getattr(M, target_clazz_name)
    except ModuleNotFoundError:
        raise InspectionException(
            f"Auto-load module '{module_name}' from '{base_path}' failure, please check it manually")

    # 类实例创建
    algorithm_instance: AlgorithmBase = target_clazz_(inputs)
    return algorithm_instance


@pytest.fixture
def primary_detect():
    return do_itself()

def test_primary(primary_detect):
    assert primary_detect.isSuccess is True

