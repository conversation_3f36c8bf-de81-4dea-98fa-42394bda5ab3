#!/usr/bin/env python

"""Tests for `IndicatorFlashReader` algorithm."""
import importlib
import os
from typing import List

import cv2
import pytest

# from backend_common.constants.alarm import AlarmRule, ExceedLimitAlarmRule
# from backend_common.constants.constant import AlgorithmClassifyEnum, AlgorithmStrategyEnum, AlgorithmOutputTypeEnum
from backend_common.constants.math_constants import ALGORITHM_CV_USER_DEFINE_PATH, ALGORITHM_CV_SYSTEM_INNER_PATH
from backend_common.exceptions.inspection_exception import InspectionException
from backend_common.maths.algorithm.base.algorithm_base import AlgorithmOutput, AlgorithmBase, AlgorithmInput
from backend_common.utils.util import to_camel_case
from backend_common.utils import MinioUtil

# clazz_name_prefix = "IndicatorFlashReader"


def do_itself():
    # settings = AlgorithmSettings(classify, strategy, alarm_rules=alarm_rules)

    # algorithm_name = "indicator_flash"
    # TODO 默认采样频率 500ms + 采样时长 5s, 即 10 张图片


    request_param = {
	"code": "liquid_level",
	"images": [
		# ".\\test\\resources\\input-img\\liquid_level\\202507081042211802.jpg"
        "./test/resources/input-img/liquid_level/202507081042211802.jpg"
	],
	"roi": {
		"dataType": "SQUARE",
		"constraints": {
			"required": False
		}
	},
	"sceneImage": "http://minio:9001/inspection/SCENE/202507081041056869.jpg",
	"userOrSystem": "system_inner",
	"inputParam": [
		{
			"algorithmInstanceId": "29534b1d92d194fc23aa8c3d884331c4",
			"sceneId": "4f15dd48f4f23b08a2d19bd8ba1a2a9e",
			"value": [
				{
					"x": 1210,
					"y": 443
				},
				{
					"x": 1210,
					"y": 681
				},
				{
					"x": 1252,
					"y": 681
				},
				{
					"x": 1252,
					"y": 443
				}
			],
			"algorithmId": "c067b9f72adf9c795f630c7b95e47510",
			"key": "liquidColumn",
			"label": "液柱区域",
			"dataType": "SQUARE",
			"constraints": {
				"required": True
			},
			"drawToOsd": False,
			"sortNo": 0,
			"outOrIn": "IN",
			"id": "c662005087248b251f750fb9fd0ec634"
		},
		{
			"algorithmInstanceId": "29534b1d92d194fc23aa8c3d884331c4",
			"sceneId": "4f15dd48f4f23b08a2d19bd8ba1a2a9e",
			"value": [
				64,
				228,
				27
			],
			"algorithmId": "c067b9f72adf9c795f630c7b95e47510",
			"key": "colorPicker",
			"label": "液体颜色",
			"dataType": "RGB",
			"defaultValue": [
				0,
				0,
				0
			],
			"constraints": {
				"required": True,
				"minLength": 3
			},
			"drawToOsd": False,
			"sortNo": 1,
			"outOrIn": "IN",
			"id": "b5a4ab935bb5228a04de89875a4bb1e1"
		},
		{
			"algorithmInstanceId": "29534b1d92d194fc23aa8c3d884331c4",
			"sceneId": "4f15dd48f4f23b08a2d19bd8ba1a2a9e",
			"value": 40,
			"algorithmId": "c067b9f72adf9c795f630c7b95e47510",
			"key": "colorThreshold",
			"label": "颜色偏差",
			"dataType": "INTEGER",
			"defaultValue": "55",
			"constraints": {
				"required": True,
				"max": 255,
				"min": 0,
				"precision": 1
			},
			"drawToOsd": False,
			"sortNo": 2,
			"outOrIn": "IN",
			"id": "d28b329af51ee682c6f33474142304d0"
		},
		{
			"algorithmInstanceId": "29534b1d92d194fc23aa8c3d884331c4",
			"sceneId": "4f15dd48f4f23b08a2d19bd8ba1a2a9e",
			"value": {
				"start": 0,
				"end": 500
			},
			"algorithmId": "c067b9f72adf9c795f630c7b95e47510",
			"key": "range",
			"label": "量程",
			"dataType": "RANGE",
			"defaultValue": {
				"start": -30,
				"end": 50
			},
			"constraints": {
				"required": True,
				"max": 999999,
				"min": -999999,
				"precision": 0.001
			},
			"drawToOsd": False,
			"sortNo": 3,
			"outOrIn": "IN",
			"id": "7d95a9bb804b77137cd8449835450443"
		},
		{
			"algorithmInstanceId": "29534b1d92d194fc23aa8c3d884331c4",
			"sceneId": "4f15dd48f4f23b08a2d19bd8ba1a2a9e",
			"value": 1,
			"algorithmId": "c067b9f72adf9c795f630c7b95e47510",
			"key": "resultPrecision",
			"label": "结果精度",
			"dataType": "INTEGER",
			"defaultValue": "2",
			"constraints": {
				"required": False,
				"max": 5,
				"min": 0,
				"precision": 1
			},
			"drawToOsd": False,
			"sortNo": 4,
			"outOrIn": "IN",
			"id": "648168cfd4808967ac49de27529300fa"
		},
		{
			"algorithmInstanceId": "29534b1d92d194fc23aa8c3d884331c4",
			"sceneId": "4f15dd48f4f23b08a2d19bd8ba1a2a9e",
			"value": "",
			"algorithmId": "c067b9f72adf9c795f630c7b95e47510",
			"key": "unit",
			"label": "单位",
			"dataType": "STRING",
			"constraints": {
				"required": False,
				"regexPattern": "^[A-Za-z0-9_]+$",
				"maxLength": 255
			},
			"drawToOsd": False,
			"sortNo": 5,
			"outOrIn": "IN"
		},
		{
			"algorithmInstanceId": "29534b1d92d194fc23aa8c3d884331c4",
			"sceneId": "4f15dd48f4f23b08a2d19bd8ba1a2a9e",
			"value": "",
			"algorithmId": "c067b9f72adf9c795f630c7b95e47510",
			"key": "resultDes",
			"label": "结果描述",
			"dataType": "STRING",
			"constraints": {
				"required": False
			},
			"drawToOsd": False,
			"sortNo": 6,
			"outOrIn": "IN"
		}
	]
    }
    inputs = AlgorithmInput()

    images = request_param['images']
    # new_image_arr = []
    # for img_path in images:
    #     # 下载图片到本地
    #     # local_path = MinioUtil.download(img_path)
    #     new_image_arr.append(img_path)
    inputs['images'] = images

    roi = request_param['roi']
    inputs['roi'] = roi

    inputParam = request_param['inputParam']
    inputs['inputParam'] = inputParam

    method_code = request_param['code']
    user_or_system = request_param['userOrSystem']

    # 输入参数校验
    from backend_common.maths.algorithm.base.parameter_check import ParameterCheck
    param_check = ParameterCheck()
    param_check.do_check_param(inputs)

    # 加载算法类
    custom_algorithm_instance = load_algorithm_super(method_code, user_or_system, inputs)

    assert custom_algorithm_instance is not None, "无法加载CV算法实例!"

    # 算法执行
    isSuccess, output, osdInfo = custom_algorithm_instance.perform()
    print(isSuccess, output, osdInfo)
    outputs = AlgorithmOutput(isSuccess, output, osdInfo)
    print(f"outputs {str(outputs)}")
    return outputs


def load_algorithm_super(algorithm_name, suer_or_system, inputs: AlgorithmInput):
    # 算法块唯一标识名,通常需与算法块文件夹名称一致，比如 liquid_level
    module_name = f".{algorithm_name}.ab_{algorithm_name}"
    target_clazz_name = f"{to_camel_case(algorithm_name, '_')}Reader"

    base_path = ALGORITHM_CV_SYSTEM_INNER_PATH
    if suer_or_system == "user_define":
        base_path = ALGORITHM_CV_USER_DEFINE_PATH

    try:
        print("ALGORITHM_CV_USER_DEFINE_PATH", base_path)
        print("module_name", module_name)
        print("target_clazz_name", target_clazz_name)

        M = importlib.import_module(module_name, base_path)
        # 找到对应的类
        target_clazz_ = getattr(M, target_clazz_name)
    except ModuleNotFoundError:
        raise InspectionException(
            f"Auto-load module '{module_name}' from '{base_path}' failure, please check it manually")

    # 类实例创建
    algorithm_instance: AlgorithmBase = target_clazz_(inputs)
    return algorithm_instance


@pytest.fixture
def primary_detect():
    return do_itself()

def test_primary(primary_detect):
    assert primary_detect.isSuccess is True

