#!/usr/bin/env python

"""Tests for `IndicatorFlashReader` algorithm."""
import importlib
import os
from typing import List

import cv2
import pytest

# from backend_common.constants.alarm import AlarmRule, ExceedLimitAlarmRule
# from backend_common.constants.constant import AlgorithmClassifyEnum, AlgorithmStrategyEnum, AlgorithmOutputTypeEnum
from backend_common.constants.math_constants import ALGORITHM_CV_USER_DEFINE_PATH, ALGORITHM_CV_SYSTEM_INNER_PATH
from backend_common.exceptions.inspection_exception import InspectionException
from backend_common.maths.algorithm.base.algorithm_base import AlgorithmOutput, AlgorithmBase, AlgorithmInput
from backend_common.utils.util import to_camel_case
from backend_common.utils import MinioUtil

# clazz_name_prefix = "IndicatorFlashReader"


def do_itself():
    # settings = AlgorithmSettings(classify, strategy, alarm_rules=alarm_rules)

    # algorithm_name = "indicator_flash"
    # TODO 默认采样频率 500ms + 采样时长 5s, 即 10 张图片


    request_param = {
	"code": "fire_monitor",
	"images": [
        # ".\\test\\resources\\input-img\\indicator_switch\\202507071548003625.jpg"
        "./test/resources/input-img/fire_monitor/010.jpg"
	],
	"roi": {
		"dataType": "SQUARE",
		"constraints": {
			"required": False
		}
	},
	"sceneImage": "http://192.168.253.113:18010/inspection/SCENE/202507071547149772.jpg",
	"userOrSystem": "user_define",
	"inputParam": [
        {
				"algorithmInstanceId": "767f34bffe5e59d5b20c5fcd815c5b3e",
				"sceneId": "07978686f85438254e30099e60b8c24b",
				"value": [
					{
						"x": 29,
						"y": 16
					},
					{
						"x": 1258,
						"y": 16
					},
					{
						"x": 1258,
						"y": 345
					},
					{
						"x": 29,
						"y": 345
					}
				],
				"algorithmId": "da565bdd225868bdf8a8791f42e4b06f",
				"key": "roi1",
				"label": "ROI1区域",
				"dataType": "SQUARE",
				"constraints": {
					"required": True
				},
				"drawToOsd": False,
				"sortNo": 0,
				"outOrIn": "IN"
		},
        {
				"algorithmInstanceId": "767f34bffe5e59d5b20c5fcd815c5b3e",
				"sceneId": "07978686f85438254e30099e60b8c24b",
				"value": [
					{
						"x": 30,
						"y": 354
					},
					{
						"x": 1259,
						"y": 354
					},
					{
						"x": 1259,
						"y": 699
					},
					{
						"x": 30,
						"y": 699
					}
				],
				"algorithmId": "da565bdd225868bdf8a8791f42e4b06f",
				"key": "roi2",
				"label": "ROI2区域",
				"dataType": "SQUARE",
				"constraints": {
					"required": True
				},
				"drawToOsd": False,
				"sortNo": 1,
				"outOrIn": "IN"
		},
        {
			"algorithmInstanceId": "29534b1d92d194fc23aa8c3d884331c4",
			"sceneId": "4f15dd48f4f23b08a2d19bd8ba1a2a9e",
			"value": {
				"start": 1,
				"end": 2
			},
			"algorithmId": "c067b9f72adf9c795f630c7b95e47510",
			"key": "range1",
			"label": "量程1",
			"dataType": "RANGE",
			"defaultValue": {
				"start": 1,
				"end": 2
			},
			"constraints": {
				"required": True,
				"max": 999999,
				"min": -999999,
				"precision": 1
			},
			"drawToOsd": False,
			"sortNo": 2,
			"outOrIn": "IN",
			"id": "7d95a9bb804b77137cd8449835450443"
		},
        {
			"algorithmInstanceId": "29534b1d92d194fc23aa8c3d884331c4",
			"sceneId": "4f15dd48f4f23b08a2d19bd8ba1a2a9e",
			"value": {
				"start": 1,
				"end": 2
			},
			"algorithmId": "c067b9f72adf9c795f630c7b95e47510",
			"key": "range2",
			"label": "量程2",
			"dataType": "RANGE",
			"defaultValue": {
				"start": 1,
				"end": 2
			},
			"constraints": {
				"required": True,
				"max": 999999,
				"min": -999999,
				"precision": 1
			},
			"drawToOsd": False,
			"sortNo": 3,
			"outOrIn": "IN",
			"id": "7d95a9bb804b77137cd8449835450443"
		},
		{
			"algorithmInstanceId": "ff43550af80aac096de71d8e114e0d62",
			"sceneId": "8ca71723ce952bf9f91e30ddcec36b70",
			"value": 920,
			"algorithmId": "f4edaafc4bc2d58ac939794d2126c584",
			"key": "main_fireline_temp",
			"label": "主火线分割温度",
			"dataType": "INTEGER",
			"defaultValue": 920,
			"constraints": {
				"min": 800,
				"max": 1000,
				"precision": 5,
				"required": True
			},
			"drawToOsd": False,
			"sortNo": 4,
			"outOrIn": "IN",
			"id": "baf16554c33047925c6be9ec6fd21bbc"
		},
        {
			"algorithmInstanceId": "ff43550af80aac096de71d8e114e0d62",
			"sceneId": "8ca71723ce952bf9f91e30ddcec36b70",
			"value": 900,
			"algorithmId": "f4edaafc4bc2d58ac939794d2126c584",
			"key": "aux_fireline_temp",
			"label": "副火线分割温度",
			"dataType": "INTEGER",
			"defaultValue": 900,
			"constraints": {
				"min": 800,
				"max": 1000,
				"precision": 5,
				"required": True
			},
			"drawToOsd": False,
			"sortNo": 5,
			"outOrIn": "IN",
			"id": "baf16554c33047925c6be9ec6fd21bbc"
		},
        {
			"algorithmInstanceId": "ff43550af80aac096de71d8e114e0d62",
			"sceneId": "8ca71723ce952bf9f91e30ddcec36b70",
			"value": 1200,
			"algorithmId": "f4edaafc4bc2d58ac939794d2126c584",
			"key": "max_temp",
			"label": "设定最高温度",
			"dataType": "INTEGER",
			"defaultValue": 1200,
			"constraints": {
				"min": 800,
				"max": 1500,
				"precision": 10,
				"required": True
			},
			"drawToOsd": False,
			"sortNo": 6,
			"outOrIn": "IN",
			"id": "baf16554c33047925c6be9ec6fd21bbc"
		},
        {
			"algorithmInstanceId": "ff43550af80aac096de71d8e114e0d62",
			"sceneId": "8ca71723ce952bf9f91e30ddcec36b70",
			"value": 3,
			"algorithmId": "f4edaafc4bc2d58ac939794d2126c584",
			"key": "remove_noise_kernel_size",
			"label": "滤波去噪内核大小",
			"dataType": "INTEGER",
			"defaultValue": 3,
			"constraints": {
				"min": 0,
				"max": 10,
				"precision": 1,
				"required": True
			},
			"drawToOsd": False,
			"sortNo": 7,
			"outOrIn": "IN",
			"id": "baf16554c33047925c6be9ec6fd21bbc"
		},
        {
			"algorithmInstanceId": "ff43550af80aac096de71d8e114e0d62",
			"sceneId": "8ca71723ce952bf9f91e30ddcec36b70",
			"value": 50,
			"algorithmId": "f4edaafc4bc2d58ac939794d2126c584",
			"key": "ash_threshold",
			"label": "蒙灰阈值调整",
			"dataType": "INTEGER",
			"defaultValue": 50,
			"constraints": {
				"min": 10,
				"max": 100,
				"precision": 5,
				"required": True
			},
			"drawToOsd": False,
			"sortNo": 8,
			"outOrIn": "IN",
			"id": "baf16554c33047925c6be9ec6fd21bbc"
		},
        {
			"algorithmInstanceId": "ff43550af80aac096de71d8e114e0d62",
			"sceneId": "8ca71723ce952bf9f91e30ddcec36b70",
			"value": True,
			"algorithmId": "f4edaafc4bc2d58ac939794d2126c584",
			"key": "show_up_part_fireline",
			"label": "是否展示上边界分割效果",
			"dataType": "BOOLEAN",
			"defaultValue": True,
            "constraints": {
				"required": True
			},
			"drawToOsd": False,
			"sortNo": 9,
			"outOrIn": "IN",
			"id": "baf16554c33047925c6be9ec6fd21bbc"
		}
	]
    }

    inputs = AlgorithmInput()

    images = request_param['images']
    # new_image_arr = []
    # for img_path in images:
    #     # 下载图片到本地
    #     # local_path = MinioUtil.download(img_path)
    #     new_image_arr.append(img_path)
    inputs['images'] = images

    roi = request_param['roi']
    inputs['roi'] = roi

    inputParam = request_param['inputParam']
    inputs['inputParam'] = inputParam

    method_code = request_param['code']
    user_or_system = request_param['userOrSystem']

    # 输入参数校验
    from backend_common.maths.algorithm.base.parameter_check import ParameterCheck
    param_check = ParameterCheck()
    param_check.do_check_param(inputs)

    # 加载算法类
    custom_algorithm_instance = load_algorithm_super(method_code, user_or_system, inputs)

    assert custom_algorithm_instance is not None, "无法加载CV算法实例!"

    # 算法执行
    isSuccess, output, osdInfo = custom_algorithm_instance.perform()
    print(isSuccess, output, osdInfo)
    outputs = AlgorithmOutput(isSuccess, output, osdInfo)
    print(f"outputs {str(outputs)}")
    return outputs


def load_algorithm_super(algorithm_name, suer_or_system, inputs: AlgorithmInput):
    # 算法块唯一标识名,通常需与算法块文件夹名称一致，比如 liquid_level
    module_name = f".{algorithm_name}.ab_{algorithm_name}"
    target_clazz_name = f"{to_camel_case(algorithm_name, '_')}Reader"

    base_path = ALGORITHM_CV_SYSTEM_INNER_PATH
    if suer_or_system == "user_define":
        base_path = ALGORITHM_CV_USER_DEFINE_PATH

    try:
        print("ALGORITHM_CV_USER_DEFINE_PATH", base_path)
        print("module_name", module_name)
        print("target_clazz_name", target_clazz_name)

        M = importlib.import_module(module_name, base_path)
        # 找到对应的类
        target_clazz_ = getattr(M, target_clazz_name)
    except ModuleNotFoundError:
        raise InspectionException(
            f"Auto-load module '{module_name}' from '{base_path}' failure, please check it manually")

    # 类实例创建
    algorithm_instance: AlgorithmBase = target_clazz_(inputs)
    return algorithm_instance


@pytest.fixture
def primary_detect():
    return do_itself()

def test_primary(primary_detect):
    assert primary_detect.isSuccess is True

