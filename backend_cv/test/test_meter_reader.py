#!/usr/bin/env python

"""Tests for `IndicatorFlashReader` algorithm."""
import importlib
import os

import cv2
import pytest

from backend_common.constants.alarm import AlarmRule, ExceedLimitAlarmRule
from backend_common.constants.constant import AlgorithmClassifyEnum, AlgorithmStrategyEnum, AlgorithmOutputTypeEnum
from backend_common.constants.math_constants import ALGORITHM_CV_USER_DEFINE_PATH, ALGORITHM_CV_SYSTEM_INNER_PATH
from backend_common.exceptions.inspection_exception import InspectionException
from backend_common.maths.algorithm.base.algorithm_base import AlgorithmOutput, AlgorithmBase, AlgorithmSettings, \
    AlgorithmInput
from backend_common.utils.util import to_camel_case

clazz_name_prefix = "MeterReader"


def do_itself(algorithm_index, classify, strategy, output_type, alarm_rule: AlarmRule = None):
    settings = AlgorithmSettings(classify, strategy, alarm_rules=[alarm_rule])

    algorithm_name = "meter"
    # TODO 默认采样频率 500ms + 采样时长 5s, 即 10 张图
    # inputs = AlgorithmInput({"images": ["./resources/input-img/meter/meter2.jpg"],
    #                          "meterList": [
    #                              {
    #                                  "roiSquare": {
    #                                      "center": [1118, 614],
    #                                      "radius": 204
    #                                  },
    #                                  "zeroPoint": [1006, 489],
    #                                  "startPoint": [997, 694],
    #                                  "endPoint": [1244, 664],
    #                                  "range": [-30, 50]
    #                              }
    #                          ]})

    # inputs = AlgorithmInput({
    #                         "targetSceneImage": "xxxxx",
    #                         "images": ["./resources/input-img/meter/meter3.jpg"],
    #                          "meterList": [
    #                              {
    #                                  "roiSquare": {
    #                                      "center": [1078, 413],
    #                                      "radius": 64
    #                                  },
    #                                  "zeroPoint": [1010, 437],
    #                                  "startPoint": [1086, 479],
    #                                  "endPoint": [1149, 418],
    #                                  "range": [-20, 50],
    #                                  "color_picker": [64, 29 ,23]
    #                              }
    #                          ]})

    inputs = AlgorithmInput({
        "targetSceneImage": "xxxxx",
        "images": ["./resources/input-img/meter/meter6.jpg"],
        "meterList": [
            {
                "roiSquare": {
                    "center": [784, 558],
                    "radius": 164
                },
                "zeroPoint": [640, 366],
                "startPoint": [577, 691],
                "endPoint": [998, 681],
                "range": [-30, 50],
                "color_picker": [90, 93, 84]
            }
        ]})

    # inputs = AlgorithmInput({"images": ["./resources/input-img/meter/meter4.jpg"],
    #                          "meterList": [
    #                              {
    #                                  "roiSquare": {
    #                                      "center": [786, 495],
    #                                      "radius": 141
    #                                  },
    #                                  "zeroPoint": [703, 395],
    #                                  "startPoint": [669, 573],
    #                                  "endPoint": [909, 559],
    #                                  "range": [-30, 50]
    #                              }
    #                          ]})

    sup: AlgorithmBase
    sup = load_algorithm_super(algorithm_name, "system_inner", inputs)
    target_instance = sup.determine_algorithm_instance(settings, algorithm_index)

    assert target_instance is not None
    ret, val, points, alarms = target_instance.perform(settings)
    print(f"结果 {ret, val, points, alarms}")

    outputs = AlgorithmOutput(ret, val, points, alarms)
    print(f"outputs {str(outputs)}")

    # 可选操作
    ret_images = target_instance.gen_result_img()
    if ret_images is not None:
        for idx, img in enumerate(ret_images):
            cv2.imwrite(os.getcwd() + f"/40_result_{idx}.jpg", img)

    return outputs


def load_algorithm_super(algorithm_name, a_type, inputs: AlgorithmInput):
    # self._name  算法块唯一标识名,通常需与算法块文件夹名称一致，比如 liquid_level
    module_name = f".{algorithm_name}.ab_{algorithm_name}"
    target_clazz_name = f"{to_camel_case(algorithm_name, '_')}Reader"

    base_path = ALGORITHM_CV_SYSTEM_INNER_PATH
    if a_type == "user_define":
        base_path = ALGORITHM_CV_USER_DEFINE_PATH

    try:
        M = importlib.import_module(module_name, base_path)
        target_clazz_ = getattr(M, target_clazz_name)
    except ModuleNotFoundError:
        raise InspectionException(
            f"Auto-load module '{module_name}' from '{base_path}' failure, please check it manually")

    algorithm_instance: AlgorithmBase = target_clazz_(inputs, -1)
    return algorithm_instance


@pytest.fixture
def primary_detect():
    algorithm_index = 1
    classify = AlgorithmClassifyEnum.Primary
    strategy = AlgorithmStrategyEnum.Main
    output_type = AlgorithmOutputTypeEnum.AlarmLevel

    # 报警条件（可选）
    alarm_rule = ExceedLimitAlarmRule(17, 0, 0, 0, hh_is_bind=True)
    return do_itself(algorithm_index, classify, strategy, output_type, alarm_rule=alarm_rule)


def test_primary(primary_detect):
    assert primary_detect.ret is True


# @pytest.fixture
# def json2_param():
#     filename = "E:\\workspace\\PycharmProjects\\iapp\\iapp_v0\\iapp_v0\\algorithm\\liquid_level\\schema.json"
#     return AlgorithmBase.json_schema_2_algorithm_params(filename)
#
#
# def test_json_reader(json2_param):
#     assert len(json2_param) > 0

def test_img_click():
    # mouse callback function
    def draw_circle(event, x, y, flags, param):
        if event == cv2.EVENT_LBUTTONDOWN:
            cv2.circle(img, (x, y), 100, (255, 0, 0), -1)
            print(f"{x},{y}")

    img = cv2.imread("resources/input-img/meter/meter6.jpg")

    cv2.imshow("img", img)
    cv2.setMouseCallback("img", draw_circle)
    cv2.waitKey()


def test_hsv():
    # 读取图像，支持 bmp、jpg、png、tiff 等常用格式
    # 第二个参数是通道数和位深的参数，有四种选择，参考https://www.cnblogs.com/goushibao/p/6671079.html
    # 1彩色2灰度
    img = cv2.imread("./resources/input-img/indicator_flash/mt4.jpeg", 1)

    title = "image"
    # 创建窗口并显示图像
    cv2.namedWindow(title, cv2.WINDOW_NORMAL)
    cv2.imshow(title, img)
    # 采集转灰度
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)

    def mouse_click(event, x, y, flags, param):
        if event == cv2.EVENT_MOUSEMOVE:
            # cv2.destroyAllWindows()
            # print(event, x, y, flags, param)
            print("像素值", x, y, gray[x][y])
            # print("RGB", img[x][y])
            img1 = img.copy()
            cv2.putText(img1, "Gray:" + str(gray[x][y]), (50, 150), cv2.FONT_HERSHEY_COMPLEX, 1, (0, 255, 0), 1)
            cv2.putText(img1, "RGB:" + str(img[x][y]), (50, 190), cv2.FONT_HERSHEY_COMPLEX, 1, (0, 255, 0), 1)
            cv2.putText(img1, "HSV:" + str(hsv[x][y]), (50, 230), cv2.FONT_HERSHEY_COMPLEX, 1, (0, 255, 0), 1)
            cv2.putText(img1, "Asix:" + str([x, y]), (50, 270), cv2.FONT_HERSHEY_COMPLEX, 1, (0, 255, 0), 1)
            cv2.imshow(title, img1)

    cv2.setMouseCallback(title, mouse_click)
    cv2.waitKey(0)
    # 释放窗口
    cv2.destroyAllWindows()
