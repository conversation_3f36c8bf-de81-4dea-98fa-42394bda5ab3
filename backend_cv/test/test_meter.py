#!/usr/bin/env python

"""Tests for `IndicatorFlashReader` algorithm."""
import importlib
import os
from typing import List

import cv2
import pytest

# from backend_common.constants.alarm import AlarmRule, ExceedLimitAlarmRule
# from backend_common.constants.constant import AlgorithmClassifyEnum, AlgorithmStrategyEnum, AlgorithmOutputTypeEnum
from backend_common.constants.math_constants import ALGORITHM_CV_USER_DEFINE_PATH, ALGORITHM_CV_SYSTEM_INNER_PATH
from backend_common.exceptions.inspection_exception import InspectionException
from backend_common.maths.algorithm.base.algorithm_base import AlgorithmOutput, AlgorithmBase, AlgorithmInput
from backend_common.utils.util import to_camel_case
from backend_common.utils import MinioUtil

# clazz_name_prefix = "IndicatorFlashReader"


def do_itself():
    # settings = AlgorithmSettings(classify, strategy, alarm_rules=alarm_rules)

    # algorithm_name = "indicator_flash"
    # TODO 默认采样频率 500ms + 采样时长 5s, 即 10 张图片


    request_param = {
	"code": "meter",
	"images": [
		# ".\\test\\resources\\input-img\\meter\\202507081113557470.jpg"
        "./test/resources/input-img/meter/202507081113557470.jpg"
	],
	"roi": {
		"dataType": "SQUARE",
		"constraints": {
			"required": False
		}
	},
	"sceneImage": "http://minio:9001/inspection/SCENE/202507081110395482.jpg",
	"userOrSystem": "system_inner",
	"inputParam": [
		{
			"algorithmInstanceId": "ec641e70e9cb52fbcc79a1ce98f5550e",
			"sceneId": "1dd479b1a1d820bb3d736a5b8e88e8c9",
			"value": {
				"center": {
					"x": 860,
					"y": 571
				},
				"radius": 123
			},
			"algorithmId": "d918cca9e34d3f420dc0ab4ba8d6ea2d",
			"key": "roiSquare",
			"label": "仪表区域",
			"dataType": "CIRCLE",
			"constraints": {
				"required": True
			},
			"drawToOsd": True,
			"sortNo": 0,
			"outOrIn": "IN",
			"id": "eea172e9904f369dd5fcfe42a238d1f2"
		},
		{
			"algorithmInstanceId": "ec641e70e9cb52fbcc79a1ce98f5550e",
			"sceneId": "1dd479b1a1d820bb3d736a5b8e88e8c9",
			"value": [
				42,
				36,
				36
			],
			"algorithmId": "d918cca9e34d3f420dc0ab4ba8d6ea2d",
			"key": "colorPicker",
			"label": "指针颜色",
			"dataType": "RGB",
			"defaultValue": [
				0,
				0,
				0
			],
			"constraints": {
				"required": True,
				"minLength": 3
			},
			"drawToOsd": False,
			"sortNo": 1,
			"outOrIn": "IN",
			"id": "74e3e7108de975d98049c828afe65ea5"
		},
		{
			"algorithmInstanceId": "ec641e70e9cb52fbcc79a1ce98f5550e",
			"sceneId": "1dd479b1a1d820bb3d736a5b8e88e8c9",
			"value": 55,
			"algorithmId": "d918cca9e34d3f420dc0ab4ba8d6ea2d",
			"key": "colorThreshold",
			"label": "颜色偏差",
			"dataType": "INTEGER",
			"defaultValue": "55",
			"constraints": {
				"required": True,
				"max": 255,
				"min": 0,
				"precision": 1
			},
			"drawToOsd": False,
			"sortNo": 2,
			"outOrIn": "IN",
			"id": "065a88c09e87138173ca4d619f675365"
		},
		{
			"algorithmInstanceId": "ec641e70e9cb52fbcc79a1ce98f5550e",
			"sceneId": "1dd479b1a1d820bb3d736a5b8e88e8c9",
			"value": {
				"x": 793,
				"y": 471
			},
			"algorithmId": "d918cca9e34d3f420dc0ab4ba8d6ea2d",
			"key": "zeroPoint",
			"label": "零点位置",
			"dataType": "POINT",
			"constraints": {
				"required": True
			},
			"drawToOsd": True,
			"sortNo": 3,
			"outOrIn": "IN",
			"id": "1c6fe9ceb5ce50c99bb5a2eca406d850"
		},
		{
			"algorithmInstanceId": "ec641e70e9cb52fbcc79a1ce98f5550e",
			"sceneId": "1dd479b1a1d820bb3d736a5b8e88e8c9",
			"value": {
				"x": 752,
				"y": 631
			},
			"algorithmId": "d918cca9e34d3f420dc0ab4ba8d6ea2d",
			"key": "startPoint",
			"label": "起点位置",
			"dataType": "POINT",
			"constraints": {
				"required": True
			},
			"drawToOsd": True,
			"sortNo": 4,
			"outOrIn": "IN",
			"id": "35a1337f6e3ca5252db3ecf5b0a1070c"
		},
		{
			"algorithmInstanceId": "ec641e70e9cb52fbcc79a1ce98f5550e",
			"sceneId": "1dd479b1a1d820bb3d736a5b8e88e8c9",
			"value": {
				"x": 968,
				"y": 631
			},
			"algorithmId": "d918cca9e34d3f420dc0ab4ba8d6ea2d",
			"key": "endPoint",
			"label": "终点位置",
			"dataType": "POINT",
			"constraints": {
				"required": True
			},
			"drawToOsd": True,
			"sortNo": 5,
			"outOrIn": "IN",
			"id": "6125e392f996a1de02470eb449e55565"
		},
		{
			"algorithmInstanceId": "ec641e70e9cb52fbcc79a1ce98f5550e",
			"sceneId": "1dd479b1a1d820bb3d736a5b8e88e8c9",
			"value": {
				"start": -30,
				"end": 50
			},
			"algorithmId": "d918cca9e34d3f420dc0ab4ba8d6ea2d",
			"key": "range",
			"label": "量程",
			"dataType": "RANGE",
			"defaultValue": {
				"start": -30,
				"end": 50
			},
			"constraints": {
				"required": True,
				"max": 999999,
				"min": -999999,
				"precision": 0.001
			},
			"drawToOsd": False,
			"sortNo": 6,
			"outOrIn": "IN",
			"id": "03e33d9b6dbcdb79610498b83efda36b"
		},
		{
			"algorithmInstanceId": "ec641e70e9cb52fbcc79a1ce98f5550e",
			"sceneId": "1dd479b1a1d820bb3d736a5b8e88e8c9",
			"value": 1,
			"algorithmId": "d918cca9e34d3f420dc0ab4ba8d6ea2d",
			"key": "resultPrecision",
			"label": "结果精度",
			"dataType": "INTEGER",
			"defaultValue": "2",
			"constraints": {
				"required": False,
				"max": 5,
				"min": 0,
				"precision": 1
			},
			"drawToOsd": False,
			"sortNo": 7,
			"outOrIn": "IN",
			"id": "d6d8774b008dc444815c1bbc13c490aa"
		},
		{
			"algorithmInstanceId": "ec641e70e9cb52fbcc79a1ce98f5550e",
			"sceneId": "1dd479b1a1d820bb3d736a5b8e88e8c9",
			"value": "",
			"algorithmId": "d918cca9e34d3f420dc0ab4ba8d6ea2d",
			"key": "unit",
			"label": "单位",
			"dataType": "STRING",
			"constraints": {
				"required": False,
				"regexPattern": "^[A-Za-z0-9_]+$",
				"maxLength": 255
			},
			"drawToOsd": False,
			"sortNo": 8,
			"outOrIn": "IN"
		},
		{
			"algorithmInstanceId": "ec641e70e9cb52fbcc79a1ce98f5550e",
			"sceneId": "1dd479b1a1d820bb3d736a5b8e88e8c9",
			"value": "",
			"algorithmId": "d918cca9e34d3f420dc0ab4ba8d6ea2d",
			"key": "resultDes",
			"label": "结果描述",
			"dataType": "STRING",
			"constraints": {
				"required": False
			},
			"drawToOsd": False,
			"sortNo": 9,
			"outOrIn": "IN"
		}
	]
}
    inputs = AlgorithmInput()

    images = request_param['images']
    # new_image_arr = []
    # for img_path in images:
    #     # 下载图片到本地
    #     # local_path = MinioUtil.download(img_path)
    #     new_image_arr.append(img_path)
    inputs['images'] = images

    roi = request_param['roi']
    inputs['roi'] = roi

    inputParam = request_param['inputParam']
    inputs['inputParam'] = inputParam

    method_code = request_param['code']
    user_or_system = request_param['userOrSystem']

    # 输入参数校验
    from backend_common.maths.algorithm.base.parameter_check import ParameterCheck
    param_check = ParameterCheck()
    param_check.do_check_param(inputs)

    # 加载算法类
    custom_algorithm_instance = load_algorithm_super(method_code, user_or_system, inputs)

    assert custom_algorithm_instance is not None, "无法加载CV算法实例!"

    # 算法执行
    isSuccess, output, osdInfo = custom_algorithm_instance.perform()
    print(isSuccess, output, osdInfo)
    outputs = AlgorithmOutput(isSuccess, output, osdInfo)
    print(f"outputs {str(outputs)}")
    return outputs


def load_algorithm_super(algorithm_name, suer_or_system, inputs: AlgorithmInput):
    # 算法块唯一标识名,通常需与算法块文件夹名称一致，比如 liquid_level
    module_name = f".{algorithm_name}.ab_{algorithm_name}"
    target_clazz_name = f"{to_camel_case(algorithm_name, '_')}Reader"

    base_path = ALGORITHM_CV_SYSTEM_INNER_PATH
    if suer_or_system == "user_define":
        base_path = ALGORITHM_CV_USER_DEFINE_PATH

    try:
        print("ALGORITHM_CV_USER_DEFINE_PATH", base_path)
        print("module_name", module_name)
        print("target_clazz_name", target_clazz_name)

        M = importlib.import_module(module_name, base_path)
        # 找到对应的类
        target_clazz_ = getattr(M, target_clazz_name)
    except ModuleNotFoundError:
        raise InspectionException(
            f"Auto-load module '{module_name}' from '{base_path}' failure, please check it manually")

    # 类实例创建
    algorithm_instance: AlgorithmBase = target_clazz_(inputs)
    return algorithm_instance


@pytest.fixture
def primary_detect():
    return do_itself()

def test_primary(primary_detect):
    assert primary_detect.isSuccess is True

