FROM ***************:5000/inspection-backend-base:1.0.1

#代码添加到home文件夹
ADD backend_opt /home/<USER>
ADD backend_common /home/<USER>/backend_common

# 设置home文件夹是工作目录
WORKDIR /home/<USER>

EXPOSE 6400

CMD ["uvicorn", "application_opt:app", "--host", "0.0.0.0", "--port", "6400"]
#CMD ["python3", "application_opt.py"]
# docker build -f Dockerfile-OPT -t ***************:5000/inspection-backend-opt:1.0.0 .
# docker build -f Dockerfile-OPT -t ***************:5000/inspection-backend-opt:1.0.0-test .
# docker run --name inspection-backend-opt --restart=always \
#-v /home/<USER>/intelligent_inspection/file-server/images:/project/file-server/images/ \
#-p 6400:6400 -d  inspection-backend-opt:1.0.0

# docker run --name inspection-backend-opt --restart=always -v E:/project/file-server/images:/project/file-server/images/ -p 6400:6400 -d  ***************:5000/inspection-backend-opt:1.0.0