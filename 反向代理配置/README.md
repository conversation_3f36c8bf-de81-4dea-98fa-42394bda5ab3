# 反向代理配置方案

#### 1.宿主机创建相关目录

- /home/<USER>/intelligent_inspection/py-server-gateway/conf：存放nginx配置文件
- /home/<USER>/intelligent_inspection/py-server-gateway/logs：存放nginx日志文件
  路径按实际情况修改，后面启动容器需要映射到容器内部

#### 2.拷贝配置文件

将conf下面的全部文件夹复制到/home/<USER>/intelligent_inspection/py-server-gateway/conf下

#### 3.启动nginx

```shell
docker run --restart=always  --name py-server-gateway -p 6200:6200 -p 6300:6300 -p 6400:6400 -v /home/<USER>/intelligent_inspection/py-server-gateway/conf:/etc/nginx  -v /home/<USER>/intelligent_inspection/py-server-gateway/logs:/var/log/nginx -v /etc/localtime:/etc/localtime:ro -d nginx:1.10.2
```
