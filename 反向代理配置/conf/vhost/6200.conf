# 代理服务器
# 设置服务器组
upstream cluster1 {
	server ***************:6201;
	server ***************:6202;
	server ***************:6203;
}
server {
    listen 6200;
    server_name localhost;
	charset utf-8;
	
   location /block-upload {
		   proxy_pass http://***************:6201;
		   mirror /block-node2;
       mirror /block-node3;
		}
   
   location /block-node2 {
			internal;
			proxy_pass http://***************:6202$request_uri;
		}
   
    location /block-node3 {
			internal;
			proxy_pass http://***************:6203$request_uri;
		}
   
    location / {
        proxy_pass http://cluster1/;
    }
}