# 代理服务器
# 设置服务器组
upstream cluster2 {
	server ***************:6301;
	server ***************:6302;
	server ***************:6303;
	server ***************:6304;
	server ***************:6305;
	server ***************:6306;
	server ***************:6307;
	server ***************:6308;
	server ***************:6309;
	server ***************:6310;
	server ***************:6311;
	server ***************:6312;
	server ***************:6313;
	server ***************:6314;
	server ***************:6315;
	server ***************:6316;
	server ***************:6317;
	server ***************:6318;
	server ***************:6319;
	server ***************:6320;
}
server {
    listen 6300;
    server_name localhost;
	charset utf-8;

    location /update-cls-gallery|/block-upload {
	    proxy_pass http://***************:6301;
        mirror /node2;
        mirror /node3;
        mirror /node4;
        mirror /node5;
        mirror /node6;
        mirror /node7;
        mirror /node8;
        mirror /node9;
        mirror /node10;
        mirror /node11;
        mirror /node12;
        mirror /node13;
        mirror /node14;
        mirror /node15;
        mirror /node16;
        mirror /node17;
        mirror /node18;
        mirror /node19;
        mirror /node20;
	}

	location /node2 {
		internal;
		proxy_pass http://***************:6302$request_uri;
	}

    location /node3 {
		internal;
		proxy_pass http://***************:6303$request_uri;
	}

	location /node4 {
		internal;
		proxy_pass http://***************:6304$request_uri;
	}

	location /node5 {
		internal;
		proxy_pass http://***************:6305$request_uri;
	}

	location /node6 {
		internal;
		proxy_pass http://***************:6306$request_uri;
	}

	location /node7 {
		internal;
		proxy_pass http://***************:6307$request_uri;
	}

	location /node8 {
		internal;
		proxy_pass http://***************:6308$request_uri;
	}

	location /node9 {
		internal;
		proxy_pass http://***************:6309$request_uri;
	}

    location /node10 {
		internal;
		proxy_pass http://***************:6310$request_uri;
	}

	location /node11 {
		internal;
		proxy_pass http://***************:6311$request_uri;
	}

	location /node12 {
		internal;
		proxy_pass http://***************:6312$request_uri;
	}

	location /node13 {
		internal;
		proxy_pass http://***************:6313$request_uri;
	}

	location /node14 {
		internal;
		proxy_pass http://***************:6314$request_uri;
	}

	location /node15 {
		internal;
		proxy_pass http://***************:6315$request_uri;
	}

	location /node16 {
		internal;
		proxy_pass http://***************:6316$request_uri;
	}

	location /node17 {
		internal;
		proxy_pass http://***************:6317$request_uri;
	}

	location /node18 {
		internal;
		proxy_pass http://***************:6318$request_uri;
	}

	location /node19 {
		internal;
		proxy_pass http://***************:6319$request_uri;
	}

	location /node20 {
		internal;
		proxy_pass http://***************:6320$request_uri;
	}

    location / {
        proxy_pass http://cluster2/;
    }
}