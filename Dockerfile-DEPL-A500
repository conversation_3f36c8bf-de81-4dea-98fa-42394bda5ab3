# arm nnrt 500小站基础镜像

ARG ARCHTECTURE
FROM ************:5000/backend-depl-cann-base:${ARCHTECTURE}1.0.0-nnrt-a500

ADD backend_depl /home/<USER>
ADD backend_common /home/<USER>/backend_common

# 设置home文件夹是工作目录
WORKDIR /home/<USER>

EXPOSE 6300

RUN sed -i 's/\r$//' /home/<USER>/run_nnrt_a500.sh && chmod +x /home/<USER>/run_nnrt_a500.sh


CMD sh run_nnrt_a500.sh

# docker build -f Dockerfile-DEPL -t  ************:5000/inspection-backend-depl:arm64-V1.0.0_LTSP7_2025.8.30 .

# 镜像运行
# docker run -it -u root --pid=host \
# --device=/dev/svm0 \
# --device /dev/davinci0:/dev/davinci0 \
# --device /dev/davinci_manager:/dev/davinci_manager \
# --device=/dev/dvpp_cmdlist:/dev/dvpp_cmdlist \
# -v  /usr/local/sbin/npu-smi:/usr/local/sbin/npu-smi \
# -v /usr/local/Ascend/driver/lib64:/usr/local/Ascend/driver/lib64 \
# -v /etc/sys_version.conf:/etc/sys_version.conf \
# -v  /var/dmp_daemon:/var/dmp_daemon \
# -v /etc/hdcBasic.cfg:/etc/hdcBasic.cfg \
# -v /usr/lib64/libaicpu_processer.so:/usr/lib64/libaicpu_processer.so \
# -v /usr/lib64/libaicpu_prof.so:/usr/lib64/libaicpu_prof.so \
# -v /usr/lib64/libaicpu_sharder.so:/usr/lib64/libaicpu_sharder.so \
# -v /usr/lib64/libadump.so:/usr/lib64/libadump.so \
# -v /usr/lib64/libtsd_eventclient.so:/usr/lib64/libtsd_eventclient.so \
# -v /usr/lib64/libaicpu_scheduler.so:/usr/lib64/libaicpu_scheduler.so \
# -v /usr/lib64/libcrypto.so.1.1.1m:/usr/lib64/libcrypto.so.1.1 \
# -v /usr/lib64/libyaml-0.so.2.0.9:/usr/lib64/libyaml-0.so.2 \
# -v /usr/lib64/libdcmi.so:/usr/lib64/libdcmi.so \
# -v /usr/lib64/libmpi_dvpp_adapter.so:/usr/lib64/libmpi_dvpp_adapter.so \
# -v  /usr/lib64/aicpu_kernels/:/usr/lib64/aicpu_kernels/ \
# -v  /usr/lib64/libstackcore.so:/usr/lib64/libstackcore.so \
# -v  /var/slogd:/var/slogd \
# [docker_image_name]