# 算法架构统一后新版基础镜像
ARG ARCHTECTURE
FROM 192.168.1.16:5000/inspection-backend-base:${ARCHTECTURE}torch-onnx-py310-0.4

#代码添加到home文件夹
ADD backend_depl /home/<USER>
ADD backend_common /home/<USER>/backend_common


# 设置home文件夹是工作目录
WORKDIR /home/<USER>

EXPOSE 6300

# ENTRYPOINT ["python3","application_depl.py"]
CMD sh run.sh

# docker build -f Dockerfile-DEPL -t  192.168.253.216:5000/inspection-backend-depl:sprint3 .