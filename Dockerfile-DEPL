# arm nnrt基础镜像

ARG ARCHTECTURE
FROM 192.168.1.16:5000/backend-depl-cann-base:${ARCHTECTURE}1.0.0-nnrt

ADD backend_depl /home/<USER>
ADD backend_common /home/<USER>/backend_common

# 设置home文件夹是工作目录
WORKDIR /home/<USER>

EXPOSE 6300

RUN sed -i 's/\r$//' /home/<USER>/run_nnrt.sh && chmod +x /home/<USER>/run_nnrt.sh


CMD sh run_nnrt.sh

# docker build -f Dockerfile-DEPL -t  192.168.1.16:5000/inspection-backend-depl:arm64-V1.0.0_LTSP7_2025.8.30 .

# 镜像运行
#  docker run -it --entrypoint='bash' -p 6301:6300
#  --device /dev/davinci0:/dev/davinci0
#  --device /dev/davinci_manager
#  --device /dev/devmm_svm
#  --device /dev/hisi_hdc
#  -v /usr/local/dcmi:/usr/local/dcmi
#  -v /usr/local/bin/npu-smi:/usr/local/bin/npu-smi
#  -v /usr/local/Ascend/driver/lib64/common:/usr/local/Ascend/driver/lib64/common
#  -v /usr/local/Ascend/driver/lib64/driver:/usr/local/Ascend/driver/lib64/driver
#  -v /etc/ascend_install.info:/etc/ascend_install.info
#  -v /usr/local/Ascend/driver/version.info:/usr/local/Ascend/driver/version.info
# [docker_image_name]