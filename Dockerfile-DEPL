FROM ***************:5000/inspection-backend-tensorrt:1.0.5

#代码添加到home文件夹
ADD backend_depl /home/<USER>
ADD backend_common /home/<USER>/backend_common

# 设置home文件夹是工作目录
WORKDIR /home/<USER>

EXPOSE 6300

# $WEB_CONCURRENCY eq --workers
#CMD ["uvicorn", "application_depl:app", "--host", "0.0.0.0", "--port", "6300"]
CMD ["python3", "application_depl.py"]

# docker build -f Dockerfile-DEPL -t  ***************:5000/inspection-backend-depl:3090 .
# docker build -f Dockerfile-DEPL -t  ***************:5000/inspection-backend-depl:T4 .
# docker build -f Dockerfile-DEPL -t  ***************:5000/inspection-backend-depl:T4-test .
#docker run --name inspection-backend-depl --restart=always \
#-v /home/<USER>/intelligent_inspection/file-server/images:/project/file-server/images/ \
#-v /home/<USER>/intelligent_inspection/file-server/voice/:/voice/ \
#--gpus all --shm-size=8g --ulimit memlock=-1 -p 6300:6300 -d  inspection-backend-depl:1.0.0

#docker run --name inspection-backend-depl --restart=always -v E:/project/file-server/images:/project/file-server/images/ --gpus all --shm-size=8g --ulimit memlock=-1 -p 6300:6300 -d   ***************:5000/inspection-backend-depl:1.0.0
