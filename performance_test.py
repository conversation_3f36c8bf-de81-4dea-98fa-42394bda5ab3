#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MinIO图片下载性能对比测试
对比三种方式的耗时：
1. 下载到本地磁盘再用cv2.imread读取
2. 直接从MinIO下载到内存并转为cv2格式
3. 通过Redis Stream传输图片数据并转为cv2格式
"""

import time
import os
import cv2
import numpy as np
import requests
from pathlib import Path
import tempfile
import redis
import base64
import json

# 测试URL
TEST_URL = "http://************:18010/inspection/EXECUTE/202507081735583219.JPEG"
LOCAL_TEST_FILE = "test.jpg"

# Redis配置
REDIS_HOST = "localhost"  # 根据实际情况修改
REDIS_PORT = 6379
REDIS_DB = 0
REDIS_STREAM_NAME = "image_stream"

def method1_download_then_read():
    """
    方法1: 下载到本地磁盘，然后用cv2.imread读取
    """
    start_time = time.time()
    
    try:
        # 下载文件到本地
        response = requests.get(TEST_URL, timeout=30)
        response.raise_for_status()
        
        # 保存到本地文件
        with open(LOCAL_TEST_FILE, 'wb') as f:
            f.write(response.content)
        
        download_time = time.time()
        
        # 使用cv2.imread读取
        img = cv2.imread(LOCAL_TEST_FILE)
        
        read_time = time.time()
        
        # 清理临时文件
        if os.path.exists(LOCAL_TEST_FILE):
            os.remove(LOCAL_TEST_FILE)
        
        total_time = read_time - start_time
        download_duration = download_time - start_time
        read_duration = read_time - download_time
        
        return {
            'success': True,
            'total_time': total_time,
            'download_time': download_duration,
            'read_time': read_duration,
            'image_shape': img.shape if img is not None else None,
            'method': '下载到本地磁盘再读取'
        }
        
    except Exception as e:
        return {
            'success': False,
            'error': str(e),
            'method': '下载到本地磁盘再读取'
        }

def method2_direct_memory_read():
    """
    方法2: 直接下载到内存并转为cv2格式
    """
    start_time = time.time()
    
    try:
        # 直接下载到内存
        response = requests.get(TEST_URL, timeout=30)
        response.raise_for_status()
        
        download_time = time.time()
        
        # 将字节数据转换为numpy数组
        img_array = np.frombuffer(response.content, np.uint8)
        
        # 使用cv2.imdecode解码图片
        img = cv2.imdecode(img_array, cv2.IMREAD_COLOR)
        
        decode_time = time.time()
        
        total_time = decode_time - start_time
        download_duration = download_time - start_time
        decode_duration = decode_time - download_time
        
        return {
            'success': True,
            'total_time': total_time,
            'download_time': download_duration,
            'decode_time': decode_duration,
            'image_shape': img.shape if img is not None else None,
            'method': '直接内存解码'
        }
        
    except Exception as e:
        return {
            'success': False,
            'error': str(e),
            'method': '直接内存解码'
        }

def method3_redis_stream(r):
    """
    方法3: 通过Redis Stream传输图片数据并转为cv2格式
    """
    try:
        # 先下载图片数据并存入Redis Stream
        response = requests.get(TEST_URL, timeout=30)
        response.raise_for_status()
        
        # 将图片数据编码为base64并存入Redis Stream
        img_data_b64 = base64.b64encode(response.content).decode('utf-8')
        
        # 添加到Redis Stream
        stream_id = r.xadd(REDIS_STREAM_NAME, {
            'image_data': img_data_b64,
            'timestamp': time.time()
        })
        
        # 开始计时（从Redis Stream接收到cv2处理完成）
        start_time = time.time()

        # 使用xrange获取最新的数据（模拟实际使用场景）
        # 获取从刚添加的消息ID开始的最新条目
        messages = r.xrange(REDIS_STREAM_NAME, min=stream_id, max='+', count=1)
        
        if not messages:
            return {
                'success': False,
                'error': 'Redis Stream读取失败',
                'method': 'Redis Stream传输'
            }

        read_time = time.time()

        # 解析消息
        message_id, fields = messages[0]
        
        # 解码图片数据
        img_data_b64 = fields[b'image_data'].decode('utf-8')
        img_data = base64.b64decode(img_data_b64)
        
        # 转换为numpy数组
        img_array = np.frombuffer(img_data, np.uint8)
        
        # 使用cv2.imdecode解码图片
        img = cv2.imdecode(img_array, cv2.IMREAD_COLOR)
        
        decode_time = time.time()
        
        # 清理Redis Stream中的测试数据
        r.xdel(REDIS_STREAM_NAME, stream_id)
        
        total_time = decode_time - start_time
        read_duration = read_time - start_time
        decode_duration = decode_time - read_time
        
        return {
            'success': True,
            'total_time': total_time,
            'read_time': read_duration,
            'decode_time': decode_duration,
            'image_shape': img.shape if img is not None else None,
            'method': 'Redis Stream传输'
        }
        
    except redis.ConnectionError:
        return {
            'success': False,
            'error': f'无法连接到Redis服务器 {REDIS_HOST}:{REDIS_PORT}',
            'method': 'Redis Stream传输'
        }
    except Exception as e:
        return {
            'success': False,
            'error': str(e),
            'method': 'Redis Stream传输'
        }

def get_latest_entries(redis_client, key: str, count: int = 1, last_id: str = None) -> list:
    """
    获取Redis Stream中的最新条目
    使用xrevrange获取最新条目，然后反转以获得时间顺序
    """
    min_id = last_id if last_id else "-"
    entries = redis_client.xrevrange(name=key, max="+", min=min_id, count=count)
    return entries[::-1]  # 反转以获得时间顺序

def method3_redis_stream_advanced():
    """
    方法3增强版: 使用更真实的Redis Stream操作模式
    """
    try:
        # 连接Redis
        r = redis.Redis(host=REDIS_HOST, port=REDIS_PORT, db=REDIS_DB, decode_responses=False)

        # 先下载图片数据并存入Redis Stream（这部分不计入测试时间）
        response = requests.get(TEST_URL, timeout=30)
        response.raise_for_status()

        # 将图片数据编码为base64并存入Redis Stream
        img_data_b64 = base64.b64encode(response.content).decode('utf-8')

        # 添加到Redis Stream
        stream_id = r.xadd(REDIS_STREAM_NAME, {
            'image_data': img_data_b64,
            'timestamp': time.time()
        })

        # 开始计时（从Redis Stream接收到cv2处理完成）
        start_time = time.time()

        # 使用自定义函数获取最新条目
        latest_entries = get_latest_entries(r, REDIS_STREAM_NAME, count=1, last_id=stream_id.decode('utf-8'))

        if not latest_entries:
            return {
                'success': False,
                'error': 'Redis Stream读取失败',
                'method': 'Redis Stream传输(增强版)'
            }

        read_time = time.time()

        # 解析消息
        message_id, fields = latest_entries[0]

        # 解码图片数据
        img_data_b64 = fields[b'image_data'].decode('utf-8')
        img_data = base64.b64decode(img_data_b64)

        # 转换为numpy数组
        img_array = np.frombuffer(img_data, np.uint8)

        # 使用cv2.imdecode解码图片
        img = cv2.imdecode(img_array, cv2.IMREAD_COLOR)

        decode_time = time.time()

        # 清理Redis Stream中的测试数据
        r.xdel(REDIS_STREAM_NAME, stream_id)

        total_time = decode_time - start_time
        read_duration = read_time - start_time
        decode_duration = decode_time - read_time

        return {
            'success': True,
            'total_time': total_time,
            'read_time': read_duration,
            'decode_time': decode_duration,
            'image_shape': img.shape if img is not None else None,
            'method': 'Redis Stream传输(增强版)'
        }

    except redis.ConnectionError:
        return {
            'success': False,
            'error': f'无法连接到Redis服务器 {REDIS_HOST}:{REDIS_PORT}',
            'method': 'Redis Stream传输(增强版)'
        }
    except Exception as e:
        return {
            'success': False,
            'error': str(e),
            'method': 'Redis Stream传输(增强版)'
        }

def test_redis_connectivity():
    """
    测试Redis连接性
    """
    try:
        r = redis.Redis(host=REDIS_HOST, port=REDIS_PORT, db=REDIS_DB)
        r.ping()
        print("✅ Redis连接正常")
        return True
    except redis.ConnectionError:
        print(f"❌ 无法连接到Redis服务器 {REDIS_HOST}:{REDIS_PORT}")
        return False
    except Exception as e:
        print(f"❌ Redis连接测试失败: {e}")
        return False

def run_performance_test(num_runs=5):
    """
    运行性能测试
    """
    print("=" * 60)
    print("MinIO图片下载性能对比测试（包含Redis Stream）")
    print("=" * 60)
    print(f"测试URL: {TEST_URL}")
    print(f"测试次数: {num_runs}")
    print("=" * 60)
    
    method1_results = []
    method2_results = []
    method3_results = []
    method3_advanced_results = []
    
    # 测试方法1
    print("\n🔄 测试方法1: 下载到本地磁盘再用cv2.imread读取")
    for i in range(num_runs):
        print(f"  运行 {i+1}/{num_runs}...", end=" ")
        result = method1_download_then_read()
        if result['success']:
            method1_results.append(result)
            print(f"✅ 总耗时: {result['total_time']:.3f}s")
        else:
            print(f"❌ 失败: {result['error']}")
    
    # 测试方法2
    print("\n🔄 测试方法2: 直接下载到内存并转为cv2格式")
    for i in range(num_runs):
        print(f"  运行 {i+1}/{num_runs}...", end=" ")
        result = method2_direct_memory_read()
        if result['success']:
            method2_results.append(result)
            print(f"✅ 总耗时: {result['total_time']:.3f}s")
        else:
            print(f"❌ 失败: {result['error']}")
    
    # 测试方法3（如果Redis可用）
    if test_redis_connectivity():
        print("\n🔄 测试方法3: Redis Stream传输并转为cv2格式 (xrange)")
        for i in range(num_runs):
            print(f"  运行 {i+1}/{num_runs}...", end=" ")
            r = redis.Redis(host=REDIS_HOST, port=REDIS_PORT, db=REDIS_DB, decode_responses=False)
            result = method3_redis_stream(r)
            if result['success']:
                method3_results.append(result)
                print(f"✅ 总耗时: {result['total_time']:.3f}s")
            else:
                print(f"❌ 失败: {result['error']}")

        print("\n🔄 测试方法3增强版: Redis Stream传输并转为cv2格式 (xrevrange)")
        for i in range(num_runs):
            print(f"  运行 {i+1}/{num_runs}...", end=" ")
            result = method3_redis_stream_advanced()
            if result['success']:
                method3_advanced_results.append(result)
                print(f"✅ 总耗时: {result['total_time']:.3f}s")
            else:
                print(f"❌ 失败: {result['error']}")
    else:
        print("\n⚠️ 跳过方法3测试（Redis不可用）")
    
    # 统计结果
    print("\n" + "=" * 60)
    print("📊 测试结果统计")
    print("=" * 60)
    
    results_summary = []
    
    if method1_results:
        avg_total_1 = sum(r['total_time'] for r in method1_results) / len(method1_results)
        avg_download_1 = sum(r['download_time'] for r in method1_results) / len(method1_results)
        avg_read_1 = sum(r['read_time'] for r in method1_results) / len(method1_results)
        
        print(f"\n📁 方法1 (下载到本地磁盘再读取) - 成功 {len(method1_results)}/{num_runs} 次:")
        print(f"  ⏱️  平均总耗时: {avg_total_1:.3f}s")
        print(f"  ⬇️  平均下载耗时: {avg_download_1:.3f}s")
        print(f"  📖 平均读取耗时: {avg_read_1:.3f}s")
        if method1_results[0]['image_shape']:
            print(f"  🖼️  图片尺寸: {method1_results[0]['image_shape']}")
        
        results_summary.append(('方法1', avg_total_1))
    
    if method2_results:
        avg_total_2 = sum(r['total_time'] for r in method2_results) / len(method2_results)
        avg_download_2 = sum(r['download_time'] for r in method2_results) / len(method2_results)
        avg_decode_2 = sum(r['decode_time'] for r in method2_results) / len(method2_results)
        
        print(f"\n💾 方法2 (直接内存解码) - 成功 {len(method2_results)}/{num_runs} 次:")
        print(f"  ⏱️  平均总耗时: {avg_total_2:.3f}s")
        print(f"  ⬇️  平均下载耗时: {avg_download_2:.3f}s")
        print(f"  🔄 平均解码耗时: {avg_decode_2:.3f}s")
        if method2_results[0]['image_shape']:
            print(f"  🖼️  图片尺寸: {method2_results[0]['image_shape']}")
        
        results_summary.append(('方法2', avg_total_2))
    
    if method3_results:
        avg_total_3 = sum(r['total_time'] for r in method3_results) / len(method3_results)
        avg_read_3 = sum(r['read_time'] for r in method3_results) / len(method3_results)
        avg_decode_3 = sum(r['decode_time'] for r in method3_results) / len(method3_results)
        
        print(f"\n🔴 方法3 (Redis Stream传输) - 成功 {len(method3_results)}/{num_runs} 次:")
        print(f"  ⏱️  平均总耗时: {avg_total_3:.3f}s")
        print(f"  📥 平均读取耗时: {avg_read_3:.3f}s")
        print(f"  🔄 平均解码耗时: {avg_decode_3:.3f}s")
        if method3_results[0]['image_shape']:
            print(f"  🖼️  图片尺寸: {method3_results[0]['image_shape']}")
        
        results_summary.append(('方法3(xrange)', avg_total_3))

    if method3_advanced_results:
        avg_total_3_adv = sum(r['total_time'] for r in method3_advanced_results) / len(method3_advanced_results)
        avg_read_3_adv = sum(r['read_time'] for r in method3_advanced_results) / len(method3_advanced_results)
        avg_decode_3_adv = sum(r['decode_time'] for r in method3_advanced_results) / len(method3_advanced_results)

        print(f"\n🟣 方法3增强版 (Redis Stream xrevrange) - 成功 {len(method3_advanced_results)}/{num_runs} 次:")
        print(f"  ⏱️  平均总耗时: {avg_total_3_adv:.3f}s")
        print(f"  📥 平均读取耗时: {avg_read_3_adv:.3f}s")
        print(f"  🔄 平均解码耗时: {avg_decode_3_adv:.3f}s")
        if method3_advanced_results[0]['image_shape']:
            print(f"  🖼️  图片尺寸: {method3_advanced_results[0]['image_shape']}")

        results_summary.append(('方法3(xrevrange)', avg_total_3_adv))

    # 性能排名
    if len(results_summary) > 1:
        results_summary.sort(key=lambda x: x[1])  # 按耗时排序
        
        print(f"\n🏆 性能排名:")
        for i, (method, time_cost) in enumerate(results_summary, 1):
            print(f"  {i}. {method}: {time_cost:.3f}s")
        
        # 最快与最慢的对比
        fastest = results_summary[0]
        slowest = results_summary[-1]
        if fastest[1] != slowest[1]:
            improvement = ((slowest[1] - fastest[1]) / slowest[1]) * 100
            print(f"\n📈 {fastest[0]} 比 {slowest[0]} 快 {slowest[1] - fastest[1]:.3f}s ({improvement:.1f}%)")

def test_connectivity():
    """
    测试连接性
    """
    print("🔍 测试MinIO连接性...")
    try:
        response = requests.head(TEST_URL, timeout=10)
        if response.status_code == 200:
            print("✅ MinIO连接正常")
            content_length = response.headers.get('content-length')
            if content_length:
                size_mb = int(content_length) / (1024 * 1024)
                print(f"📏 文件大小: {size_mb:.2f} MB")
            return True
        else:
            print(f"❌ MinIO连接异常，状态码: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ MinIO连接失败: {e}")
        return False

if __name__ == "__main__":
    # 测试连接性
    if not test_connectivity():
        print("❌ 无法连接到MinIO，请检查网络和URL")
        exit(1)
    
    # 运行性能测试
    try:
        run_performance_test(num_runs=1000)
    except KeyboardInterrupt:
        print("\n\n⚠️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
    finally:
        # 清理可能残留的临时文件
        if os.path.exists(LOCAL_TEST_FILE):
            os.remove(LOCAL_TEST_FILE)
        print("\n🧹 清理完成")
