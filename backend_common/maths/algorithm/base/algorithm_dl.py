import os.path
import random
from typing import List

import numpy as np
import paddle
from paddle.inference import Config
from paddle.inference import create_predictor
from paddleocr import PaddleOCR

IMG_TARGET_SIZE = 640
# Global dictionary
SUPPORT_MODELS = {
    'YOLO', 'PPYOLOE', 'PPYOLOv2', 'RCNN', 'SSD', 'Face', 'FCOS', 'SOLOv2', 'TTFNet',
    'S2ANet', 'JDE', 'FairMOT', 'DeepSORT', 'GFL', 'PicoDet', 'CenterNet',
    'TOOD', 'RetinaNet', 'StrongBaseline', 'STGCN', 'YOLOX', 'YOLOF', 'PPHGNet',
    'PPLCNet', 'DETR', 'CenterTrack'
}

TUNED_TRT_DYNAMIC_MODELS = {'DETR'}


def load_predictor(model_dir,
                   arch,
                   run_mode='paddle',
                   batch_size=1,
                   device='CPU',
                   min_subgraph_size=3,
                   use_dynamic_shape=False,
                   trt_min_shape=1,
                   trt_max_shape=1280,
                   trt_opt_shape=640,
                   trt_calib_mode=False,
                   cpu_threads=1,
                   enable_mkldnn=False,
                   enable_mkldnn_bfloat16=False,
                   delete_shuffle_pass=False,
                   tuned_trt_shape_file="shape_range_info.pbtxt"):
    """set AnalysisConfig, generate AnalysisPredictor
    Args:
        model_dir (str): root path of __model__ and __params__
        device (str): Choose the device you want to run, it can be: CPU/GPU/XPU, default is CPU
        run_mode (str): mode of running(paddle/trt_fp32/trt_fp16/trt_int8)
        use_dynamic_shape (bool): use dynamic shape or not
        trt_min_shape (int): min shape for dynamic shape in trt
        trt_max_shape (int): max shape for dynamic shape in trt
        trt_opt_shape (int): opt shape for dynamic shape in trt
        trt_calib_mode (bool): If the model is produced by TRT offline quantitative
            calibration, trt_calib_mode need to set True
        delete_shuffle_pass (bool): whether to remove shuffle_channel_detect_pass in TensorRT.
                                    Used by action model.
    Returns:
        predictor (PaddlePredictor): AnalysisPredictor
    Raises:
        ValueError: predict by TensorRT need device == 'GPU'.
    """
    if device != 'GPU' and run_mode != 'paddle':
        raise ValueError(
            "Predict by TensorRT mode: {}, expect device=='GPU', but device == {}"
            .format(run_mode, device))
    infer_model = os.path.join(model_dir, 'inference_model/model.pdmodel')
    infer_params = os.path.join(model_dir, 'inference_model/model.pdiparams')
    if not os.path.exists(infer_model):
        infer_model = os.path.join(model_dir, 'inference.pdmodel')
        infer_params = os.path.join(model_dir, 'inference.pdiparams')
        if not os.path.exists(infer_model):
            raise ValueError(
                "Cannot find any inference model in dir: {},".format(model_dir))
    config = Config(infer_model, infer_params)
    if device == 'GPU':
        # initial GPU memory(M), device ID, just random it
        gpu_id = random.randrange(0, paddle.device.cuda.device_count())
        config.enable_use_gpu(512, gpu_id)
        # optimize graph and fuse op
        config.switch_ir_optim(True)
    elif device == 'XPU':
        if config.lite_engine_enabled():
            config.enable_lite_engine()
        config.enable_xpu(10 * 1024 * 1024)
    elif device == 'NPU':
        if config.lite_engine_enabled():
            config.enable_lite_engine()
        config.enable_npu()
    else:
        config.disable_gpu()
        config.set_cpu_math_library_num_threads(cpu_threads)
        if enable_mkldnn:
            try:
                # cache 10 different shapes for mkldnn to avoid memory leak
                config.set_mkldnn_cache_capacity(10)
                config.enable_mkldnn()
                if enable_mkldnn_bfloat16:
                    config.enable_mkldnn_bfloat16()
            except Exception as e:
                print(
                    "The current environment does not support `mkldnn`, so disable mkldnn."
                )
                pass

    precision_map = {
        'trt_int8': Config.Precision.Int8,
        'trt_fp32': Config.Precision.Float32,
        'trt_fp16': Config.Precision.Half
    }
    if run_mode in precision_map.keys():
        if arch in TUNED_TRT_DYNAMIC_MODELS:
            config.collect_shape_range_info(tuned_trt_shape_file)
        config.enable_tensorrt_engine(
            workspace_size=(1 << 25) * batch_size,
            max_batch_size=batch_size,
            min_subgraph_size=min_subgraph_size,
            precision_mode=precision_map[run_mode],
            use_static=False,
            use_calib_mode=trt_calib_mode)
        if arch in TUNED_TRT_DYNAMIC_MODELS:
            config.enable_tuned_tensorrt_dynamic_shape(tuned_trt_shape_file,
                                                       True)

        if use_dynamic_shape:
            min_input_shape = {
                'image': [batch_size, 3, trt_min_shape, trt_min_shape],
                'scale_factor': [batch_size, 2]
            }
            max_input_shape = {
                'image': [batch_size, 3, trt_max_shape, trt_max_shape],
                'scale_factor': [batch_size, 2]
            }
            opt_input_shape = {
                'image': [batch_size, 3, trt_opt_shape, trt_opt_shape],
                'scale_factor': [batch_size, 2]
            }
            config.set_trt_dynamic_shape_info(min_input_shape, max_input_shape,
                                              opt_input_shape)
            print('trt set dynamic shape done!')

    # disable print log when predict
    config.disable_glog_info()
    # enable shared memory
    config.enable_memory_optim()
    # disable feed, fetch OP, needed by zero_copy_run
    config.switch_use_feed_fetch_ops(False)
    if delete_shuffle_pass:
        config.delete_pass("shuffle_channel_detect_pass")
    predictor = create_predictor(config)
    return predictor, config


def run(predictor, batch_img: List[np.ndarray]):
    """启动运行 预测"""
    ret = []
    for idx, img in enumerate(batch_img):
        # copy img data to input tensor
        # img = batch_img[idx]  # img.shape === (1,  3, IMG_TARGET_SIZE, IMG_TARGET_SIZE)
        _, _, h, w = img.shape
        # 因为预处理阶段已经 resize 过了, 所以当前此处 scale_factor 必定==1 TODO
        scale_factor = np.array([IMG_TARGET_SIZE * 1. / h, IMG_TARGET_SIZE * 1. / w]).reshape((1, 2)).astype(np.float32)

        im_shape = np.array([IMG_TARGET_SIZE, IMG_TARGET_SIZE]).reshape((1, 2)).astype(np.float32)

        inputs = [im_shape, img, scale_factor]
        input_names = predictor.get_input_names()
        # ['im_shape', 'image', 'scale_factor']
        for i, name in enumerate(input_names):
            input_tensor = predictor.get_input_handle(name)
            input_tensor.reshape(inputs[i].shape)
            input_tensor.copy_from_cpu(inputs[i].copy())

        # do the inference
        predictor.run()

        results = []
        # get out data from output tensor
        output_names = predictor.get_output_names()
        for i, name in enumerate(output_names):
            output_tensor = predictor.get_output_handle(name)
            output_data = output_tensor.copy_to_cpu()
            results.append(output_data)
        ret.append(results)

    return ret


def init_ocr_predictor():
    """ OCR """
    # need to run only once to download and load model into memory
    return PaddleOCR(use_angle_cls=False, use_gpu=True, lang="ch", vis_font_path="../../cvutils/simsun.ttc")
