import os
from backend_common.exceptions.inspection_exception import AlgorithmCheckException


class ParameterCheck():
    """
    输入参数校验
    """

    def __init__(self):
        self.param_type_dict = {
                "SQUARE": list,
                "POLYGON": list,
                "RGB": list,
                "SELECTOR": list,
                "INTEGER": int,
                "FLOAT": float,
                "STRING": str,
                "CIRCLE": dict,
                "POINT": dict,
                "ELLIPSE": dict,
                "LINE": dict,
                "LINE_ARROW": dict,
                "RANGE": dict,
                "BOOLEAN": bool,
                "CHANNEL_PIC": str
            }

        self.roi_param_type = {
                "SQUARE": list,
                "POLYGON": list,
                "CIRCLE": dict,
                "ELLIPSE": dict,
        }

        self.type_dict = {
            list: [],
            dict: {},
            str: ""
        }

    def check_type(self, para_dict, is_roi=False):
        py_type = self.param_type_dict[para_dict['dataType']]
        if not isinstance(para_dict['value'], py_type):
            if is_roi:
                return False, f"roi参数值类型错误, 应为{py_type}"

            # 兼容前端float类型边界值变为int
            if py_type == float and isinstance(para_dict['value'], int):
                return True, "check passed"

            return False, f"输入参数 {para_dict['label']} 值类型错误, 应为{py_type}"

        if py_type in self.type_dict.keys() and para_dict['value'] == self.type_dict[py_type]:
            if is_roi:
                return False, "roi参数值的长度为0"
            return False, f"输入参数 {para_dict['label']} value的长度为0"

        return True, "check passed"

    def check_img_val(self, images_val):
        if not isinstance(images_val, list):
            return False, "images参数值的类型不是列表"

        if images_val == []:
            return False, "images参数值是空列表"

        # 图片现在不再需要在本地路径校验
        # for img_path in images_val:
        #     if not os.path.exists(img_path):
        #         return False, f"图像路径: '{img_path}' 不存在"
        return True, "check passed"

    def check_datatype_value(self, para_dict, is_roi=False):
        if para_dict['constraints']['required'] is True:
            if "value" not in para_dict:
                if is_roi:
                    return False, "roi参数缺少value字段"
                return False, f"参数 {para_dict['label']} 缺少value字段"
            status, msg = self.check_type(para_dict, is_roi)
            if not status:
                return status, msg
        return True, "check passed"

    def check_param(self, inputs):
        # 1、images 参数检查
        images_val = inputs['images']
        status, msg = self.check_img_val(images_val)
        if not status:
            return status, msg

        # 2、roi参数检查
        roi_val = inputs['roi']
        status, msg = self.check_datatype_value(roi_val, is_roi=True)
        if not status:
            return status, msg

        # 3、inputParam 参数检查
        input_param_val = inputs['inputParam']

        if input_param_val == []:
            return False, "inputParam为空列表"

        for sub_dict in input_param_val:
            status, msg = self.check_datatype_value(sub_dict, is_roi=False)
            if not status:
                return status, msg
        return True, "check passed"

    def do_check_param(self, inputs):
        status, msg = self.check_param(inputs)
        if not status:
            raise AlgorithmCheckException(f"输入参数校验异常: {msg}")
