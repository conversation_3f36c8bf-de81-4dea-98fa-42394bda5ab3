import json
import os
import threading
import typing
from abc import abstractmethod, ABCMeta
from typing import AnyStr, List, Any

from loguru import logger

from backend_common.exceptions.inspection_exception import AlgorithmCheckException, AlgorithmProcessException
from backend_common.utils.util import get_image


class AlgorithmInput(dict):
    """
    算法输入
    :key 输入参数名
    :value 输入参数值
    """

    def __init__(self, dic=None):
        super(AlgorithmInput, self).__init__()

        if dic is None:
            dic = dict()
        self.update(dic)


class AlgorithmOutput:
    """
    算法输出
    :isSuccess 是否成功
    :output 识别结果值
    :osdInfo OSD绘制信息
    """

    def __init__(self, isSuccess: bool, output: dict, osdInfo: List[dict]):
        self.isSuccess = isSuccess
        self.output = output
        self.osdInfo = osdInfo

    def __str__(self):
        return f"isSuccess: {self.isSuccess}, output: {self.output}, osdInfo: {self.osdInfo}"


class Base(object):
    """
    :_name: str
    """
    __meta_class__ = ABCMeta

    def __init__(self, name):
        self._name = name

    def __getitem__(self, item):
        return self.__dict__.get(item, None)


class AlgorithmBase(Base):
    """
    算法 Base
    """

    def __init__(self, name):
        super(AlgorithmBase, self).__init__(name)

        self._local_var = threading.local()
        self._local_var._inputs = {}
        self._name = name

    def _get_input_images(self, first_only=False):
        """
        获取图像源，返回一张或多张图
        """
        img_list = self._local_var._inputs.get("images", [])
        assert len(img_list) > 0
        if first_only:
            return [get_image(path) for path in img_list][0]
        else:
            return [get_image(path) for path in img_list]

    def _get_roi(self):
        info = self._local_var._inputs['roi']
        value = info.get("value", [])
        return value

    def _get_input_param(self):
        """
        获取inputParam参数的key-value
        """
        input_dict = {}
        for info in self._local_var._inputs['inputParam']:
            input_dict[info['key']] = info['value']
        return input_dict

    @staticmethod
    def _get_scene_image(file_abspath: AnyStr):
        """
        获取 目标场景图基准图 定义
        """

        abspath = os.path.abspath(file_abspath)

        with open(abspath, encoding='utf-8', errors='ignore') as f:
            schema = json.load(f)
        define = schema['sceneImage']
        return define

    @staticmethod
    def _get_algorithm_schema_info(file_abspath: AnyStr, key: str):
        """
        获取算法基本信息定义
        """
        abspath = os.path.abspath(file_abspath)

        with open(abspath, encoding='utf-8', errors='ignore') as f:
            schema = json.load(f)
        value = schema[key]
        return value

    def _get_roi_value(self):
        """
        获取回传的 roi 点值
        """
        return self._local_var._inputs.get("roi", {'value': []})['value']

    @abstractmethod
    def _do_detect(self) -> (bool, Any, tuple):
        """
        核心检测算法
        param:
        return:
            ret 检测是否成功
            val 数值化结果(如果有,若没有留空)
            points 需绘制的关键结果点集(具体每个点含义由各算法自行约定)
        """
        pass

    def perform(self) -> (bool, Any, List[tuple]):
        """
        算法调用主入口
        Usage:
            AlgorithmBase ab = LiquidLevelReader(xxx)
            isSuccess,output,osdInfo = ab.perform(**)
            ...
        return:
            isSuccess: bool 是否识别成功
            output: Any 核心识别结果
            osdInfo: List[dict] 需绘制的OSD信息
        """
        return self.__inner_perform__()

    @typing.final
    def __inner_perform__(self):
        try:
            logger.debug(f"{self._name} start to do-detect ... ")
            detect_ret = self._do_detect()

            logger.debug("output={}", detect_ret)
            return detect_ret

        except AlgorithmProcessException as e:
            logger.exception(e)  # just record the root cause here
            raise e
        except Exception as e:
            logger.exception(e)  # just record the root cause here
            raise e

    def img_cut_check(self, img, xmax, ymax):
        assert img is not None, "读取图片失败, 图片为空"
        h, w = img.shape[:2]
        if xmax > w or ymax > h:
            logger.info(f"[img_cut_check functions] h={h}, w={w}")
            msg = "坐标超出图片大小，请检查！"
            raise AlgorithmCheckException(f"{msg}")
