import importlib
import json
import os
import threading
import typing
from abc import abstractmethod, ABCMeta
from typing import Any, Union, AnyStr, List, Dict

import numpy as np
from loguru import logger

from backend_common.constants.alarm import AlarmRule, ExceedLimitAlarmRule
from backend_common.constants.constant import AlgorithmClassifyEnum, AlgorithmStrategyEnum, AlgorithmTypeEnum, \
    OutputDefineTypeEnum
from backend_common.constants.math_constants import ALGORITHM_CV_SYSTEM_INNER_PATH, ALGORITHM_CV_USER_DEFINE_PATH, \
    ALGORITHM_DEPL_SYSTEM_INNER_PATH, ALGORITHM_DEPL_USER_DEFINE_PATH
from backend_common.exceptions.inspection_exception import AlgorithmCheckException, AlgorithmProcessException, \
    InspectionException
from backend_common.utils.util import get_image, to_camel_case

"""
写在前面 UML类图请参考
https://www.processon.com/view/link/63d87ff3d2c3891f02fc7c95 访问密码：PB4F
"""
ValueType = typing.TypeVar

# TYPING_MAP = {'string': ValueType('str'), 'number': ValueType('float'), 'point': ValueType('tuple'),
#               'double': ValueType('float'), 'array': ValueType('list'), 'circle': ValueType('circle'),
#               'audio': ValueType('AnyStr'), 'square': ValueType('Tuple[tuple]'), 'select': ValueType('dict')}
"""
python类型和前端类型的映射
"""


class AlgorithmInput(dict):
    """
    算法输入
    :key 输入参数名
    :value 输入参数值
    """

    def __init__(self, dic=None):
        super(AlgorithmInput, self).__init__()

        if dic is None:
            dic = dict()
        self.update(dic)


class AlgorithmOutput:
    """
    算法输出
    :ret 是否成功
    :val 识别结果值
    :points 关键点坐标
    :alarm [[(是否报警，报警等级，报警描述)]]
    """

    def __init__(self, ret: bool, val: Any, points: List[tuple], alarms: List[List]):
        self.ret = ret
        self.val = val
        self.points = points

        self.alarms = alarms
        # self.level = level
        # self.desc = desc

    def __str__(self):
        return f"ret: {self.ret}, val: {self.val}, points: {self.points}, alarms: {self.alarms}"


class Base(object):
    """
    # _id: int
    :_name: str
    :_description: str
    """
    __meta_class__ = ABCMeta

    def __init__(self, _id, name, description):
        self._id = _id
        self._name = name
        self._description = description

    def __getitem__(self, item):
        return self.__dict__.get(item, None)


class AlgorithmSettings(object):
    """
    算法设定,由此设定决定一个算法的对用的具体实例及其逻辑走向
    """

    def __init__(self, classify: AlgorithmClassifyEnum, strategy: AlgorithmStrategyEnum,
                 alarm_rules: List[AlarmRule] = None):
        self.classify = classify
        self.strategy = strategy
        self.alarm_rules = alarm_rules


T = typing.TypeVar('T')


class AlgorithmParam(Base):
    """
    算法参数-入参
    cn_name: str
    # 值类型
    :val_type Type
    # 取值范围
    :val_range tuple[float]
    """

    def __init__(self, name, description, cn_name, val_type: ValueType, val_range: Union[tuple, list, dict],
                 for_sub: list = None, nullable: bool = False, showable: bool = False,_id: int = -1):
        super(AlgorithmParam, self).__init__(_id, name, description)

        # if not val_typing.__contains__(val_type):
        #     raise AlgorithmCheckException(f"Unsupported param type: {val_type}")
        self.cn_name = cn_name
        self.val_type = val_type
        self.val_range = val_range if val_range is not None else ()
        self.for_sub = for_sub if for_sub is not None else []
        self.nullable = nullable
        self.showable = showable

    def __repr__(self):
        return repr(
            (self._id, self._name, self._description, self.cn_name, self.val_type, self.val_range, self.nullable, self.showable,
             self.for_sub))


class AlgorithmOutputDefine(Base):
    """
    算法参数-出参, 用于描述算法出参类型、取值含义等信息，方便对算法出参结果进行后续处理
    :cn_name str
    # 取值含义枚举
    :val_enum dict
    """

    def __init__(self, name, description, val_enum: dict, type_=OutputDefineTypeEnum.ANALOGUE, _id=-1, rule_info=None):
        super(AlgorithmOutputDefine, self).__init__(_id, name, description)
        self.val_enum = val_enum
        self.type_ = type_
        self.ruleInfo = rule_info

    def __repr__(self):
        return repr((self._id, self._name, self._description, self.type_, self.val_enum))


class AlgorithmStrategy(Base):
    """
    算法策略
    :cn_name: str
    # 策略对应的多个配置项及其值 AlgorithmParam
    :kv_param: dict
    """

    def __init__(self, owner: AlgorithmClassifyEnum, strategy: AlgorithmStrategyEnum, cn_name, description,
                 kv_param=None, _id: int = -1):
        # super(AlgorithmStrategy, self).__init__(_id, str(strategy), description)
        super(AlgorithmStrategy, self).__init__(owner.value[0], str(strategy), description)

        self.cn_name = cn_name
        # 所属算法分类
        self.owner = owner
        self.kv_param = kv_param


class AlgorithmBaseInfo(Base):
    code: str
    cn_name: str
    type_: str
    batch_support: bool
    alarm_support: bool

    def __init__(self, _id, code, name, cn_name, desc, type_: str = AlgorithmTypeEnum.OPENCV.value,
                 batch_support=False, alarm_support=False, is_override_alarm=False, result_show_type=""):
        super(AlgorithmBaseInfo, self).__init__(_id, name, desc)
        self.code = code
        self.cn_name = cn_name
        self.type_ = type_
        self.batch_support = batch_support
        self.alarm_support = alarm_support
        self.is_override_alarm = is_override_alarm
        self.result_show_type = result_show_type

    def __repr__(self):
        return repr((self._id, self._name, self._description, self.code, self.cn_name, self.type_, self.batch_support,
                     self.alarm_support, self.result_show_type))


class AlgorithmClassifyInfo(Base):
    def __init__(self, _id, name, desc):
        super(AlgorithmClassifyInfo, self).__init__(_id, name, desc)

    def __repr__(self):
        return repr((self._id, self._name, self._description))


class AlgorithmStrategyInfo(Base):
    code: str
    cn_name: str
    kv_params: dict

    def __init__(self, _id, code, name, cn_name, desc, kv_params=None):
        super(AlgorithmStrategyInfo, self).__init__(_id, name, desc)
        self.code = code
        self.cn_name = cn_name
        self.kv_params = kv_params

    def __repr__(self):
        return repr((self._id, self._name, self._description, self.code, self.cn_name, self.kv_params))


class AlgorithmSubxInfo(Base):
    # classifies: List[AlgorithmClassifyInfo]
    # strategies: List[AlgorithmStrategyInfo]
    # TODO 当前实现下 所有子算法支持的classify 和 strategy 都是一致的 (即：和父算法所申明的是一致的)， 此处不再单独定义

    # 私有专属参数, 当子算法有不同于super的参数时有用(参数为子算法特有)
    privacy_params: List[AlgorithmParam]

    def __init__(self, _id, name, desc, classifies=None, strategies=None, privacy_params=None):
        super(AlgorithmSubxInfo, self).__init__(_id, name, desc)
        self.classifies = classifies
        self.strategies = strategies
        self.privacy_params = privacy_params

    def __repr__(self):
        return repr((self._id, self._name, self._description, self.privacy_params))


class AlgorithmROI(Base):
    """
    算法ROI区域
    """

    def __init__(self, name, description, val_type: ValueType, nullable: bool = False, _id: int = -1):
        super(AlgorithmROI, self).__init__(_id, name, description)

        self.val_type = val_type
        self.nullable = nullable

    def __repr__(self):
        return repr(
            (self._id, self._name, self._description, self.val_type, self.nullable))


class AlgorithmSceneImage(Base):
    """
    算法场景 基准图
    """

    def __init__(self, name, description, uri, inuse: bool = False, _id: int = -1):
        super(AlgorithmSceneImage, self).__init__(_id, name, description)

        self.uri = uri  #
        self.in_use = inuse

    def __repr__(self):
        return repr((self._id, self._name, self._description, self.uri, self.in_use))


class AlgorithmDetail:
    info: AlgorithmBaseInfo
    classifies: List[AlgorithmClassifyInfo]
    strategies: List[AlgorithmStrategyInfo]
    roi: AlgorithmROI
    scene_image: AlgorithmSceneImage

    params: List[AlgorithmParam]  # 入参定义
    output_define: AlgorithmOutputDefine  # 出参定义

    sub_list: List[AlgorithmSubxInfo]  # 子算法清单

    def __init__(self, info, classifies, strategies, roi, scene_image, params, output_define, sub_list):
        self.info = info
        self.classifies = classifies
        self.strategies = strategies
        self.roi = roi
        self.scene_image = scene_image
        self.params = params
        self.output_define = output_define
        self.sub_list = sub_list

    def __repr__(self):
        return repr(
            (self.info, self.classifies, self.strategies, self.roi, self.scene_image,
             self.params, self.output_define, self.sub_list))


class AlgorithmBase(Base):
    """
    算法 Base
    TODO 从概念上将 算法定义&算法实例&算法配置(输入输出) 区分开来
    TODO 其他 1. 参数预检查 (如入参是否在指定范围内 √
             2. 报警(越限）逻辑的实现  _alarm_trig √
             3. 算法输出根据类型变化适配  output_type √
    """
    # 实例相关
    # is_primary: bool = False
    _base_params: Dict[str, AlgorithmParam] = {
        "classify": AlgorithmParam("classify", "算法分类", "算法分类", ValueType('str'),
                                   ()),
        "strategy": AlgorithmParam("strategy", "算法策略", "算法策略", ValueType('str'),
                                   ())}

    def __init__(self, name, description, cn_name,  # classify: AlgorithmClassifyEnum,
                 strategies: List[AlgorithmStrategy],
                 params: Dict[str, AlgorithmParam], _id: int = -1):
        super(AlgorithmBase, self).__init__(_id, name, description)

        self._local_var = threading.local()
        self._local_var._settings = []
        self._local_var._inputs = {}

        self.cn_name = cn_name
        # self.classify = classify
        # 当前算法策略
        self.strategies = strategies
        # 当前算法参数
        self.params = params

    @typing.final
    def __param_check(self) -> (bool, str):
        """
        参数预检查
        return:
            :bool 是否通过
            :str 校验不通过的异常原因
        """
        # TODO optimize
        ignore_params = ["kwargs", "images", "targetSceneImage", "roi"]
        # ignore_params = ["kwargs", "images", "targetSceneImage"]

        def in_range(val, rang, typ) -> bool:
            """
            校验 入参值是否在 预期范围内
            """
            # 没有取值范围限制
            if len(rang) <= 0:
                return True
            # TODO 目前只能对列表中的类型的值进行校验
            if typ not in ['number', 'select', 'range']:
                return True

            if typ == 'select' and isinstance(rang, dict):
                # val = Red , range =\
                # {'Blue': '蓝', 'Cyan': '青', 'Green': '绿', 'Purple': '紫', ...}
                return rang.keys().__contains__(str(val))

            if typ == 'range' and isinstance(val, list):
                return val[0] < val[1]

            if typ == 'number' and (isinstance(val, float) or isinstance(val, int) or isinstance(val, str)):
                if isinstance(val, str):
                    val = float(val)
                # rang : [默认最小，默认最大，精度，默认], 此处不能用默认，和前端约定 ±8个9
                return -99999999 <= val <= 99999999

            return False

        def check_single_param(input_name, define, value=None) -> (bool, str):
            """ 对某一具体参数进行校验
                :input_name 入参名称
                :define 参数的定义
                :value 入参值
            """

            if input_name in ignore_params:
                return True, ""

            # 1. 检查是否可以为空
            if value is None:
                if define.nullable:
                    return True, ""
                else:
                    return False, f"Value of '{input_name}' can't be {value}"

            # 2. 对应参数所传入的值是否在区间内
            if not in_range(value, define.val_range, define.val_type):
                range_ref = [-99999999, 99999999] if define.val_type == 'number' else define.val_range
                return False, f"Value '{value}' of '{input_name}' not in range" \
                              f" {range_ref} for {self._name}"

            return True, ""

        def check_array_param(array_input_name, define) -> (bool, str):
            """
                对某一array 类型 参数进行校验
            """
            if self._local_var._inputs[array_input_name] is None or len(self._local_var._inputs[array_input_name]) <= 0:
                return True, ""

            # 已定义的参数
            defined_names = [p._name for p in define.val_range if p.nullable is False]
            # array 内层的参数，判断是否少传参了
            for index, params_grp in enumerate(self._local_var._inputs[array_input_name]):
                input_names = params_grp.keys()
                # 是否少传参了
                for param in defined_names:
                    if not input_names.__contains__(param):
                        return False, f"Missing param named '{param}' for {self._name}.{array_input_name}[{index}]"

                for param in params_grp:
                    # type == array 的 val_range [] 至少会有一个 {} 定义
                    if len(define.val_range) > 0:  # 以防万一
                        param_define = [d for d in define.val_range if d._name == param]
                        if len(param_define) > 0:  # 以防万一
                            ok, msg = check_single_param(param, param_define[0], params_grp[param])
                            if not ok:
                                return ok, msg

            return True, ""

        # 最外层参数，判断是否少传参了
        all_inputs = [input_name for input_name in self._local_var._inputs.keys()]
        for define in self._supported_params().values():
            if not all_inputs.__contains__(define._name) and not define.nullable:
                return False, f"Missing param named '{define._name}' for {self._name}"

        for input_name in self._local_var._inputs.keys():

            if input_name in ignore_params:
                continue
            # 参数的定义# 参数的定义

            define = self._supported_params().get(input_name, None)
            if define is None:
                return False, f"Undefined param named '{input_name}' for {self._name}"

            if define.val_type == "array":
                ok, msg = check_array_param(input_name, define)
            else:
                value = self._local_var._inputs.get(input_name)
                ok, msg = check_single_param(input_name, define, value)

            if not ok:
                return ok, msg
        return True, ""

    def __output_format(self, detect_ret: tuple, alarms: List[List]) -> (bool, Any, List[tuple], List[List]):
        """
        对输出进行格式化,
            内置变量 质量位： STATUS
                   数值：  VALUE
                   报警等级： ALEVEL
        return:[[(
            ret : 是否报警
            level: 报警等级
            desc: 报警描述
            )]]
        """
        _, values, _ = detect_ret
        if alarms is None or len(alarms) == 0:
            if values is not None and len(values) > 0:
                alms_of_img = [(False, None, None) for _ in range(len(values[0]))]
                # 此处根据输入图片数量再初始化一下，方便后续使用时直接按 index 获取
                alarms = [alms_of_img for _ in self._local_var._inputs.get("images", [])]
            else:
                # 无告警，无等级，无报警描述
                alarms = [[(False, None, None)]]

        # 如果识别失败或 输出类型非‘根据模板',则原样输出
        # if not STATUS or self._local_var._settings.output_type != AlgorithmOutputTypeEnum.ByTemplate \
        #         or self._local_var._settings.output_template is None:
        logger.info(f"{self.cn_name}_{self._id} perform success ")
        return *detect_ret, alarms

    def _alarm_trig(self, detect_ret) -> List[List]:
        """
        根据报警规则触发报警
        return:
            ret : 是否报警
            level: 报警等级
            desc: 报警描述
        """
        _, val, _ = detect_ret
        alarms = []
        if self._local_var._settings.alarm_rules is None or len(self._local_var._settings.alarm_rules) == 0:
            return alarms

        for ret_of_each_img in val:
            alm_of_img = []
            for v_idx, a_v in enumerate(ret_of_each_img):
                # for r_idx, rule in enumerate(self._local_var._settings.alarm_rules):
                # 对于一张图仅产生一条汇总报警的，报警逻辑应当走自己的逻辑 比如 安全帽，明火等
                # 此时报警个数 和检测结果数是不一致的, 无法直接 [v_idx]
                rule = self._local_var._settings.alarm_rules[v_idx]
                ret, level, desc = False, None, None
                if a_v is None:
                    alm_of_img.append((ret, level, desc))
                    continue
                if isinstance(rule, ExceedLimitAlarmRule):
                    if rule.hh_is_bind and a_v >= rule.hh_limit:
                        ret, level, desc = True, "HH", "越高高限报警" if rule.hh_txt is None else rule.hh_txt
                    elif rule.h_is_bind and a_v >= rule.h_limit:
                        ret, level, desc = True, "H", "越高限报警" if rule.h_txt is None else rule.h_txt
                    elif rule.ll_is_bind and a_v <= rule.ll_limit:
                        ret, level, desc = True, "LL", "越低低限报警" if rule.ll_txt is None else rule.ll_txt
                    elif rule.l_is_bind and a_v <= rule.l_limit:
                        ret, level, desc = True, "L", "越低限报警" if rule.l_txt is None else rule.l_txt
                        
                # TODO support more type AlarmRules
                # alm_of_av.append((ret, level, desc))
                alm_of_img.append((ret, level, desc))
            alarms.append(alm_of_img)
        return alarms

    def _get_strategy_by_name(self, name: str):
        """
        根据策略名获取具体策略对象
        """
        ls = [s for s in self._supported_strategy() if s._name == name]
        if len(ls) != 1:
            raise AlgorithmProcessException(f"Unsupported strategy :{name}")
        return ls[0]

    def _get_input_value_by_name(self, param_name: str):
        """
        根据参数名获取具体 参数值
        """
        param_dict = {**self._supported_params(), **self._base_params}
        lp = [self._local_var._inputs.get(pname) for pname in param_dict.keys() if pname == param_name]
        if len(lp) != 1:
            raise AlgorithmProcessException(f"Unsupported param : {param_name}")
        return lp[0]

    def _get_input_images(self, first_only=False):
        """
        获取图像源，返回一张或多张图
        """
        img_list = self._local_var._inputs.get("images", [])
        assert len(img_list) > 0
        if first_only:
            return [get_image(path) for path in img_list][0]
        else:
            return [get_image(path) for path in img_list]

    @staticmethod
    def _get_roi_define(file_abspath: AnyStr):
        """
        获取图像源外部整个(包含所有待识别对象的最小外接图形)感兴趣区域 的定义
        """

        abspath = os.path.abspath(file_abspath)

        with open(abspath, encoding='utf-8', errors='ignore') as f:
            schema = json.load(f)
        roi = schema['roi']
        # changing keys of dictionary
        roi['val_type'] = roi['type']
        del roi['type']
        return roi

    @staticmethod
    def _get_scene_image(file_abspath: AnyStr):
        """
        获取 目标场景图基准图 定义
        """

        abspath = os.path.abspath(file_abspath)

        with open(abspath, encoding='utf-8', errors='ignore') as f:
            schema = json.load(f)
        define = schema['targetSceneImage']
        return define

    @staticmethod
    def _get_algorithm_schema_info(file_abspath: AnyStr, key: str):
        """
        获取算法基本信息定义
        """
        abspath = os.path.abspath(file_abspath)

        with open(abspath, encoding='utf-8', errors='ignore') as f:
            schema = json.load(f)
        value = schema[key]
        return value

    def _get_roi_value(self):
        """
        获取回传的 roi 点值
        """
        return self._local_var._inputs.get("roi", {'value': []})['value']

    def _get_scene_img_uri(self):
        """
        获取回传的 targetSceneImage-uri 路径
        """
        return self._local_var._inputs.get("targetSceneImage", {'uri': ''})['uri']

    @abstractmethod
    def _supported_classify(self):
        ...

    @abstractmethod
    def _supported_strategy(self):
        ...

    @abstractmethod
    def _supported_params(self):
        ...

    @abstractmethod
    def get_algorithm_detail(self, a_type) -> AlgorithmDetail:
        """
        return:
            AlgorithmDetail 获取算法定义详情,包括算法进本信息、支持的主次清单、策略清单、算法参数清单等
        """
        ...

    def _classify2info(self, classify: AlgorithmClassifyEnum) -> AlgorithmClassifyInfo:
        """
        将Enum转化为info
        """
        return AlgorithmClassifyInfo(classify.value[0], classify.name, classify.value[2])

    def _strategy2info(self, strategy: AlgorithmStrategy) -> AlgorithmStrategyInfo:
        """
        将Enum转化为info
        """
        return AlgorithmStrategyInfo(strategy._id, strategy._name.split(".")[1], strategy._name, strategy.cn_name,
                                     strategy._description, strategy.kv_param)

    def _output_define2info(self, output_define: dict) -> AlgorithmOutputDefine:
        """
        将 输出定义类型 转化为 info
        """
        return AlgorithmOutputDefine(None, output_define['desc'], output_define.get('val_enum'),
                                     type_=output_define['type'], rule_info=output_define['ruleInfo'])

    @abstractmethod
    def _preprocess(self) -> Any:
        """
        图像预处理，比如 灰度、二值化、膨胀、腐蚀 等, 预处理结果内部保存
        """
        pass

    @abstractmethod
    def _do_detect(self) -> (bool, Any, tuple):
        """
        核心检测算法
        param:
            classify 算法运行时采用主主模型) or 次(质量模型)类别
            strategy 算法运行时采用何种策略
        return:
            ret 检测是否成功
            val 数值化结果(如果有,若没有留空)
            points 检测到的关键结果点集(具体每个点含义由各算法自行约定)
        """
        pass

    @abstractmethod
    def _postprocess(self) -> Any:
        """
        后置处理： 可选, 比如结果展示等
        """
        pass

    def gen_result_img(self) -> List[np.ndarray]:
        """
        生成绘制结果图像
        """
        return self._gen_result_img()

    @abstractmethod
    def _gen_result_img(self) -> List[np.ndarray]:
        pass

    def perform(self, settings: AlgorithmSettings) -> (bool, Any, List[tuple], tuple):
        """
        算法调用主入口
        Usage:
            AlgorithmBase ab = LiquidLevelReader(xxx)
            ret,val,points,*alarm = ab.perform(**)
            ...
        param: settings 算法设定
        return:
            ret: bool 是否识别成功
            val: Any 核心识别结果
            points: List[tuple] 可选项,关键识别结果点的坐标列表
            alarming: tuple 是否报警,报警等级,报警描述
        """
        return self.__inner_perform__(settings)

    @typing.final
    def __inner_perform__(self, settings: AlgorithmSettings):
        # lock.acquire()
        self._local_var._settings = settings
        # lock.release()

        b, msg = self.__param_check()
        if not b:
            raise AlgorithmCheckException(f"Param check exception: {msg}")

        detect_ret = False, None, None
        # 默认无告警, 无等级, 无描述
        alarms = None

        try:

            logger.debug(f"{self.cn_name}_{self._id} start to pre-process ... ")
            self._preprocess()

            logger.debug(f"{self.cn_name}_{self._id} start to do-detect ... ")
            detect_ret = self._do_detect()

            detect_success = detect_ret[0]
            # 是否需要检测报警
            # is_trig_alarm = self._local_var._settings.output_type == AlgorithmOutputTypeEnum.AlarmLevel and self._local_var._settings.alarm_rules is not None
            # is_trig_alarm = self._local_var._settings.output_type == AlgorithmOutputTypeEnum.AlarmLevel
            if detect_success:
                logger.debug(f"{self.cn_name}_{self._id} start to trig-alarm ... ")
                alarms = self._alarm_trig(detect_ret)

            logger.debug(f"{self.cn_name}_{self._id} start to post-process ...")
            self._postprocess()

            logger.debug(f"{self.cn_name}_{self._id} start to format-output-return ...")
            format = self.__output_format(detect_ret, alarms)
            logger.debug("output={}", format)
            return format

        except AlgorithmProcessException as e:
            logger.exception(e)  # just record the root cause here
            raise e
        except Exception as e:
            logger.exception(e)  # just record the root cause here
            raise e

    @abstractmethod
    def determine_algorithm_instance(self, settings: AlgorithmSettings = None, index: int = 0,
                                     a_type: str = "system_inner"):
        """
        根据classify 和 strategy 确定需要走哪个算法实例
        :param settings  算法块设定参数
        :param index  算法文件 index
        :return:
             算法块实例
        """

        ...

    @staticmethod
    def json_schema_2_algorithm_params(file_abspath: AnyStr) -> dict:
        """
        读取算法schema描述文件并将inputParam部分转换为成算法参数 AlgorithmInput
        :param file_abspath: json 文件绝对路径
        :return: 参数名和参数定义的 dict
        """
        abspath = os.path.abspath(file_abspath)

        with open(abspath, encoding='utf-8', errors='ignore') as f:
            schema = json.load(f)
        assert schema['inputParam'] is not None

        def __one_param(key, param):
            description = param.get('des')
            cn_name = param.get('name')
            # 默认为 string 类型
            raw_type = param.get('type', 'string')
            # 取值范围 默认为 (): 无限制
            val_range = param.get('val_range', ())
            # for_sub 默认为 False
            for_sub = param.get('for_sub', [])
            # nullable 默认为 False
            nullable = param.get('nullable', False)
            # showable 默认为 False
            showable = param.get('showable', False)
            # id 默认为 -1
            _id = param.get('id', -1)
            return AlgorithmParam(key, description, cn_name, raw_type, val_range, for_sub, nullable, showable, _id)

        def __list_param(key, param):
            assert len(param['val_range']) > 0  # val_range 中至少有一个值
            list_val = [__one_param(k, param['val_range'][0][k]) for k in param['val_range'][0].keys()]

            return AlgorithmParam(key, param['desc'], param['name'], param['type'], list_val, param.get('for_sub', []),
                                  param.get('nullable', True), param.get('showable', False))

        def __c2param(key: dict) -> AlgorithmParam:
            assert schema['inputParam'][key] is not None
            param = schema['inputParam'][key]
            if param['type'] == 'array':  # 数组类型特殊处理
                return __list_param(key, param)
            else:  # 单个参数的dict
                return __one_param(key, param)

        return {key: __c2param(key) for key in schema['inputParam'].keys()}
            
        # inputParam = {key: __c2param(key) for key in schema['inputParam'].keys()}
        # roi_param = __one_param("roi", schema["roi"])
        # inputParam['roi'] = roi_param
        # return inputParam

    def load_privacy_params(self, sub_clz):
        """
        获取 sub 算法的私有参数 即 : for_sub.contains(subX._index) 的参数
        """
        assert issubclass(sub_clz, self.__class__)
        ret = []
        for param in list(self._supported_params().values()):
            print(param)
            if param.for_sub.__contains__(sub_clz._index):
                ret.append(param)
        return ret

    def load_algorithm_sublist(self, algorithm_name, a_type, type) -> typing.List[AlgorithmSubxInfo]:
        """
        加载算法块的所有 子算法
        :param     algorithm_name  算法名称
        :param     a_type          算法分类   'system_inner' OR 'user_define'
        :param     type            算法类型   'cv' OR 'depl'

        :return:    子算法清单信息  List[AlgorithmSubxInfo]
        """

        def __sub2info(clz):
            privacy_params = self.load_privacy_params(clz)
            return AlgorithmSubxInfo(clz._index, clz._name, clz._description,
                                     # TODO 当前所有sub算法的 classifies、 strategies 与 sup 定义的一致
                                     classifies=[self._classify2info(c) for c in self._supported_classify()],
                                     strategies=[self._strategy2info(c) for c in self._supported_strategy()],
                                     privacy_params=privacy_params)

        # self._name  算法块唯一标识名,通常需与算法块文件夹名称一致，比如 liquid_level
        module_name = f".{algorithm_name}.ab_{algorithm_name}"
        target_clazz_name = f"{to_camel_case(algorithm_name, '_')}Reader"
        if type == 'cv':
            base_path = ALGORITHM_CV_SYSTEM_INNER_PATH
            if a_type == "user_define":
                base_path = ALGORITHM_CV_USER_DEFINE_PATH
        else:
            base_path = ALGORITHM_DEPL_SYSTEM_INNER_PATH
            if a_type == "user_define":
                base_path = ALGORITHM_DEPL_USER_DEFINE_PATH
        try:
            M = importlib.import_module(module_name, base_path)
            super_clazz_ = getattr(M, target_clazz_name)
        except ModuleNotFoundError:
            raise InspectionException(
                f"Auto-load module '{module_name}' from '{base_path}' failure, please check it manually")
        # !!!!  找出子类中名称与当前 classify.name 一致的作为 target 算法类  !!!!
        # 此处认为已经import过了，不再 import
        target_clazz_list = super_clazz_.__subclasses__()

        return [__sub2info(clz) for clz in target_clazz_list]
    
    def img_cut_check(self, img, xmax, ymax):
        assert img is not None, f"读取图片失败, 图片为空"
        h, w = img.shape[:2]
        if xmax > w or ymax > h:
            print(h, w)
            msg = "Coordinates is out of the image, please check !"
            raise AlgorithmCheckException(f"Param check exception: {msg}")



