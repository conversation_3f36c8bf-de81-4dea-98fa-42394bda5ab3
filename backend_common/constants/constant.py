from enum import Enum

import numpy as np

# 红色区间1
# https://blog.csdn.net/wanggsx918/article/details/23272669
LOWER_RED1 = np.array([0, 43, 46])
UPPER_RED1 = np.array([10, 255, 255])
# 红色区间2
LOWER_RED2 = np.array([156, 43, 46])
UPPER_RED2 = np.array([180, 255, 255])
# 绿色区间
LOWER_GREEN = np.array([35, 43, 46])
UPPER_GREEN = np.array([77, 255, 255])
# 蓝色区间
LOWER_BLUE = np.array([100, 43, 46])
UPPER_BLUE = np.array([124, 255, 255])
# 橙黄色区间  11-25|26-34
LOWER_YELLOW = np.array([11, 43, 46])
UPPER_YELLOW = np.array([34, 255, 255])
# 青色
LOWER_CYAN = np.array([78, 43, 46])
UPPER_CYAN = np.array([99, 255, 255])
# 紫色
LOWER_PURPLE = np.array([125, 43, 46])
UPPER_PURPLE = np.array([155, 255, 255])
# 黑色
LOWER_BLACK = np.array([0, 0, 0])
UPPER_BLACK = np.array([180, 255, 46])
# 白色
LOWER_WHITE = np.array([0, 0, 221])
UPPER_WHITE = np.array([180, 30, 255])
# 灰色
LOWER_GRAY = np.array([0, 0, 46])
UPPER_GRAY = np.array([180, 43, 220])
LOWER = np.stack((LOWER_RED1, LOWER_RED2, LOWER_GREEN, LOWER_BLUE,
                  LOWER_YELLOW, LOWER_CYAN, LOWER_PURPLE, LOWER_BLACK, LOWER_WHITE, LOWER_GRAY), axis=0)
UPPER = np.stack((UPPER_RED1, UPPER_RED2, UPPER_GREEN, UPPER_BLUE, UPPER_YELLOW, UPPER_CYAN, UPPER_PURPLE,
                  UPPER_BLACK, UPPER_WHITE, UPPER_GRAY), axis=0)
# color_list = ['红色', '红色', '绿色', '蓝色', '橙黄色', '青色', '紫色', '黑色', '白色', '灰色']
# 红色对应两个颜色区间，故color_list有两个Red
color_list = ["Red", "Red", "Green", "Blue", "Yellow", "Cyan", "Purple", "Black", "White", "Gray"]

class ColorSeriesEnum(Enum):
    Red = 0
    Green = 1
    Blue = 2
    Yellow = 3
    Cyan = 4
    Purple = 5
    Black = 6
    White = 7
    Gray = 8

    @classmethod
    def from_str(cls, txt):
        assert txt is not None
        for k, v in cls.__members__.items():
            if k.casefold() == txt.casefold():
                return v
        else:
            raise ValueError(f"'{cls.__name__}' enum not found for '{txt}'")

    @classmethod
    def value_of(cls, value):
        assert value is not None
        for k, v in cls.__members__.items():
            if v.value == value:
                return v
        else:
            raise ValueError(f"'{cls.__name__}' enum not found for '{value}'")


class AlgorithmOutputTypeEnum(Enum):
    # 仅质量位
    QualityOnly = 0
    # 值+质量位 ->  质量位 好0，坏1
    ValueQuality = 1
    # 报警等级 :  1->高高，低低 2->高，低
    AlarmLevel = 2

    @classmethod
    def from_str(cls, txt):
        for k, v in cls.__members__.items():
            if k.casefold() == txt.casefold():
                return v
        else:
            raise ValueError(f"'{cls.__name__}' enum not found for '{txt}'")

    @classmethod
    def value_of(cls, value):
        for k, v in cls.__members__.items():
            if v.value == value:
                return v
        else:
            raise ValueError(f"'{cls.__name__}' enum not found for '{value}'")


class AlgorithmClassifyEnum(Enum):
    # 主
    Primary = (0, "primary", "主要模型")
    # 次(质量)
    Secondary = (1, "secondary", "质量模型")

    @classmethod
    def from_str(cls, txt):
        for k, v in cls.__members__.items():
            if k.casefold() == txt.casefold():
                return v
        else:
            raise ValueError(f"'{cls.__name__}' enum not found for '{txt}'")

    @classmethod
    def value_of(cls, value):
        for k, v in cls.__members__.items():
            if v.value[0] == value:
                return v
        else:
            raise ValueError(f"'{cls.__name__}' enum not found for '{value}'")


class AlgorithmStrategyEnum(Enum):
    """
    算法策略等级枚举
    """
    # 主策略
    Main = (0, "main"),
    Second = (1, "second")
    Third = (2, "third"),
    Fourth = (3, "fourth")
    Fifth = (4, "fifth")

    @classmethod
    def from_str(cls, txt):
        for k, v in cls.__members__.items():
            if k.casefold() == txt.casefold():
                return v
        else:
            raise ValueError(f"'{cls.__name__}' enum not found for '{txt}'")

    @classmethod
    def value_of(cls, value):
        for k, v in cls.__members__.items():
            if v.value[0] == value:
                return v
        else:
            raise ValueError(f"'{cls.__name__}' enum not found for '{value}'")


class AlgorithmTypeEnum(Enum):
    # 传统机器学习
    OPENCV = "openCV"
    # 深度学习
    DEEPLEARN = "deeplearn"

    @classmethod
    def from_str(cls, txt):
        for k, v in cls.__members__.items():
            if k.casefold() == txt.casefold():
                return v
        else:
            raise ValueError(f"'{cls.__name__}' enum not found for '{txt}'")

    @classmethod
    def value_of(cls, value):
        for k, v in cls.__members__.items():
            if v.value == value:
                return v
        else:
            raise ValueError(f"'{cls.__name__}' enum not found for '{value}'")


class OutputDefineTypeEnum(Enum):
    STRING = "string"  # 字符串
    ANALOGUE = "analogue"  # 模拟量
    SWITCH = "switch"  # 开关量 需定义 val_enum
    DIGITAL = "digital"  # 数字量 需定义 val_enum

    @classmethod
    def from_str(cls, txt):
        for k, v in cls.__members__.items():
            if k.casefold() == txt.casefold():
                return v
        else:
            raise ValueError(f"'{cls.__name__}' enum not found for '{txt}'")

    @classmethod
    def value_of(cls, value):
        for k, v in cls.__members__.items():
            if v.value == value:
                return v
        else:
            raise ValueError(f"'{cls.__name__}' enum not found for '{value}'")


# 默认的识别结果准确率%
FAKE_PERCENT = .9654321

# OSD绘制背景色 RGB
OSD_BG_COLOR = (0, 255, 255)

# OSD绘制前景色
OSD_FG_COLOR = (255, 255, 255)

# OSD score 保留位数
OSD_SCORE_PRECISION = 2
