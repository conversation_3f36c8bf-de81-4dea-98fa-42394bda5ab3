from typing import Any, Dict, List
from pydantic import BaseModel


class ConfigPointBody(BaseModel):
    ip: str
    user: str
    pwd: str
    p: float
    t: float
    z: float


class DrawShapeBody(BaseModel):
    imgUrl: str
    osdItemList: List[Dict[str, Any]]


class MatchingPositionReqBody(BaseModel):
    img_path: str
    tmp_paths: list
    bench_mark_paths: list


class RequestBody(BaseModel):
    request_param: Dict[str, Any]


class TransformCircleReqBody(BaseModel):
    form_square: List[Any]
    to_square: List[Any]
    center: Any
    radius: int


class TransformPicReqBody(BaseModel):
    img_path: str
    form_square: List[Any]
    to_square: List[Any]


class TransformPointsReqBody(BaseModel):
    input_points: List[Any] = False
    form_square: List[Any] = False
    to_square: List[Any] = False


class ZeroPositionReqBody(BaseModel):
    img_path: str
    tmp_path: str
