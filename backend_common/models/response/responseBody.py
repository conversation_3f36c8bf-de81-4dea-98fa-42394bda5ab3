from typing import Union

from fastapi import status
from fastapi.responses import JSONResponse, Response


def response(*, code=200, data: Union[list, dict, str, None], message="Success") -> Response:
    return JSONResponse(
        status_code=status.HTTP_200_OK,
        content={
            'code': code,
            'msg': message,
            'data': data,
        }
    )


def response_data(img_path: str = "", degrees: float = 0.0, alert_status: int = 1, result_des: dict = {}):
    return {
        "img_path": img_path,
        "degrees": degrees,
        "status": alert_status,
        "result_des": result_des
    }


def response_data_none():
    return JSONResponse(
        status_code=status.HTTP_200_OK,
        content={
            'code': 200,
            'message': "success",
            'data': None,
        }
    )


def response_data_point(left_top_x: float = 0.0, left_top_y: float = 0.0, right_bottom_x: float = 0.0,
                        right_bottom_y: float = 0.0,
                        left_bottom_x: float = 0.0, left_bottom_y: float = 0.0, right_top_x: float = 0.0,
                        right_top_y: float = 0.0, ):
    return [
        [left_top_x, left_top_y],
        [right_top_x, right_top_y],
        [right_bottom_x, right_bottom_y],
        [left_bottom_x, left_bottom_y]
    ]
