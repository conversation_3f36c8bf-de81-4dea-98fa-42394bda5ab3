import math
import os
from math import sqrt
from sys import platform
from typing import Dict, Any

import cv2
from PIL import Image, ImageDraw, ImageFont
from pyzbar.locations import Rect

from backend_common.constants.constant import *
from backend_common.exceptions.inspection_exception import AlgorithmProcessException

font_path = ''
if ("linux" == platform) or ("linux2" == platform):
    font_path = '/home/<USER>/backend_common/utils/font/simsun.ttc'
    # font_path = '/opt/tjh/model_server/git_code_cv/intelligent_inspection_backend/backend_cv/backend_common/utils/font/simsun.ttc'
else:
    font_path = 'backend_common/fonts/simsun.ttc'


# TODO 此工具包代码和算法代码没有一起打包，新的算法包导入方式进系统有缺少依赖的风险
class RectUtils:
    """
    矩形相关 工具类

    """

    @staticmethod
    def rect_contains(rect, pt, *args):
        # a < x < a + c and b < y < b + d
        # （中心点坐标，（宽度，高度）,旋转的角度
        rectCent, (w, h), c = rect
        # 最小矩形框的四个点坐标（左下，左上，右下，右上）
        _, lt, rb, _ = cv2.boxPoints(rect)

        first, others = lt[0] < pt[0] < rb[0] and lt[0] < pt[1] < rb[1], True
        for o_pt in args:
            other = lt[0] < o_pt[0] < rb[0] and lt[0] < o_pt[1] < rb[1]
            if not other:
                others = False
                break
        return first and others

    @staticmethod
    def rect_area(lt, rb):
        # w * h
        return abs(lt[0] - rb[0]) * abs(lt[1] - rb[1])

    @staticmethod
    def rect_area2(w, h):
        # w * h
        return abs(w * h)

    @staticmethod
    def square_to_list(square: Dict[str, Any]):
        left_up_point = square['leftTop']
        right_up_point = square['rightTop']
        left_down_point = square['leftBottom']
        right_down_point = square['rightBottom']
        return [left_up_point, right_up_point, left_down_point, right_down_point]

    @staticmethod
    def rect_to_4_points(rect: Rect) -> list:
        x, y, w, h = rect
        return [[x, y], [x + w, y], [x + w, y + h], [x, y + h]]

    @staticmethod
    def ltrb_to_4_points(lt, rb) -> list:
        x, y, w, h = lt[0], lt[1], rb[0] - lt[0], rb[1] - lt[1]
        return [[x, y], [x + w, y], [x + w, y + h], [x, y + h]]


# 第三版，在第二版的基础上直方图均衡化+对指针线增加w/h>2及w>r/2的限制条件
class Functions:

    @staticmethod
    def get_clock_angle(v1, v2):
        # 2个向量模的乘积 ,返回夹角
        the_norm = np.linalg.norm(v1) * np.linalg.norm(v2)
        # 叉乘
        rho = np.rad2deg(np.arcsin(np.cross(v1, v2) / the_norm))
        # 点乘
        theta = np.rad2deg(np.arccos(np.dot(v1, v2) / the_norm))
        if rho > 0:
            return 360 - theta
        else:
            return theta

    @staticmethod
    def distances(a, b):
        # 返回两点之间的距离
        x1, y1 = a
        x2, y2 = b
        D = int(sqrt((x1 - x2) ** 2 + (y1 - y2) ** 2))
        return D

    @staticmethod
    def compute_mean(deg):
        # 对数据进行处理，提取均值
        # new_nums = list(set(deg)) #剔除重复元素
        mean = np.mean(deg)
        var = np.var(deg)
        # print("原始数据共", len(deg), "个\n", deg)
        '''
        for i in range(len(deg)):
            print(deg[i],'→',(deg[i] - mean)/var)
            #另一个思路，先归一化，即标准正态化，再利用3σ原则剔除异常数据，反归一化即可还原数据
        '''
        # print("中位数:",np.median(deg))
        percentile = np.percentile(deg, (25, 50, 75), interpolation='midpoint')
        # print("分位数：", percentile)
        # 以下为箱线图的五个特征值
        Q1 = percentile[0]  # 上四分位数
        Q3 = percentile[2]  # 下四分位数
        IQR = Q3 - Q1  # 四分位距
        ulim = Q3 + 2.5 * IQR  # 上限 非异常范围内的最大值
        llim = Q1 - 1.5 * IQR  # 下限 非异常范围内的最小值

        new_deg = []
        for i in range(len(deg)):
            if (llim < deg[i] and deg[i] < ulim):
                new_deg.append(deg[i])
            # print("清洗后数据共", len(new_deg), "个\n", new_deg)
        new_deg = np.mean(new_deg)
        return new_deg

    @staticmethod
    def get_middle_point(point_one, point_two):
        x = int((point_one[0] + point_two[0]) / 2)
        y = int((point_one[1] + point_two[1]) / 2)
        return [x, y]


def get_image(path):
    """
    读取图片
    """
    img = cv2.imread(path)
    assert img is not None, f"读取图片 {path} 失败, 请确认路径是否正确"
    return img


def tran_hsv(img):
    """
    转化为HSV
    """
    img_hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)
    return img_hsv


def tran_gray(img):
    """
    转化为灰度
    """
    img_gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    return img_gray


def to_camel_case(string, spliter, cap_first=True):
    """
    转换为驼峰形式(默认首字大写)
    """
    components = string.split(spliter)
    # Capitalize the first letter of each word except the first one
    components = [word.lower() if index == 0 and not cap_first else word.capitalize() for index, word in
                  enumerate(components)]
    # Join the components together
    camel_case_string = ''.join(components)
    return camel_case_string


def remove_quietly(file):
    try:
        os.remove(file)
        print(f"{file} removed")
    except OSError:
        pass


def cv2_put_chinese_txt(img, text, position, text_color=(255, 255, 255), bg_color=None, text_size=24,
                        thickness=1):
    if isinstance(img, np.ndarray):  # 判断是否OpenCV图片类型
        img = Image.fromarray(cv2.cvtColor(img, cv2.COLOR_BGR2RGB))
    # 创建一个可以在给定图像上绘图的对象
    draw = ImageDraw.Draw(img)
    # 字体的格式
    font_style = ImageFont.truetype(font_path, text_size, encoding="utf-8")
    text = rebuild_text(img, text, position, draw, font_style, thickness)
    text_width, text_height = draw.textsize(text, font_style, stroke_width=thickness)
    text_x, text_y = position
    if bg_color is not None:
        # 绘制文本背景
        draw.rectangle(((text_x, text_y - text_height), (text_x + text_width, text_y)), fill=bg_color,
                       outline=bg_color)
    # 绘制文本
    draw.text((text_x + 2, text_y - text_height - 2), text, fill=text_color, font=font_style, stroke_width=thickness)
    # 转换回OpenCV格式
    return cv2.cvtColor(np.asarray(img), cv2.COLOR_RGB2BGR)


# 重置字符长度（换行）
def rebuild_text(img, text, position, draw, font_style, thickness):
    img_width, img_height = img.size
    text_width, text_height = draw.textsize(text, font_style, stroke_width=thickness)
    text_x, text_y = position
    text_x_sum = text_x + text_width

    text_x_out = text_x_sum - img_width
    # 计算文字最大宽度（以汉字为准）
    char_width, char_height = draw.textsize("哈", font_style, stroke_width=thickness)
    item_size1 = 1000000000000
    # 文字超出边界
    if text_x_out > 0:
        # 计算边界内最大宽度
        max_width = img_width - text_x
        # 计算边界内最多绘制字符个数
        item_size1 = max_width / char_width
    # 文字超出图像宽度一半
    item_size2 = 1000000000000
    half_img_width = img_width / 2
    if text_width > half_img_width:
        item_size2 = half_img_width / char_width

    # 取小值
    item_size = min(item_size1, item_size2)
    item_size = math.floor(item_size)
    temp = split_list_by_n(list(text), item_size)
    temp_arr = []
    for i in temp:
        temp_arr.append(''.join(i))
    # 每一行之间插入换行符
    text = os.linesep.join(temp_arr)
    return text


def split_list_by_n(list_collection, n):
    """
    将集合均分，每份n个元素
    :param list_collection:
    :param n:
    :return:返回的结果为评分后的每份可迭代对象
    """
    for i in range(0, len(list_collection), n):
        yield list_collection[i: i + n]


def get_hsv_color_str(rgb):
    """
    得到指针颜色，确定其颜色属性
    """
    rgb = np.array(rgb, dtype='uint8').reshape(1, 1, 3)[:, :, ::-1]
    hsv = np.array(cv2.cvtColor(rgb, cv2.COLOR_BGR2HSV)[0][0].tolist())
    lower_hsv = np.greater_equal(hsv, LOWER)
    upper_hsv = np.greater_equal(UPPER, hsv)
    hsv_and = np.logical_and(lower_hsv, upper_hsv)
    hsv_index = np.where(np.all(hsv_and, axis=1))[0][0]
    meter_color = color_list[hsv_index]
    return meter_color, hsv


def get_mask_by_color_(img_hsv, color, color_picker=False):
    """
    根据颜色获取图像遮罩层
    """
    if color_picker:
        color, _ = get_hsv_color_str(color)
        color = ColorSeriesEnum.from_str(color)
    # 去除红颜色范围外的其余颜色
    if color == ColorSeriesEnum.Red:
        mask0 = cv2.inRange(img_hsv, LOWER_RED1, UPPER_RED1)
        mask1 = cv2.inRange(img_hsv, LOWER_RED2, UPPER_RED2)
        mask = mask0 + mask1
    elif color == ColorSeriesEnum.Green:
        mask = cv2.inRange(img_hsv, LOWER_GREEN, UPPER_GREEN)
    elif color == ColorSeriesEnum.Blue:
        mask = cv2.inRange(img_hsv, LOWER_BLUE, UPPER_BLUE)
    elif color == ColorSeriesEnum.Yellow:
        mask = cv2.inRange(img_hsv, LOWER_YELLOW, UPPER_YELLOW)
    elif color == ColorSeriesEnum.Cyan:
        mask = cv2.inRange(img_hsv, LOWER_CYAN, UPPER_CYAN)
    elif color == ColorSeriesEnum.Purple:
        mask = cv2.inRange(img_hsv, LOWER_PURPLE, UPPER_PURPLE)
    elif color == ColorSeriesEnum.Black:
        mask = cv2.inRange(img_hsv, LOWER_BLACK, UPPER_BLACK)
    elif color == ColorSeriesEnum.White:
        mask = cv2.inRange(img_hsv, LOWER_WHITE, UPPER_WHITE)
    elif color == ColorSeriesEnum.Gray:
        mask = cv2.inRange(img_hsv, LOWER_GRAY, UPPER_GRAY)
    else:
        raise AlgorithmProcessException('Unknown ColorSeriesEnum.')
    return mask


def get_mask_by_color(img, color, color_range_thresh=50, color_picker=False):
    '''
    # 颜色空间mask
    if color_picker:
        color, point_hsv = get_hsv_color_str(color)
    print('color is {}'.format(color))
    if color == 'Red':
        hsv_index_list = [0, 1]
    else:
        hsv_index_list = [color_list.index(color)]
    img_hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)
    image_add = []
    for index in hsv_index_list:
        if LOWER[index][0] < point_hsv[0] < UPPER[index][0]:
            low = np.where((point_hsv - color_range_thresh) > LOWER[index], point_hsv - color_range_thresh,
                           LOWER[index])
            up = np.where((point_hsv + color_range_thresh) < UPPER[index], point_hsv + color_range_thresh, UPPER[index])
        else:
            low, up = LOWER[index], UPPER[index]
        # print(point_hsv, LOWER[index], UPPER[index])
        # print(low, up)
        mask_image = cv2.inRange(img_hsv, low, up)
        # cv2.imwrite(os.path.join(self.out_dir, 'mask.jpg'), mask)
        # reval_t, thre = cv2.threshold(mask, 0, 255, cv2.THRESH_BINARY | cv2.THRESH_OTSU)
        # cv2.imwrite('thre.jpg', thre)
        # kernel = np.ones((5, 1), np.uint8)
        # self.edge = cv2.erode(mask, kernel, iterations=1)
        # mask_image = cv2.morphologyEx(mask_image, cv2.MORPH_CLOSE,kernel=np.ones((5,5),np.uint8))
        image_add.append(mask_image)
        # cv2.imwrite(os.path.join(self.out_dir, 'mask_{}.jpg'.format(index)), mask_image)
        if index == 1:
            mask_image = cv2.add(image_add[0], image_add[1])
    # mask_image = cv2.morphologyEx(mask_image,cv2.MORPH_CLOSE,kernel=np.ones((5,5),np.uint8))
    # cv2.imwrite(os.path.join(self.out_dir, 'mask_pre.jpg'), mask_image)
    # mask_image = cv2.dilate(mask_image, kernel=np.ones((2,2), np.uint8), iterations=1)
    # cv2.imwrite(os.path.join(self.out_dir, 'mask.jpg'), mask_image)
    '''
    rgb = np.array(color, dtype='uint8').reshape(1, 1, 3)[:, :, ::-1]
    hsv = np.array(cv2.cvtColor(rgb, cv2.COLOR_BGR2HSV)[0][0])
    img_hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)
    # print(hsv)

    color_range_thresh = np.array([color_range_thresh, color_range_thresh, color_range_thresh])
    # if hsv[2] < 46:
    #     color_range_thresh = np.array([50, 50, 50])
    # elif hsv[1] < 30 and hsv[2] > 221:
    #     color_range_thresh[0] = np.array([50, 15, 15])
    # elif hsv[1] < 43:
    #     color_range_thresh = np.array([50, 20, 50])
    # print(color_range_thresh)
    low = np.where((hsv - color_range_thresh) > np.array([0, 0, 0]), hsv - color_range_thresh, np.array([0, 0, 0]))
    up = np.where((hsv + color_range_thresh) < np.array([180, 255, 255]), hsv + color_range_thresh,
                  np.array([180, 255, 255]))
    # print(low, up)
    mask_image = cv2.inRange(img_hsv, low, up)
    return mask_image


def get_median_val_by_color(img, img_hsv, color):
    """
    获取某个颜色值的中值, 如果不能正确取值则取hsv空间第一个像素点的值
    :img 原始图片
    :img_hsv  img转换为hsv空间的图片
    :color 颜色
    """
    mask = get_mask_by_color(img_hsv, color, color_picker=True)
    # cv2.imwrite('/opt/tjh/model_server/cv_device_img/mask.jpg', mask)
    # .reshape(-1,3)
    median_val = cv2.medianBlur(img, 5)[mask != 0].mean(axis=0)
    return median_val if not np.isnan(median_val) else img_hsv[0, 0, 0]


def get_angle(point1, point2):
    # 以point1为原点，建立直角坐标系，第一象限x轴正方向为0角度，逆时针转动，返回值为0-360区间角度。
    (x1, y1), (x2, y2) = point1, point2
    if y2 - y1 == 0:
        if x2 > x1:
            return 0
        else:
            return 180
    if x2 - x1 == 0:
        if y2 > y1:
            return 270
        else:
            return 90
    k = -(y2 - y1) / (x2 - x1)
    angle = abs(np.arctan(k) * 57.29577)
    if k > 0:
        if x2 < x1:
            angle += 180
    else:
        if x2 > x1:
            angle = 360 - angle
        else:
            angle = 180 - angle
    return angle


def circle2bbox(circle):
    """将 圆转换为 外接bbox坐标"""
    x, y = circle['center']
    radius = circle['radius']

    return [[x - radius, y - radius], [x - radius, y + radius], [x + radius, y + radius], [x + radius, y - radius]]


def render_text_on_bbox(image, bbox, text, bg_color=None, text_color=OSD_FG_COLOR, text_size=24, thickness=2):
    """ 提取bbox的坐标 """
    xmin, ymin, xmax, ymax = bbox
    # 绘制bbox矩形， 颜色= OSD_BG_COLOR
    cv2.rectangle(image, (xmin, ymin), (xmax, ymax), bg_color, thickness)
    image = cv2_put_chinese_txt(image, text, (xmin, ymin), text_color, bg_color, text_size, thickness)

    return image


def render_text_on_position(image, position, text, text_color=OSD_FG_COLOR, bg_color=None, text_size=24,
                            thickness=2):
    """ 绘制带背景色的 text """
    image = cv2_put_chinese_txt(image, text, position, text_color, bg_color, text_size, thickness)
    return image
