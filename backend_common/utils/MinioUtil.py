import os
from pathlib import Path

import requests
from minio import Minio

bucket_name = os.getenv("MINIO_BUCKET", "inspection")
# 文件上传父级目录
dir_of_bucket = os.getenv("MINIO_UPLOAD_DIR", "TEMP")
temp_dir = os.getenv("TEMP_FILE_DIR", "/tmp_file")

minio_endpoint = os.getenv("MINIO_ENDPOINT", "192.168.253.126:18010")
access_key = os.getenv("MINIO_AK", "admin")
secret_key = os.getenv("MINIO_SK", "Qqwe@1234")
# 初始化MinIO客户端
minio_client = Minio(
    minio_endpoint,  # MinIO服务器地址
    access_key,  # 访问密钥
    secret_key,  # 密钥
    secure=False  # 使用HTTPS
)


# 上传本地文件
def upload(file_path: str):
    filename = Path(file_path).name
    # 添加前缀，用来区分Python服务上传的文件
    filename = "py_" + filename
    # 拼接文件父级目录
    filename = dir_of_bucket + "/" + filename
    minio_client.fput_object(
        bucket_name,  # 桶名
        filename,  # 对象名
        file_path  # 本地文件路径
    )

    return "http://" + minio_endpoint + '/' + bucket_name + "/" + filename


# 下载文件到本地（url路径中不能带中文）
def download(url: str):
    try:
        filename = Path(url).name
        response = requests.get(url)
        response.raise_for_status()  # 检查请求是否成功
        # 临时目录如果不存在则创建
        os.makedirs(temp_dir, exist_ok=True)

        filepath = Path(temp_dir, filename).absolute()
        filepath = str(filepath)
        with open(filepath, 'wb') as f:
            f.write(response.content)
        # print(f"文件已保存到: {filepath}")
        return filepath
    except Exception as e:
        print(f"下载失败: {e}")

def save_image(image):
    import datetime
    firename = datetime.datetime.now().strftime('%Y%m%d%H%M%S%f')[:18] + '_result.JPEG'
    # 临时目录如果不存在则创建
    os.makedirs(temp_dir, exist_ok=True)
    filepath = Path(temp_dir, firename).absolute()
    filepath = str(filepath)
    # 保存图片
    import cv2
    cv2.imwrite(filepath, image)
    return filepath

# 返回新的文件名，新名字=文件名和扩展名之间添加suffix
def name_add_suffix(file: str, suffix: str):
    # 原始文件路径
    file_path = Path(file)
    # 提取文件名和扩展名
    name_without_ext = file_path.stem  # "old_name"
    file_extension = file_path.suffix  # ".txt"
    # 新文件名（不修改扩展名）
    new_name = name_without_ext + "_" + suffix + file_extension  # "new_name.txt"
    return str(file_path.parent / new_name)

# def download(file_path: str):
#     # 下载到本地文件
#     minio_client.fget_object(
#         bucket_name,
#         "my-object",
#         "downloaded-file.txt"
#     )
