import cv2
import numpy as np



class NormalizeImage(object):
    """
    归一化图像，例如减去平均值、除标准
    """

    def __init__(self, scale=None, mean=None, std=None, order='hwc', **kwargs):
        if isinstance(scale, str):
            scale = eval(scale)
        self.scale = np.float32(scale if scale is not None else 1.0 / 255.0)
        mean = mean if mean is not None else [0.485, 0.456, 0.406]
        std = std if std is not None else [0.229, 0.224, 0.225]

        shape = (3, 1, 1) if order == 'chw' else (1, 1, 3)
        self.mean = np.array(mean).reshape(shape).astype('float32')
        self.std = np.array(std).reshape(shape).astype('float32')

    def __call__(self, img):
        from PIL import Image
        if isinstance(img, Image.Image):
            img = np.array(img)
        assert isinstance(img, np.ndarray), "invalid input 'img' in NormalizeImage"
        return (img.astype('float32') * self.scale - self.mean) / self.std


class ToCHWImage(object):
    """
    convert hwc image to chw image
    """

    def __init__(self, **kwargs):
        pass

    def __call__(self, img):
        from PIL import Image
        if isinstance(img, Image.Image):
            img = np.array(img)
        #  hwc -> chw
        return img.transpose((2, 0, 1))


class ToHWCImage(object):
    """
    convert chw image to hwc image
    """

    def __init__(self, **kwargs):
        pass

    def __call__(self, img):
        from PIL import Image
        if isinstance(img, Image.Image):
            img = np.array(img)
        #  chw -> hwc
        return img.transpose((1, 2, 0))


def resize_ratio(img, target_size):
    """ resize_ratio 按短边比例缩放"""
    percent = float(target_size) / min(img.shape[0], img.shape[1])
    resized_width = int(round(img.shape[1] * percent))
    resized_height = int(round(img.shape[0] * percent))
    resized = cv2.resize(img, (resized_width, resized_height))
    return resized


def padding_2square(img, target_size):
    """ resize_2square 按长边缩放"""
    # 获取输入图像的高度和宽度
    h, w = img.shape[:2]
    # 计算缩放比例
    ratio = min(target_size / w, target_size / h)
    # 计算缩放后的图像大小
    new_size = (int(w * ratio), int(h * ratio))
    # 使用OpenCV进行图像缩放
    resized_image = cv2.resize(img, new_size, interpolation=cv2.INTER_CUBIC)

    # 创建一个空白的正方形图像
    square_image = np.zeros((target_size, target_size, 3), np.uint8)

    # 计算需要将缩放后的图像放置在正方形图像中的位置
    x_offset = int((target_size - new_size[0]) / 2)
    y_offset = int((target_size - new_size[1]) / 2)

    # 将缩放后的图像放置在正方形图像中
    square_image[y_offset:y_offset + new_size[1], x_offset:x_offset + new_size[0]] = resized_image

    return square_image


def crop_image(img, target_size, center):
    """ crop_image 图片裁剪"""
    height, width = img.shape[:2]
    size = target_size
    if center:
        w_start = (width - size) / 2
        h_start = (height - size) / 2
    else:
        w_start = np.random.randint(0, width - size + 1)
        h_start = np.random.randint(0, height - size + 1)
    w_end = w_start + size
    h_end = h_start + size
    img = img[int(h_start):int(h_end), int(w_start):int(w_end), :]
    return img
