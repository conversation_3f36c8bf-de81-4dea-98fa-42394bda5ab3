class UnicornException(Exception):
    def __init__(self, message: str, code: int = -1):
        self.message = message
        self.code = code


class AlgorithmCheckException(Exception):
    def __init__(self, message: str, code: int = -1):
        self.message = message
        self.code = code


class AlgorithmProcessException(Exception):
    def __init__(self, message: str, code: int = -1):
        self.message = message
        self.code = code


class InspectionException(Exception):
    def __init__(self, message: str, code: int = -1):
        self.message = message
        self.code = code