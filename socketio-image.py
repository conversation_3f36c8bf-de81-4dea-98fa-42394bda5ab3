import asyncio
from fastapi import FastAPI, WebSocket
from PIL import Image
import io
import time
import cv2


import asyncio
from collections import deque

import threading
from collections import deque


class SyncBoundedQueue:
    def __init__(self, maxsize: int):
        self.maxsize = maxsize
        self.queue = deque(maxlen=maxsize)  # 自动满时替换最左侧（最老）元素
        self.lock = threading.Lock()  # 线程安全锁
        self.not_empty = threading.Condition(self.lock)  # 条件变量，用于同步等待/通知

    def put(self, item):
        """添加元素（满时替换最老的）"""
        with self.lock:
            self.queue.append(item)  # 满⾃动替换最左侧元素
            self.not_empty.notify()  # 通知等待获取的线程

    def get(self):
        """获取元素（无数据时阻塞等待）"""
        with self.lock:
            while not self.queue:  # 队列为空时循环等待
                self.not_empty.wait()  # 释放锁并阻塞，直到被 notify 唤醒
            return self.queue.popleft()  # 返回最左侧（最老）元素


app = FastAPI()
# 异步队列：存储生成的图像帧（二进制）
frame_queue = SyncBoundedQueue(maxsize=20)

rtspUrl= 'rtsp://admin:Qqwe1234@192.168.1.65/Streaming/Channels/101?transportmode=unicast&profile=Profile_1'

cap = cv2.VideoCapture(rtspUrl)
cap.set(cv2.CAP_PROP_BUFFERSIZE,0);

# 同步生成器：使用OpenCV读取摄像头帧（替换为真实业务逻辑）
def generate_frames():
    while True:
        ret, frame = cap.read()  # 读取一帧
        if not ret:  # 处理读取失败情况（如摄像头断开）
            print("[Backend] 无法读取摄像头帧，等待重试...")
            time.sleep(0.1)  # 等待0.1秒后重试
            continue

        # 将OpenCV的BGR格式转换为PIL需要的RGB格式
        frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)

        # 将NumPy数组转换为PIL图像
        img = Image.fromarray(frame_rgb)

        # 压缩为JPEG格式（降低带宽占用）
        buffer = io.BytesIO()
        img.save(buffer, format='JPEG', quality=70)
        frame_data = buffer.getvalue()

        # 尝试将帧存入队列（若队列已满，丢弃旧帧）
        try:
             frame_queue.put(frame_data)
            # frame_queue.put(frame_data)
        except asyncio.QueueFull:
            # 队列满时，丢弃最新帧（保留旧帧），避免阻塞生成器
            print("[Backend] frame_queue已满，丢弃最新帧...")
            pass


# 异步启动生成器任务
async def start_generator():
    loop = asyncio.get_event_loop()
    # 直接在线程池中运行同步生成器（避免阻塞事件循环）
    loop.run_in_executor(None, generate_frames)


# WebSocket 路由：向客户端推送队列中的帧
@app.websocket("/ws/video")
async def video_stream(websocket: WebSocket):
    await websocket.accept()
    try:
        # 仅第一次连接时启动生成器（避免重复启动）
        if not getattr(start_generator, "started", False):
            await start_generator()
            start_generator.started = True

        while True:
            frame = frame_queue.get()  # 从队列取帧（阻塞直到有新帧）
            await websocket.send_bytes(frame)  # 发送二进制帧
    except Exception as e:
        print(f"[Backend] WebSocket Error: {e}")
    finally:
        await websocket.close()


if __name__ == "__main__":
    import uvicorn

    # TODO cap 断线重连
    uvicorn.run(app, host="0.0.0.0", port=5000)
